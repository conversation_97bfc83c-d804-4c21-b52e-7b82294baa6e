2025-08-10 00:40:53.035 INFO log_util.__aenter__ e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file start...
2025-08-10 00:40:53.036 INFO middleware_util.custom_route_handler e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file body={"content":"搜索结果已保存，包含理想汽车的负面新闻和争议事件。","description":"整理关于理想汽车的负面新闻内容","fileName":"理想汽车负面新闻.md","requestId":"geniesession-1754757551055-129:1754757612700-5946"}
2025-08-10 00:40:53.038 INFO log_util.__aenter__ e680ec57-22e1-485b-ba4f-667debb5a95c  add_by_content start...
2025-08-10 00:40:53.040 ERROR log_util.__aexit__ e680ec57-22e1-485b-ba4f-667debb5a95c  add_by_content error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniesession-1754757551055-129:1754757612700-5946'

2025-08-10 00:40:53.050 ERROR middleware_util.dispatch e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniesession-1754757551055-129:1754757612700-5946'

2025-08-10 00:40:53.051 INFO log_util.__aexit__ e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file cost=[15 ms]
2025-08-10 00:41:02.725 INFO log_util.__aenter__ 154404a5-8ed4-4daa-959d-da11c7f24a22 POST /v1/tool/report start...
2025-08-10 00:41:02.726 INFO middleware_util.custom_route_handler 154404a5-8ed4-4daa-959d-da11c7f24a22 POST /v1/tool/report body={"contentStream":true,"fileDescription":"关于理想汽车的负面新闻整理报告","fileName":"理想汽车负面新闻报告.html","fileNames":[],"fileType":"html","query":"帮我查找一下有关 理想汽车 有关的负面新闻","requestId":"geniesession-1754757551055-129:1754757612700-5946","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并分析关于理想汽车的负面新闻，包括争议事件、用户投诉等内容，形成一份详细的HTML报告。"}
2025-08-10 00:41:02.730 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter report start...
2025-08-10 00:41:02.730 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter report cost=[0 ms]
2025-08-10 00:41:02.730 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter html_report start...
2025-08-10 00:41:02.731 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter html_report cost=[1 ms]
2025-08-10 00:41:02.731 INFO log_util.__aenter__ 154404a5-8ed4-4daa-959d-da11c7f24a22  download_all_files start...
2025-08-10 00:41:02.731 INFO log_util.__aexit__ 154404a5-8ed4-4daa-959d-da11c7f24a22  download_all_files cost=[0 ms]
2025-08-10 00:41:02.732 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files start...
2025-08-10 00:41:02.732 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files cost=[0 ms]
2025-08-10 00:41:02.732 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files start...
2025-08-10 00:41:02.733 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files cost=[0 ms]
2025-08-10 00:41:02.735 INFO log_util.__aexit__ 154404a5-8ed4-4daa-959d-da11c7f24a22 POST /v1/tool/report cost=[9 ms]
2025-08-10 00:41:11.511 INFO log_util.__aenter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c POST /v1/tool/report start...
2025-08-10 00:41:11.512 INFO middleware_util.custom_route_handler c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c POST /v1/tool/report body={"contentStream":true,"fileDescription":"关于理想汽车的负面新闻整理报告","fileName":"理想汽车负面新闻报告.md","fileNames":[],"fileType":"markdown","query":"帮我查找一下有关 理想汽车 有关的负面新闻","requestId":"geniesession-1754757551055-129:1754757612700-5946","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并分析关于理想汽车的负面新闻，包括争议事件、用户投诉等内容，形成一份详细的Markdown报告。"}
2025-08-10 00:41:11.513 INFO log_util.__enter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter report start...
2025-08-10 00:41:11.514 INFO log_util.__exit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter report cost=[0 ms]
2025-08-10 00:41:11.514 INFO log_util.__enter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter markdown_report start...
2025-08-10 00:41:11.515 INFO log_util.__exit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter markdown_report cost=[1 ms]
2025-08-10 00:41:11.515 INFO log_util.__aenter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  download_all_files start...
2025-08-10 00:41:11.515 INFO log_util.__aexit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  download_all_files cost=[0 ms]
2025-08-10 00:41:11.516 INFO log_util.__enter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  truncate_files start...
2025-08-10 00:41:11.517 INFO log_util.__exit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  truncate_files cost=[0 ms]
2025-08-10 00:41:11.518 INFO log_util.__aexit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c POST /v1/tool/report cost=[7 ms]
2025-08-10 00:41:16.788 INFO log_util.__aenter__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8 POST /v1/tool/deepsearch start...
2025-08-10 00:41:16.791 INFO middleware_util.custom_route_handler 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"理想汽车负面新闻 2025年","request_id":"geniesession-1754757551055-129:1754757612700-5946:n48zq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:41:16.794 INFO log_util.__enter__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  run start...
2025-08-10 00:41:16.795 INFO log_util.__exit__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  run cost=[0 ms]
2025-08-10 00:41:16.796 INFO deepsearch.run geniesession-1754757551055-129:1754757612700-5946:n48zq 第 1 轮深度搜索...
2025-08-10 00:41:16.796 INFO log_util.__aenter__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  query_decompose start...
2025-08-10 00:41:16.799 ERROR log_util.__aexit__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:41:16.800 INFO log_util.__aexit__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 00:43:14.920 INFO log_util.__aenter__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b POST /v1/tool/deepsearch start...
2025-08-10 00:43:14.922 INFO middleware_util.custom_route_handler 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅游攻略","request_id":"geniesession-1754757551055-129:1754757776162-4267:8c3aq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:14.925 INFO log_util.__enter__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  run start...
2025-08-10 00:43:14.925 INFO log_util.__exit__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  run cost=[0 ms]
2025-08-10 00:43:14.926 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:8c3aq 第 1 轮深度搜索...
2025-08-10 00:43:14.926 INFO log_util.__aenter__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  query_decompose start...
2025-08-10 00:43:14.930 ERROR log_util.__aexit__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:14.931 INFO log_util.__aexit__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 00:43:24.324 INFO log_util.__aenter__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1 POST /v1/tool/deepsearch start...
2025-08-10 00:43:24.325 INFO log_util.__aenter__ 7d8bb099-2077-4d25-b886-162d749dd4a4 POST /v1/tool/deepsearch start...
2025-08-10 00:43:24.327 INFO middleware_util.custom_route_handler 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛航班信息","request_id":"geniesession-1754757551055-129:1754757776162-4267:q6eks","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:24.329 INFO middleware_util.custom_route_handler 7d8bb099-2077-4d25-b886-162d749dd4a4 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游景点推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:xpghw","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:24.329 INFO log_util.__enter__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  run start...
2025-08-10 00:43:24.330 INFO log_util.__exit__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  run cost=[1 ms]
2025-08-10 00:43:24.331 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:q6eks 第 1 轮深度搜索...
2025-08-10 00:43:24.332 INFO log_util.__aenter__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  query_decompose start...
2025-08-10 00:43:24.335 ERROR log_util.__aexit__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:24.337 INFO log_util.__enter__ 7d8bb099-2077-4d25-b886-162d749dd4a4  run start...
2025-08-10 00:43:24.338 INFO log_util.__exit__ 7d8bb099-2077-4d25-b886-162d749dd4a4  run cost=[1 ms]
2025-08-10 00:43:24.339 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:xpghw 第 1 轮深度搜索...
2025-08-10 00:43:24.341 INFO log_util.__aenter__ 7d8bb099-2077-4d25-b886-162d749dd4a4  query_decompose start...
2025-08-10 00:43:24.343 ERROR log_util.__aexit__ 7d8bb099-2077-4d25-b886-162d749dd4a4  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:24.345 INFO log_util.__aexit__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1 POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:43:24.347 INFO log_util.__aexit__ 7d8bb099-2077-4d25-b886-162d749dd4a4 POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:43:32.897 INFO log_util.__aenter__ 3254fc2b-397f-4747-8d88-e6998df0a32f POST /v1/tool/deepsearch start...
2025-08-10 00:43:32.898 INFO log_util.__aenter__ 4a8ece0e-a361-451d-a535-9efe01afbcdd POST /v1/tool/deepsearch start...
2025-08-10 00:43:32.901 INFO middleware_util.custom_route_handler 3254fc2b-397f-4747-8d88-e6998df0a32f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海飞巴厘岛旅行指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:kifgj","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:32.902 INFO middleware_util.custom_route_handler 4a8ece0e-a361-451d-a535-9efe01afbcdd POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛自由行攻略","request_id":"geniesession-1754757551055-129:1754757776162-4267:9co8q","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:32.904 INFO log_util.__enter__ 3254fc2b-397f-4747-8d88-e6998df0a32f  run start...
2025-08-10 00:43:32.905 INFO log_util.__exit__ 3254fc2b-397f-4747-8d88-e6998df0a32f  run cost=[1 ms]
2025-08-10 00:43:32.908 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:kifgj 第 1 轮深度搜索...
2025-08-10 00:43:32.909 INFO log_util.__aenter__ 3254fc2b-397f-4747-8d88-e6998df0a32f  query_decompose start...
2025-08-10 00:43:32.912 ERROR log_util.__aexit__ 3254fc2b-397f-4747-8d88-e6998df0a32f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:32.914 INFO log_util.__enter__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  run start...
2025-08-10 00:43:32.915 INFO log_util.__exit__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  run cost=[0 ms]
2025-08-10 00:43:32.918 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:9co8q 第 1 轮深度搜索...
2025-08-10 00:43:32.918 INFO log_util.__aenter__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  query_decompose start...
2025-08-10 00:43:32.924 ERROR log_util.__aexit__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:32.927 INFO log_util.__aexit__ 3254fc2b-397f-4747-8d88-e6998df0a32f POST /v1/tool/deepsearch cost=[29 ms]
2025-08-10 00:43:32.931 INFO log_util.__aexit__ 4a8ece0e-a361-451d-a535-9efe01afbcdd POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:43:39.952 INFO log_util.__aenter__ 64427b48-b277-423e-8093-4ec3326cae99 POST /v1/tool/deepsearch start...
2025-08-10 00:43:39.958 INFO middleware_util.custom_route_handler 64427b48-b277-423e-8093-4ec3326cae99 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛自由行详细攻略","request_id":"geniesession-1754757551055-129:1754757776162-4267:5gjim","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:39.962 INFO log_util.__enter__ 64427b48-b277-423e-8093-4ec3326cae99  run start...
2025-08-10 00:43:39.967 INFO log_util.__exit__ 64427b48-b277-423e-8093-4ec3326cae99  run cost=[5 ms]
2025-08-10 00:43:39.969 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:5gjim 第 1 轮深度搜索...
2025-08-10 00:43:39.974 INFO log_util.__aenter__ 64427b48-b277-423e-8093-4ec3326cae99  query_decompose start...
2025-08-10 00:43:39.979 ERROR log_util.__aexit__ 64427b48-b277-423e-8093-4ec3326cae99  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:39.983 INFO log_util.__aexit__ 64427b48-b277-423e-8093-4ec3326cae99 POST /v1/tool/deepsearch cost=[30 ms]
2025-08-10 00:43:48.570 INFO log_util.__aenter__ c8eb451e-2bcc-422a-9d20-c630a94035cb POST /v1/tool/deepsearch start...
2025-08-10 00:43:48.571 INFO middleware_util.custom_route_handler c8eb451e-2bcc-422a-9d20-c630a94035cb POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游必去景点","request_id":"geniesession-1754757551055-129:1754757776162-4267:kslue","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:48.572 INFO log_util.__enter__ c8eb451e-2bcc-422a-9d20-c630a94035cb  run start...
2025-08-10 00:43:48.572 INFO log_util.__exit__ c8eb451e-2bcc-422a-9d20-c630a94035cb  run cost=[0 ms]
2025-08-10 00:43:48.572 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:kslue 第 1 轮深度搜索...
2025-08-10 00:43:48.572 INFO log_util.__aenter__ c8eb451e-2bcc-422a-9d20-c630a94035cb  query_decompose start...
2025-08-10 00:43:48.574 ERROR log_util.__aexit__ c8eb451e-2bcc-422a-9d20-c630a94035cb  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:48.574 INFO log_util.__aexit__ c8eb451e-2bcc-422a-9d20-c630a94035cb POST /v1/tool/deepsearch cost=[4 ms]
2025-08-10 00:43:48.575 INFO log_util.__aenter__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b POST /v1/tool/deepsearch start...
2025-08-10 00:43:48.576 INFO middleware_util.custom_route_handler 3e5d05ec-3b54-48ab-a22c-997af447bb2b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛交通方式","request_id":"geniesession-1754757551055-129:1754757776162-4267:4iiv6","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:48.579 INFO log_util.__enter__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  run start...
2025-08-10 00:43:48.580 INFO log_util.__exit__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  run cost=[0 ms]
2025-08-10 00:43:48.580 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:4iiv6 第 1 轮深度搜索...
2025-08-10 00:43:48.580 INFO log_util.__aenter__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  query_decompose start...
2025-08-10 00:43:48.581 ERROR log_util.__aexit__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:48.581 INFO log_util.__aexit__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b POST /v1/tool/deepsearch cost=[6 ms]
2025-08-10 00:43:57.943 INFO log_util.__aenter__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011 POST /v1/tool/deepsearch start...
2025-08-10 00:43:57.944 INFO log_util.__aenter__ e3139530-f419-4548-b244-2c74991df845 POST /v1/tool/deepsearch start...
2025-08-10 00:43:57.945 INFO middleware_util.custom_route_handler 9b9ab384-dda4-4bab-9cb4-434b7a52a011 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛最佳旅行季节","request_id":"geniesession-1754757551055-129:1754757776162-4267:z6g2i","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:57.945 INFO middleware_util.custom_route_handler e3139530-f419-4548-b244-2c74991df845 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游签证要求","request_id":"geniesession-1754757551055-129:1754757776162-4267:plla3","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:57.946 INFO log_util.__enter__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  run start...
2025-08-10 00:43:57.946 INFO log_util.__exit__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  run cost=[0 ms]
2025-08-10 00:43:57.946 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:z6g2i 第 1 轮深度搜索...
2025-08-10 00:43:57.946 INFO log_util.__aenter__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  query_decompose start...
2025-08-10 00:43:57.947 ERROR log_util.__aexit__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:57.948 INFO log_util.__enter__ e3139530-f419-4548-b244-2c74991df845  run start...
2025-08-10 00:43:57.948 INFO log_util.__exit__ e3139530-f419-4548-b244-2c74991df845  run cost=[0 ms]
2025-08-10 00:43:57.948 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:plla3 第 1 轮深度搜索...
2025-08-10 00:43:57.948 INFO log_util.__aenter__ e3139530-f419-4548-b244-2c74991df845  query_decompose start...
2025-08-10 00:43:57.949 ERROR log_util.__aexit__ e3139530-f419-4548-b244-2c74991df845  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:57.949 INFO log_util.__aexit__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-10 00:43:57.951 INFO log_util.__aexit__ e3139530-f419-4548-b244-2c74991df845 POST /v1/tool/deepsearch cost=[6 ms]
2025-08-10 00:44:05.346 INFO log_util.__aenter__ e6cd3884-a6e7-4706-b667-608eba324db7 POST /v1/tool/deepsearch start...
2025-08-10 00:44:05.347 INFO middleware_util.custom_route_handler e6cd3884-a6e7-4706-b667-608eba324db7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛自由行攻略 2025","request_id":"geniesession-1754757551055-129:1754757776162-4267:n9e3d","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:05.347 INFO log_util.__enter__ e6cd3884-a6e7-4706-b667-608eba324db7  run start...
2025-08-10 00:44:05.347 INFO log_util.__exit__ e6cd3884-a6e7-4706-b667-608eba324db7  run cost=[0 ms]
2025-08-10 00:44:05.348 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:n9e3d 第 1 轮深度搜索...
2025-08-10 00:44:05.348 INFO log_util.__aenter__ e6cd3884-a6e7-4706-b667-608eba324db7  query_decompose start...
2025-08-10 00:44:05.349 ERROR log_util.__aexit__ e6cd3884-a6e7-4706-b667-608eba324db7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:05.349 INFO log_util.__aexit__ e6cd3884-a6e7-4706-b667-608eba324db7 POST /v1/tool/deepsearch cost=[2 ms]
2025-08-10 00:44:13.563 INFO log_util.__aenter__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc POST /v1/tool/deepsearch start...
2025-08-10 00:44:13.564 INFO middleware_util.custom_route_handler 7287f9e2-0646-45a7-99b2-1c3f56c941fc POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛航班及住宿推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:dt0eb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:13.564 INFO log_util.__aenter__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3 POST /v1/tool/deepsearch start...
2025-08-10 00:44:13.565 INFO log_util.__enter__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  run start...
2025-08-10 00:44:13.565 INFO log_util.__exit__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  run cost=[0 ms]
2025-08-10 00:44:13.565 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:dt0eb 第 1 轮深度搜索...
2025-08-10 00:44:13.565 INFO log_util.__aenter__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  query_decompose start...
2025-08-10 00:44:13.566 ERROR log_util.__aexit__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:13.566 INFO log_util.__aexit__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc POST /v1/tool/deepsearch cost=[3 ms]
2025-08-10 00:44:13.567 INFO middleware_util.custom_route_handler 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游注意事项","request_id":"geniesession-1754757551055-129:1754757776162-4267:lrb6s","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:13.568 INFO log_util.__enter__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  run start...
2025-08-10 00:44:13.568 INFO log_util.__exit__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  run cost=[0 ms]
2025-08-10 00:44:13.569 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:lrb6s 第 1 轮深度搜索...
2025-08-10 00:44:13.569 INFO log_util.__aenter__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  query_decompose start...
2025-08-10 00:44:13.570 ERROR log_util.__aexit__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:13.574 INFO log_util.__aexit__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 00:44:22.788 INFO log_util.__aenter__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5 POST /v1/tool/deepsearch start...
2025-08-10 00:44:22.789 INFO log_util.__aenter__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9 POST /v1/tool/deepsearch start...
2025-08-10 00:44:22.789 INFO middleware_util.custom_route_handler 4487d75d-e6b8-453d-a2e3-9c24f46441e5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海飞巴厘岛旅行贴士","request_id":"geniesession-1754757551055-129:1754757776162-4267:w874r","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:22.790 INFO middleware_util.custom_route_handler 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛自由行行程规划","request_id":"geniesession-1754757551055-129:1754757776162-4267:0ldpu","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:22.790 INFO log_util.__enter__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  run start...
2025-08-10 00:44:22.790 INFO log_util.__exit__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  run cost=[0 ms]
2025-08-10 00:44:22.790 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:w874r 第 1 轮深度搜索...
2025-08-10 00:44:22.791 INFO log_util.__aenter__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  query_decompose start...
2025-08-10 00:44:22.792 ERROR log_util.__aexit__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:22.792 INFO log_util.__enter__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  run start...
2025-08-10 00:44:22.792 INFO log_util.__exit__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  run cost=[0 ms]
2025-08-10 00:44:22.792 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:0ldpu 第 1 轮深度搜索...
2025-08-10 00:44:22.792 INFO log_util.__aenter__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  query_decompose start...
2025-08-10 00:44:22.793 ERROR log_util.__aexit__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:22.794 INFO log_util.__aexit__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5 POST /v1/tool/deepsearch cost=[6 ms]
2025-08-10 00:44:22.794 INFO log_util.__aexit__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-10 00:44:31.934 INFO log_util.__aenter__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2 POST /v1/tool/deepsearch start...
2025-08-10 00:44:31.934 INFO log_util.__aenter__ 96033014-87f2-44e4-a714-c4617201b7f2 POST /v1/tool/deepsearch start...
2025-08-10 00:44:31.935 INFO middleware_util.custom_route_handler 41e9b646-c24b-4d13-9a8b-478c445b2ea2 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛机票预订指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:pc2ta","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:31.936 INFO middleware_util.custom_route_handler 96033014-87f2-44e4-a714-c4617201b7f2 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛热门景点介绍","request_id":"geniesession-1754757551055-129:1754757776162-4267:bsz98","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:31.937 INFO log_util.__enter__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  run start...
2025-08-10 00:44:31.937 INFO log_util.__exit__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  run cost=[0 ms]
2025-08-10 00:44:31.937 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:pc2ta 第 1 轮深度搜索...
2025-08-10 00:44:31.937 INFO log_util.__aenter__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  query_decompose start...
2025-08-10 00:44:31.939 ERROR log_util.__aexit__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:31.939 INFO log_util.__enter__ 96033014-87f2-44e4-a714-c4617201b7f2  run start...
2025-08-10 00:44:31.939 INFO log_util.__exit__ 96033014-87f2-44e4-a714-c4617201b7f2  run cost=[0 ms]
2025-08-10 00:44:31.939 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:bsz98 第 1 轮深度搜索...
2025-08-10 00:44:31.941 INFO log_util.__aenter__ 96033014-87f2-44e4-a714-c4617201b7f2  query_decompose start...
2025-08-10 00:44:31.943 ERROR log_util.__aexit__ 96033014-87f2-44e4-a714-c4617201b7f2  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:31.944 INFO log_util.__aexit__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 00:44:31.945 INFO log_util.__aexit__ 96033014-87f2-44e4-a714-c4617201b7f2 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 00:44:43.052 INFO log_util.__aenter__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8 POST /v1/tool/deepsearch start...
2025-08-10 00:44:43.055 INFO log_util.__aenter__ c3cea461-d8cf-4a82-aac8-04a75e368ccf POST /v1/tool/deepsearch start...
2025-08-10 00:44:43.062 INFO middleware_util.custom_route_handler 9618064b-f7d5-4459-aeed-32dd1b62b3a8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游美食推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:vqn64","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:43.064 INFO middleware_util.custom_route_handler c3cea461-d8cf-4a82-aac8-04a75e368ccf POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行必备物品","request_id":"geniesession-1754757551055-129:1754757776162-4267:i3npb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:43.071 INFO log_util.__enter__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  run start...
2025-08-10 00:44:43.077 INFO log_util.__exit__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  run cost=[6 ms]
2025-08-10 00:44:43.081 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vqn64 第 1 轮深度搜索...
2025-08-10 00:44:43.083 INFO log_util.__aenter__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  query_decompose start...
2025-08-10 00:44:43.093 ERROR log_util.__aexit__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:43.097 INFO log_util.__enter__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  run start...
2025-08-10 00:44:43.100 INFO log_util.__exit__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  run cost=[2 ms]
2025-08-10 00:44:43.107 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:i3npb 第 1 轮深度搜索...
2025-08-10 00:44:43.110 INFO log_util.__aenter__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  query_decompose start...
2025-08-10 00:44:43.116 ERROR log_util.__aexit__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:43.123 INFO log_util.__aexit__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8 POST /v1/tool/deepsearch cost=[70 ms]
2025-08-10 00:44:43.131 INFO log_util.__aexit__ c3cea461-d8cf-4a82-aac8-04a75e368ccf POST /v1/tool/deepsearch cost=[75 ms]
2025-08-10 00:44:49.955 INFO log_util.__aenter__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd POST /v1/tool/deepsearch start...
2025-08-10 00:44:49.959 INFO log_util.__aenter__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c POST /v1/tool/deepsearch start...
2025-08-10 00:44:49.963 INFO middleware_util.custom_route_handler 5f9a6841-9a3c-4a11-a57e-859795df5bbd POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行预算","request_id":"geniesession-1754757551055-129:1754757776162-4267:u06a0","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:49.966 INFO middleware_util.custom_route_handler 3ce02035-aa78-4e0b-a094-f84d5fe5a51c POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游文化体验","request_id":"geniesession-1754757551055-129:1754757776162-4267:794gs","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:49.969 INFO log_util.__enter__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  run start...
2025-08-10 00:44:49.973 INFO log_util.__exit__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  run cost=[4 ms]
2025-08-10 00:44:49.979 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:u06a0 第 1 轮深度搜索...
2025-08-10 00:44:49.983 INFO log_util.__aenter__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  query_decompose start...
2025-08-10 00:44:49.995 ERROR log_util.__aexit__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:49.999 INFO log_util.__enter__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  run start...
2025-08-10 00:44:50.007 INFO log_util.__exit__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  run cost=[7 ms]
2025-08-10 00:44:50.014 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:794gs 第 1 轮深度搜索...
2025-08-10 00:44:50.021 INFO log_util.__aenter__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  query_decompose start...
2025-08-10 00:44:50.032 ERROR log_util.__aexit__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:50.038 INFO log_util.__aexit__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd POST /v1/tool/deepsearch cost=[82 ms]
2025-08-10 00:44:50.048 INFO log_util.__aexit__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c POST /v1/tool/deepsearch cost=[89 ms]
2025-08-10 00:44:56.436 INFO log_util.__aenter__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b POST /v1/tool/deepsearch start...
2025-08-10 00:44:56.438 INFO log_util.__aenter__ 3836e4e6-452e-499b-b304-940db5e41a9f POST /v1/tool/deepsearch start...
2025-08-10 00:44:56.441 INFO middleware_util.custom_route_handler 6bbd9269-4cc8-4810-b25c-c2aab8efe18b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游安全注意事项","request_id":"geniesession-1754757551055-129:1754757776162-4267:4v992","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:56.441 INFO middleware_util.custom_route_handler 3836e4e6-452e-499b-b304-940db5e41a9f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行常见问题","request_id":"geniesession-1754757551055-129:1754757776162-4267:92fhg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:56.443 INFO log_util.__enter__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  run start...
2025-08-10 00:44:56.446 INFO log_util.__exit__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  run cost=[2 ms]
2025-08-10 00:44:56.447 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:4v992 第 1 轮深度搜索...
2025-08-10 00:44:56.447 INFO log_util.__aenter__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  query_decompose start...
2025-08-10 00:44:56.455 ERROR log_util.__aexit__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:56.458 INFO log_util.__enter__ 3836e4e6-452e-499b-b304-940db5e41a9f  run start...
2025-08-10 00:44:56.459 INFO log_util.__exit__ 3836e4e6-452e-499b-b304-940db5e41a9f  run cost=[0 ms]
2025-08-10 00:44:56.460 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:92fhg 第 1 轮深度搜索...
2025-08-10 00:44:56.460 INFO log_util.__aenter__ 3836e4e6-452e-499b-b304-940db5e41a9f  query_decompose start...
2025-08-10 00:44:56.465 ERROR log_util.__aexit__ 3836e4e6-452e-499b-b304-940db5e41a9f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:56.467 INFO log_util.__aexit__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b POST /v1/tool/deepsearch cost=[30 ms]
2025-08-10 00:44:56.471 INFO log_util.__aexit__ 3836e4e6-452e-499b-b304-940db5e41a9f POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:45:02.951 INFO log_util.__aenter__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a POST /v1/tool/deepsearch start...
2025-08-10 00:45:02.952 INFO log_util.__aenter__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f POST /v1/tool/deepsearch start...
2025-08-10 00:45:02.954 INFO middleware_util.custom_route_handler 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游购物指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:vyf49","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:02.955 INFO middleware_util.custom_route_handler 636f6ac9-00a6-47ae-9201-8f7b51babc9f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行保险推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:jnxce","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:02.957 INFO log_util.__enter__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  run start...
2025-08-10 00:45:02.957 INFO log_util.__exit__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  run cost=[0 ms]
2025-08-10 00:45:02.958 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vyf49 第 1 轮深度搜索...
2025-08-10 00:45:02.958 INFO log_util.__aenter__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  query_decompose start...
2025-08-10 00:45:02.963 ERROR log_util.__aexit__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:02.964 INFO log_util.__enter__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  run start...
2025-08-10 00:45:02.964 INFO log_util.__exit__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  run cost=[0 ms]
2025-08-10 00:45:02.965 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:jnxce 第 1 轮深度搜索...
2025-08-10 00:45:02.966 INFO log_util.__aenter__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  query_decompose start...
2025-08-10 00:45:02.968 ERROR log_util.__aexit__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:02.972 INFO log_util.__aexit__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:45:02.973 INFO log_util.__aexit__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:45:11.247 INFO log_util.__aenter__ 6a54b467-7187-4252-99b8-5573f1e44fae POST /v1/tool/deepsearch start...
2025-08-10 00:45:11.249 INFO log_util.__aenter__ c565b832-52ac-44f2-8410-e7191967452c POST /v1/tool/deepsearch start...
2025-08-10 00:45:11.253 INFO middleware_util.custom_route_handler 6a54b467-7187-4252-99b8-5573f1e44fae POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游住宿推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:c7w6b","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:11.258 INFO middleware_util.custom_route_handler c565b832-52ac-44f2-8410-e7191967452c POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行季节选择","request_id":"geniesession-1754757551055-129:1754757776162-4267:nmr77","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:11.265 INFO log_util.__enter__ 6a54b467-7187-4252-99b8-5573f1e44fae  run start...
2025-08-10 00:45:11.271 INFO log_util.__exit__ 6a54b467-7187-4252-99b8-5573f1e44fae  run cost=[5 ms]
2025-08-10 00:45:11.277 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:c7w6b 第 1 轮深度搜索...
2025-08-10 00:45:11.280 INFO log_util.__aenter__ 6a54b467-7187-4252-99b8-5573f1e44fae  query_decompose start...
2025-08-10 00:45:11.286 ERROR log_util.__aexit__ 6a54b467-7187-4252-99b8-5573f1e44fae  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:11.292 INFO log_util.__enter__ c565b832-52ac-44f2-8410-e7191967452c  run start...
2025-08-10 00:45:11.295 INFO log_util.__exit__ c565b832-52ac-44f2-8410-e7191967452c  run cost=[3 ms]
2025-08-10 00:45:11.296 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:nmr77 第 1 轮深度搜索...
2025-08-10 00:45:11.300 INFO log_util.__aenter__ c565b832-52ac-44f2-8410-e7191967452c  query_decompose start...
2025-08-10 00:45:11.305 ERROR log_util.__aexit__ c565b832-52ac-44f2-8410-e7191967452c  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:11.309 INFO log_util.__aexit__ 6a54b467-7187-4252-99b8-5573f1e44fae POST /v1/tool/deepsearch cost=[62 ms]
2025-08-10 00:45:11.317 INFO log_util.__aexit__ c565b832-52ac-44f2-8410-e7191967452c POST /v1/tool/deepsearch cost=[68 ms]
2025-08-10 00:45:17.091 INFO log_util.__aenter__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f POST /v1/tool/deepsearch start...
2025-08-10 00:45:17.091 INFO log_util.__aenter__ 0a084bde-521f-4d07-be8e-d11977f52e0b POST /v1/tool/deepsearch start...
2025-08-10 00:45:17.093 INFO middleware_util.custom_route_handler a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游交通指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:mk3ek","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:17.095 INFO middleware_util.custom_route_handler 0a084bde-521f-4d07-be8e-d11977f52e0b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行签证办理","request_id":"geniesession-1754757551055-129:1754757776162-4267:6f472","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:17.095 INFO log_util.__enter__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  run start...
2025-08-10 00:45:17.096 INFO log_util.__exit__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  run cost=[0 ms]
2025-08-10 00:45:17.096 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:mk3ek 第 1 轮深度搜索...
2025-08-10 00:45:17.098 INFO log_util.__aenter__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  query_decompose start...
2025-08-10 00:45:17.102 ERROR log_util.__aexit__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:17.106 INFO log_util.__enter__ 0a084bde-521f-4d07-be8e-d11977f52e0b  run start...
2025-08-10 00:45:17.107 INFO log_util.__exit__ 0a084bde-521f-4d07-be8e-d11977f52e0b  run cost=[1 ms]
2025-08-10 00:45:17.107 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:6f472 第 1 轮深度搜索...
2025-08-10 00:45:17.109 INFO log_util.__aenter__ 0a084bde-521f-4d07-be8e-d11977f52e0b  query_decompose start...
2025-08-10 00:45:17.112 ERROR log_util.__aexit__ 0a084bde-521f-4d07-be8e-d11977f52e0b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:17.115 INFO log_util.__aexit__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:45:17.116 INFO log_util.__aexit__ 0a084bde-521f-4d07-be8e-d11977f52e0b POST /v1/tool/deepsearch cost=[24 ms]
2025-08-10 00:45:23.723 INFO log_util.__aenter__ 27e0b650-54cc-4a33-affb-fecc4df56cf1 POST /v1/tool/deepsearch start...
2025-08-10 00:45:23.726 INFO log_util.__aenter__ d560fe3e-2a65-40f8-9fb1-10c987531873 POST /v1/tool/deepsearch start...
2025-08-10 00:45:23.729 INFO middleware_util.custom_route_handler 27e0b650-54cc-4a33-affb-fecc4df56cf1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间","request_id":"geniesession-1754757551055-129:1754757776162-4267:3yz40","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:23.733 INFO middleware_util.custom_route_handler d560fe3e-2a65-40f8-9fb1-10c987531873 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行费用估算","request_id":"geniesession-1754757551055-129:1754757776162-4267:xnify","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:23.741 INFO log_util.__enter__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  run start...
2025-08-10 00:45:23.746 INFO log_util.__exit__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  run cost=[5 ms]
2025-08-10 00:45:23.751 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:3yz40 第 1 轮深度搜索...
2025-08-10 00:45:23.759 INFO log_util.__aenter__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  query_decompose start...
2025-08-10 00:45:23.763 ERROR log_util.__aexit__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:23.772 INFO log_util.__enter__ d560fe3e-2a65-40f8-9fb1-10c987531873  run start...
2025-08-10 00:45:23.775 INFO log_util.__exit__ d560fe3e-2a65-40f8-9fb1-10c987531873  run cost=[3 ms]
2025-08-10 00:45:23.778 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:xnify 第 1 轮深度搜索...
2025-08-10 00:45:23.781 INFO log_util.__aenter__ d560fe3e-2a65-40f8-9fb1-10c987531873  query_decompose start...
2025-08-10 00:45:23.784 ERROR log_util.__aexit__ d560fe3e-2a65-40f8-9fb1-10c987531873  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:23.791 INFO log_util.__aexit__ 27e0b650-54cc-4a33-affb-fecc4df56cf1 POST /v1/tool/deepsearch cost=[68 ms]
2025-08-10 00:45:23.797 INFO log_util.__aexit__ d560fe3e-2a65-40f8-9fb1-10c987531873 POST /v1/tool/deepsearch cost=[71 ms]
2025-08-10 00:45:30.589 INFO log_util.__aenter__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7 POST /v1/tool/deepsearch start...
2025-08-10 00:45:30.591 INFO log_util.__aenter__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8 POST /v1/tool/deepsearch start...
2025-08-10 00:45:30.592 INFO middleware_util.custom_route_handler 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李准备","request_id":"geniesession-1754757551055-129:1754757776162-4267:qdadv","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:30.594 INFO middleware_util.custom_route_handler 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游必玩项目","request_id":"geniesession-1754757551055-129:1754757776162-4267:j2ie2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:30.596 INFO log_util.__enter__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  run start...
2025-08-10 00:45:30.597 INFO log_util.__exit__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  run cost=[0 ms]
2025-08-10 00:45:30.598 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:qdadv 第 1 轮深度搜索...
2025-08-10 00:45:30.621 INFO log_util.__aenter__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  query_decompose start...
2025-08-10 00:45:30.647 ERROR log_util.__aexit__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:30.666 INFO log_util.__enter__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  run start...
2025-08-10 00:45:30.685 INFO log_util.__exit__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  run cost=[19 ms]
2025-08-10 00:45:30.703 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:j2ie2 第 1 轮深度搜索...
2025-08-10 00:45:30.722 INFO log_util.__aenter__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  query_decompose start...
2025-08-10 00:45:30.742 ERROR log_util.__aexit__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:30.767 INFO log_util.__aexit__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7 POST /v1/tool/deepsearch cost=[178 ms]
2025-08-10 00:45:30.787 INFO log_util.__aexit__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8 POST /v1/tool/deepsearch cost=[196 ms]
2025-08-10 00:45:37.232 INFO log_util.__aenter__ ac369ff3-d580-4da4-9b6d-c1bacd14f138 POST /v1/tool/deepsearch start...
2025-08-10 00:45:37.233 INFO log_util.__aenter__ 7c303f9b-6868-40eb-896e-05ee97de10db POST /v1/tool/deepsearch start...
2025-08-10 00:45:37.235 INFO middleware_util.custom_route_handler ac369ff3-d580-4da4-9b6d-c1bacd14f138 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地习俗","request_id":"geniesession-1754757551055-129:1754757776162-4267:4eysp","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:37.237 INFO middleware_util.custom_route_handler 7c303f9b-6868-40eb-896e-05ee97de10db POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行语言沟通","request_id":"geniesession-1754757551055-129:1754757776162-4267:o30gy","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:37.239 INFO log_util.__enter__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  run start...
2025-08-10 00:45:37.244 INFO log_util.__exit__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  run cost=[5 ms]
2025-08-10 00:45:37.248 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:4eysp 第 1 轮深度搜索...
2025-08-10 00:45:37.249 INFO log_util.__aenter__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  query_decompose start...
2025-08-10 00:45:37.256 ERROR log_util.__aexit__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:37.258 INFO log_util.__enter__ 7c303f9b-6868-40eb-896e-05ee97de10db  run start...
2025-08-10 00:45:37.259 INFO log_util.__exit__ 7c303f9b-6868-40eb-896e-05ee97de10db  run cost=[0 ms]
2025-08-10 00:45:37.262 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:o30gy 第 1 轮深度搜索...
2025-08-10 00:45:37.266 INFO log_util.__aenter__ 7c303f9b-6868-40eb-896e-05ee97de10db  query_decompose start...
2025-08-10 00:45:37.271 ERROR log_util.__aexit__ 7c303f9b-6868-40eb-896e-05ee97de10db  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:37.273 INFO log_util.__aexit__ ac369ff3-d580-4da4-9b6d-c1bacd14f138 POST /v1/tool/deepsearch cost=[41 ms]
2025-08-10 00:45:37.276 INFO log_util.__aexit__ 7c303f9b-6868-40eb-896e-05ee97de10db POST /v1/tool/deepsearch cost=[43 ms]
2025-08-10 00:45:43.728 INFO log_util.__aenter__ f6c8477c-f759-4da8-a41e-69d67111a229 POST /v1/tool/deepsearch start...
2025-08-10 00:45:43.731 INFO log_util.__aenter__ 57f69525-2f8a-45e7-9a8c-11fccd091d73 POST /v1/tool/deepsearch start...
2025-08-10 00:45:43.737 INFO middleware_util.custom_route_handler f6c8477c-f759-4da8-a41e-69d67111a229 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游货币兑换","request_id":"geniesession-1754757551055-129:1754757776162-4267:fofqy","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:43.739 INFO middleware_util.custom_route_handler 57f69525-2f8a-45e7-9a8c-11fccd091d73 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行网络通讯","request_id":"geniesession-1754757551055-129:1754757776162-4267:mketw","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:43.744 INFO log_util.__enter__ f6c8477c-f759-4da8-a41e-69d67111a229  run start...
2025-08-10 00:45:43.747 INFO log_util.__exit__ f6c8477c-f759-4da8-a41e-69d67111a229  run cost=[2 ms]
2025-08-10 00:45:43.749 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:fofqy 第 1 轮深度搜索...
2025-08-10 00:45:43.752 INFO log_util.__aenter__ f6c8477c-f759-4da8-a41e-69d67111a229  query_decompose start...
2025-08-10 00:45:43.758 ERROR log_util.__aexit__ f6c8477c-f759-4da8-a41e-69d67111a229  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:43.760 INFO log_util.__enter__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  run start...
2025-08-10 00:45:43.764 INFO log_util.__exit__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  run cost=[3 ms]
2025-08-10 00:45:43.766 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:mketw 第 1 轮深度搜索...
2025-08-10 00:45:43.769 INFO log_util.__aenter__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  query_decompose start...
2025-08-10 00:45:43.778 ERROR log_util.__aexit__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:43.781 INFO log_util.__aexit__ f6c8477c-f759-4da8-a41e-69d67111a229 POST /v1/tool/deepsearch cost=[52 ms]
2025-08-10 00:45:43.789 INFO log_util.__aexit__ 57f69525-2f8a-45e7-9a8c-11fccd091d73 POST /v1/tool/deepsearch cost=[58 ms]
2025-08-10 00:45:50.172 INFO log_util.__aenter__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f POST /v1/tool/deepsearch start...
2025-08-10 00:45:50.175 INFO log_util.__aenter__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd POST /v1/tool/deepsearch start...
2025-08-10 00:45:50.177 INFO middleware_util.custom_route_handler aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行健康安全","request_id":"geniesession-1754757551055-129:1754757776162-4267:gofe2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:50.179 INFO middleware_util.custom_route_handler 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游摄影地点","request_id":"geniesession-1754757551055-129:1754757776162-4267:ce5c9","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:50.181 INFO log_util.__enter__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  run start...
2025-08-10 00:45:50.184 INFO log_util.__exit__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  run cost=[3 ms]
2025-08-10 00:45:50.195 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:gofe2 第 1 轮深度搜索...
2025-08-10 00:45:50.203 INFO log_util.__aenter__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  query_decompose start...
2025-08-10 00:45:50.214 ERROR log_util.__aexit__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:50.222 INFO log_util.__enter__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  run start...
2025-08-10 00:45:50.229 INFO log_util.__exit__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  run cost=[6 ms]
2025-08-10 00:45:50.236 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:ce5c9 第 1 轮深度搜索...
2025-08-10 00:45:50.244 INFO log_util.__aenter__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  query_decompose start...
2025-08-10 00:45:50.254 ERROR log_util.__aexit__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:50.265 INFO log_util.__aexit__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f POST /v1/tool/deepsearch cost=[92 ms]
2025-08-10 00:45:50.287 INFO log_util.__aexit__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd POST /v1/tool/deepsearch cost=[111 ms]
2025-08-10 00:45:57.029 INFO log_util.__aenter__ 9d444181-3169-4be7-bcf2-f046ddefd2d6 POST /v1/tool/deepsearch start...
2025-08-10 00:45:57.032 INFO log_util.__aenter__ d73697cb-3ddd-4039-9b58-c4d68699ea59 POST /v1/tool/deepsearch start...
2025-08-10 00:45:57.034 INFO middleware_util.custom_route_handler 9d444181-3169-4be7-bcf2-f046ddefd2d6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行环保建议","request_id":"geniesession-1754757551055-129:1754757776162-4267:81wt1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:57.037 INFO middleware_util.custom_route_handler d73697cb-3ddd-4039-9b58-c4d68699ea59 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游夜生活推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:9mfvg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:57.039 INFO log_util.__enter__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  run start...
2025-08-10 00:45:57.041 INFO log_util.__exit__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  run cost=[2 ms]
2025-08-10 00:45:57.042 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:81wt1 第 1 轮深度搜索...
2025-08-10 00:45:57.043 INFO log_util.__aenter__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  query_decompose start...
2025-08-10 00:45:57.049 ERROR log_util.__aexit__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:57.052 INFO log_util.__enter__ d73697cb-3ddd-4039-9b58-c4d68699ea59  run start...
2025-08-10 00:45:57.054 INFO log_util.__exit__ d73697cb-3ddd-4039-9b58-c4d68699ea59  run cost=[2 ms]
2025-08-10 00:45:57.057 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:9mfvg 第 1 轮深度搜索...
2025-08-10 00:45:57.058 INFO log_util.__aenter__ d73697cb-3ddd-4039-9b58-c4d68699ea59  query_decompose start...
2025-08-10 00:45:57.062 ERROR log_util.__aexit__ d73697cb-3ddd-4039-9b58-c4d68699ea59  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:57.066 INFO log_util.__aexit__ 9d444181-3169-4be7-bcf2-f046ddefd2d6 POST /v1/tool/deepsearch cost=[36 ms]
2025-08-10 00:45:57.073 INFO log_util.__aexit__ d73697cb-3ddd-4039-9b58-c4d68699ea59 POST /v1/tool/deepsearch cost=[41 ms]
2025-08-10 00:46:05.778 INFO log_util.__aenter__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861 POST /v1/tool/deepsearch start...
2025-08-10 00:46:05.780 INFO log_util.__aenter__ 4e0bfd72-4d0c-408d-9458-a61c613f9308 POST /v1/tool/deepsearch start...
2025-08-10 00:46:05.782 INFO middleware_util.custom_route_handler 274f707b-4a08-4bfc-b7ce-a9c1ed19f861 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行紧急联系方式","request_id":"geniesession-1754757551055-129:1754757776162-4267:z6clg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:05.783 INFO middleware_util.custom_route_handler 4e0bfd72-4d0c-408d-9458-a61c613f9308 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游亲子活动","request_id":"geniesession-1754757551055-129:1754757776162-4267:3kh5k","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:05.785 INFO log_util.__enter__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  run start...
2025-08-10 00:46:05.789 INFO log_util.__exit__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  run cost=[4 ms]
2025-08-10 00:46:05.790 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:z6clg 第 1 轮深度搜索...
2025-08-10 00:46:05.791 INFO log_util.__aenter__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  query_decompose start...
2025-08-10 00:46:05.793 ERROR log_util.__aexit__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:05.796 INFO log_util.__enter__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  run start...
2025-08-10 00:46:05.802 INFO log_util.__exit__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  run cost=[5 ms]
2025-08-10 00:46:05.806 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:3kh5k 第 1 轮深度搜索...
2025-08-10 00:46:05.810 INFO log_util.__aenter__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  query_decompose start...
2025-08-10 00:46:05.816 ERROR log_util.__aexit__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:05.823 INFO log_util.__aexit__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861 POST /v1/tool/deepsearch cost=[44 ms]
2025-08-10 00:46:05.829 INFO log_util.__aexit__ 4e0bfd72-4d0c-408d-9458-a61c613f9308 POST /v1/tool/deepsearch cost=[48 ms]
2025-08-10 00:46:12.347 INFO log_util.__aenter__ 8e62b739-7d46-414e-adee-13b6fa21dba6 POST /v1/tool/deepsearch start...
2025-08-10 00:46:12.347 INFO log_util.__aenter__ d18e399f-1688-442d-8a35-e6846d33f56f POST /v1/tool/deepsearch start...
2025-08-10 00:46:12.349 INFO middleware_util.custom_route_handler 8e62b739-7d46-414e-adee-13b6fa21dba6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游潜水地点","request_id":"geniesession-1754757551055-129:1754757776162-4267:hrnoa","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:12.350 INFO middleware_util.custom_route_handler d18e399f-1688-442d-8a35-e6846d33f56f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李限制","request_id":"geniesession-1754757551055-129:1754757776162-4267:l2tbc","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:12.351 INFO log_util.__enter__ 8e62b739-7d46-414e-adee-13b6fa21dba6  run start...
2025-08-10 00:46:12.351 INFO log_util.__exit__ 8e62b739-7d46-414e-adee-13b6fa21dba6  run cost=[0 ms]
2025-08-10 00:46:12.352 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:hrnoa 第 1 轮深度搜索...
2025-08-10 00:46:12.352 INFO log_util.__aenter__ 8e62b739-7d46-414e-adee-13b6fa21dba6  query_decompose start...
2025-08-10 00:46:12.356 ERROR log_util.__aexit__ 8e62b739-7d46-414e-adee-13b6fa21dba6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:12.359 INFO log_util.__enter__ d18e399f-1688-442d-8a35-e6846d33f56f  run start...
2025-08-10 00:46:12.363 INFO log_util.__exit__ d18e399f-1688-442d-8a35-e6846d33f56f  run cost=[3 ms]
2025-08-10 00:46:12.364 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:l2tbc 第 1 轮深度搜索...
2025-08-10 00:46:12.364 INFO log_util.__aenter__ d18e399f-1688-442d-8a35-e6846d33f56f  query_decompose start...
2025-08-10 00:46:12.366 ERROR log_util.__aexit__ d18e399f-1688-442d-8a35-e6846d33f56f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:12.367 INFO log_util.__aexit__ 8e62b739-7d46-414e-adee-13b6fa21dba6 POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:46:12.369 INFO log_util.__aexit__ d18e399f-1688-442d-8a35-e6846d33f56f POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:46:18.777 INFO log_util.__aenter__ 69122c3a-9006-4d56-9cd1-96b54245c8af POST /v1/tool/deepsearch start...
2025-08-10 00:46:18.778 INFO log_util.__aenter__ a4fdbd67-6571-413a-a0a4-cbff8cea5787 POST /v1/tool/deepsearch start...
2025-08-10 00:46:18.780 INFO middleware_util.custom_route_handler 69122c3a-9006-4d56-9cd1-96b54245c8af POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行天气情况","request_id":"geniesession-1754757551055-129:1754757776162-4267:9bs5u","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:18.780 INFO middleware_util.custom_route_handler a4fdbd67-6571-413a-a0a4-cbff8cea5787 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游SPA推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:lslk2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:18.782 INFO log_util.__enter__ 69122c3a-9006-4d56-9cd1-96b54245c8af  run start...
2025-08-10 00:46:18.782 INFO log_util.__exit__ 69122c3a-9006-4d56-9cd1-96b54245c8af  run cost=[0 ms]
2025-08-10 00:46:18.783 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:9bs5u 第 1 轮深度搜索...
2025-08-10 00:46:18.783 INFO log_util.__aenter__ 69122c3a-9006-4d56-9cd1-96b54245c8af  query_decompose start...
2025-08-10 00:46:18.787 ERROR log_util.__aexit__ 69122c3a-9006-4d56-9cd1-96b54245c8af  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:18.790 INFO log_util.__enter__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  run start...
2025-08-10 00:46:18.791 INFO log_util.__exit__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  run cost=[0 ms]
2025-08-10 00:46:18.792 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:lslk2 第 1 轮深度搜索...
2025-08-10 00:46:18.792 INFO log_util.__aenter__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  query_decompose start...
2025-08-10 00:46:18.795 ERROR log_util.__aexit__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:18.799 INFO log_util.__aexit__ 69122c3a-9006-4d56-9cd1-96b54245c8af POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:46:18.801 INFO log_util.__aexit__ a4fdbd67-6571-413a-a0a4-cbff8cea5787 POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:46:24.806 INFO log_util.__aenter__ a3104a22-7915-4e90-9c45-20c4b955ce7f POST /v1/tool/deepsearch start...
2025-08-10 00:46:24.807 INFO log_util.__aenter__ 0ea06094-a830-4a40-93d9-a79dceb05589 POST /v1/tool/deepsearch start...
2025-08-10 00:46:24.809 INFO middleware_util.custom_route_handler a3104a22-7915-4e90-9c45-20c4b955ce7f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游婚礼场地","request_id":"geniesession-1754757551055-129:1754757776162-4267:rlvty","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:24.810 INFO middleware_util.custom_route_handler 0ea06094-a830-4a40-93d9-a79dceb05589 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行交通卡","request_id":"geniesession-1754757551055-129:1754757776162-4267:ipx8t","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:24.811 INFO log_util.__enter__ a3104a22-7915-4e90-9c45-20c4b955ce7f  run start...
2025-08-10 00:46:24.812 INFO log_util.__exit__ a3104a22-7915-4e90-9c45-20c4b955ce7f  run cost=[0 ms]
2025-08-10 00:46:24.812 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:rlvty 第 1 轮深度搜索...
2025-08-10 00:46:24.814 INFO log_util.__aenter__ a3104a22-7915-4e90-9c45-20c4b955ce7f  query_decompose start...
2025-08-10 00:46:24.819 ERROR log_util.__aexit__ a3104a22-7915-4e90-9c45-20c4b955ce7f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:24.819 INFO log_util.__enter__ 0ea06094-a830-4a40-93d9-a79dceb05589  run start...
2025-08-10 00:46:24.824 INFO log_util.__exit__ 0ea06094-a830-4a40-93d9-a79dceb05589  run cost=[4 ms]
2025-08-10 00:46:24.826 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:ipx8t 第 1 轮深度搜索...
2025-08-10 00:46:24.828 INFO log_util.__aenter__ 0ea06094-a830-4a40-93d9-a79dceb05589  query_decompose start...
2025-08-10 00:46:24.831 ERROR log_util.__aexit__ 0ea06094-a830-4a40-93d9-a79dceb05589  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:24.838 INFO log_util.__aexit__ a3104a22-7915-4e90-9c45-20c4b955ce7f POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:46:24.843 INFO log_util.__aexit__ 0ea06094-a830-4a40-93d9-a79dceb05589 POST /v1/tool/deepsearch cost=[36 ms]
2025-08-10 00:46:31.099 INFO log_util.__aenter__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6 POST /v1/tool/deepsearch start...
2025-08-10 00:46:31.101 INFO log_util.__aenter__ 54aeee17-6562-44d7-b08d-ff440413aae7 POST /v1/tool/deepsearch start...
2025-08-10 00:46:31.103 INFO middleware_util.custom_route_handler 707d004f-c54d-4254-9e6e-6e6fb035f4e6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游冲浪地点","request_id":"geniesession-1754757551055-129:1754757776162-4267:396nt","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:31.105 INFO middleware_util.custom_route_handler 54aeee17-6562-44d7-b08d-ff440413aae7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李打包技巧","request_id":"geniesession-1754757551055-129:1754757776162-4267:vetgk","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:31.106 INFO log_util.__enter__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  run start...
2025-08-10 00:46:31.107 INFO log_util.__exit__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  run cost=[1 ms]
2025-08-10 00:46:31.109 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:396nt 第 1 轮深度搜索...
2025-08-10 00:46:31.113 INFO log_util.__aenter__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  query_decompose start...
2025-08-10 00:46:31.116 ERROR log_util.__aexit__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:31.118 INFO log_util.__enter__ 54aeee17-6562-44d7-b08d-ff440413aae7  run start...
2025-08-10 00:46:31.121 INFO log_util.__exit__ 54aeee17-6562-44d7-b08d-ff440413aae7  run cost=[2 ms]
2025-08-10 00:46:31.123 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vetgk 第 1 轮深度搜索...
2025-08-10 00:46:31.125 INFO log_util.__aenter__ 54aeee17-6562-44d7-b08d-ff440413aae7  query_decompose start...
2025-08-10 00:46:31.129 ERROR log_util.__aexit__ 54aeee17-6562-44d7-b08d-ff440413aae7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:31.132 INFO log_util.__aexit__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6 POST /v1/tool/deepsearch cost=[33 ms]
2025-08-10 00:46:31.137 INFO log_util.__aexit__ 54aeee17-6562-44d7-b08d-ff440413aae7 POST /v1/tool/deepsearch cost=[36 ms]
2025-08-10 00:46:37.355 INFO log_util.__aenter__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6 POST /v1/tool/deepsearch start...
2025-08-10 00:46:37.356 INFO log_util.__aenter__ 48193c12-0aab-4bf2-9981-0a6f710604ca POST /v1/tool/deepsearch start...
2025-08-10 00:46:37.359 INFO middleware_util.custom_route_handler 09eab9c4-a584-47bd-b8ed-a16f7089a3c6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行机场接送","request_id":"geniesession-1754757551055-129:1754757776162-4267:jiyc1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:37.360 INFO middleware_util.custom_route_handler 48193c12-0aab-4bf2-9981-0a6f710604ca POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游瑜伽体验","request_id":"geniesession-1754757551055-129:1754757776162-4267:u6n1g","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:37.361 INFO log_util.__enter__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  run start...
2025-08-10 00:46:37.363 INFO log_util.__exit__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  run cost=[2 ms]
2025-08-10 00:46:37.366 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:jiyc1 第 1 轮深度搜索...
2025-08-10 00:46:37.367 INFO log_util.__aenter__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  query_decompose start...
2025-08-10 00:46:37.373 ERROR log_util.__aexit__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:37.374 INFO log_util.__enter__ 48193c12-0aab-4bf2-9981-0a6f710604ca  run start...
2025-08-10 00:46:37.377 INFO log_util.__exit__ 48193c12-0aab-4bf2-9981-0a6f710604ca  run cost=[3 ms]
2025-08-10 00:46:37.378 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:u6n1g 第 1 轮深度搜索...
2025-08-10 00:46:37.380 INFO log_util.__aenter__ 48193c12-0aab-4bf2-9981-0a6f710604ca  query_decompose start...
2025-08-10 00:46:37.385 ERROR log_util.__aexit__ 48193c12-0aab-4bf2-9981-0a6f710604ca  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:37.386 INFO log_util.__aexit__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6 POST /v1/tool/deepsearch cost=[31 ms]
2025-08-10 00:46:37.388 INFO log_util.__aexit__ 48193c12-0aab-4bf2-9981-0a6f710604ca POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:46:45.786 INFO log_util.__aenter__ eca75d18-5941-4b08-b51c-2bfc23b3acf2 POST /v1/tool/deepsearch start...
2025-08-10 00:46:45.788 INFO log_util.__aenter__ 20d85433-f78b-4881-9cbe-87327045435d POST /v1/tool/deepsearch start...
2025-08-10 00:46:45.791 INFO middleware_util.custom_route_handler eca75d18-5941-4b08-b51c-2bfc23b3acf2 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游徒步路线","request_id":"geniesession-1754757551055-129:1754757776162-4267:kkfo4","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:45.792 INFO middleware_util.custom_route_handler 20d85433-f78b-4881-9cbe-87327045435d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行货币兑换建议","request_id":"geniesession-1754757551055-129:1754757776162-4267:p59z6","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:45.795 INFO log_util.__enter__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  run start...
2025-08-10 00:46:45.797 INFO log_util.__exit__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  run cost=[1 ms]
2025-08-10 00:46:45.797 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:kkfo4 第 1 轮深度搜索...
2025-08-10 00:46:45.798 INFO log_util.__aenter__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  query_decompose start...
2025-08-10 00:46:45.800 ERROR log_util.__aexit__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:45.801 INFO log_util.__enter__ 20d85433-f78b-4881-9cbe-87327045435d  run start...
2025-08-10 00:46:45.803 INFO log_util.__exit__ 20d85433-f78b-4881-9cbe-87327045435d  run cost=[3 ms]
2025-08-10 00:46:45.805 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:p59z6 第 1 轮深度搜索...
2025-08-10 00:46:45.806 INFO log_util.__aenter__ 20d85433-f78b-4881-9cbe-87327045435d  query_decompose start...
2025-08-10 00:46:45.808 ERROR log_util.__aexit__ 20d85433-f78b-4881-9cbe-87327045435d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:45.809 INFO log_util.__aexit__ eca75d18-5941-4b08-b51c-2bfc23b3acf2 POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:46:45.811 INFO log_util.__aexit__ 20d85433-f78b-4881-9cbe-87327045435d POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:46:52.005 INFO log_util.__aenter__ f002619b-f64d-4064-8349-736cef248950 POST /v1/tool/deepsearch start...
2025-08-10 00:46:52.007 INFO log_util.__aenter__ e7b88e70-d6cf-42e3-a768-201754b841c4 POST /v1/tool/deepsearch start...
2025-08-10 00:46:52.008 INFO middleware_util.custom_route_handler f002619b-f64d-4064-8349-736cef248950 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行语言翻译工具","request_id":"geniesession-1754757551055-129:1754757776162-4267:konc4","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:52.009 INFO middleware_util.custom_route_handler e7b88e70-d6cf-42e3-a768-201754b841c4 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游水上活动","request_id":"geniesession-1754757551055-129:1754757776162-4267:bes5r","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:52.010 INFO log_util.__enter__ f002619b-f64d-4064-8349-736cef248950  run start...
2025-08-10 00:46:52.010 INFO log_util.__exit__ f002619b-f64d-4064-8349-736cef248950  run cost=[0 ms]
2025-08-10 00:46:52.011 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:konc4 第 1 轮深度搜索...
2025-08-10 00:46:52.011 INFO log_util.__aenter__ f002619b-f64d-4064-8349-736cef248950  query_decompose start...
2025-08-10 00:46:52.016 ERROR log_util.__aexit__ f002619b-f64d-4064-8349-736cef248950  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:52.017 INFO log_util.__enter__ e7b88e70-d6cf-42e3-a768-201754b841c4  run start...
2025-08-10 00:46:52.019 INFO log_util.__exit__ e7b88e70-d6cf-42e3-a768-201754b841c4  run cost=[1 ms]
2025-08-10 00:46:52.019 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:bes5r 第 1 轮深度搜索...
2025-08-10 00:46:52.019 INFO log_util.__aenter__ e7b88e70-d6cf-42e3-a768-201754b841c4  query_decompose start...
2025-08-10 00:46:52.021 ERROR log_util.__aexit__ e7b88e70-d6cf-42e3-a768-201754b841c4  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:52.022 INFO log_util.__aexit__ f002619b-f64d-4064-8349-736cef248950 POST /v1/tool/deepsearch cost=[17 ms]
2025-08-10 00:46:52.027 INFO log_util.__aexit__ e7b88e70-d6cf-42e3-a768-201754b841c4 POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:47:00.858 INFO log_util.__aenter__ 371b47c1-473a-4aa3-8174-906c6d729ed7 POST /v1/tool/deepsearch start...
2025-08-10 00:47:00.859 INFO log_util.__aenter__ b0f4bbac-3aea-47ad-b577-37ded47536ed POST /v1/tool/deepsearch start...
2025-08-10 00:47:00.862 INFO middleware_util.custom_route_handler 371b47c1-473a-4aa3-8174-906c6d729ed7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行SIM卡购买","request_id":"geniesession-1754757551055-129:1754757776162-4267:atp72","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:00.863 INFO middleware_util.custom_route_handler b0f4bbac-3aea-47ad-b577-37ded47536ed POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游日落观赏点","request_id":"geniesession-1754757551055-129:1754757776162-4267:x401s","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:00.864 INFO log_util.__enter__ 371b47c1-473a-4aa3-8174-906c6d729ed7  run start...
2025-08-10 00:47:00.864 INFO log_util.__exit__ 371b47c1-473a-4aa3-8174-906c6d729ed7  run cost=[0 ms]
2025-08-10 00:47:00.865 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:atp72 第 1 轮深度搜索...
2025-08-10 00:47:00.868 INFO log_util.__aenter__ 371b47c1-473a-4aa3-8174-906c6d729ed7  query_decompose start...
2025-08-10 00:47:00.871 ERROR log_util.__aexit__ 371b47c1-473a-4aa3-8174-906c6d729ed7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:00.871 INFO log_util.__enter__ b0f4bbac-3aea-47ad-b577-37ded47536ed  run start...
2025-08-10 00:47:00.874 INFO log_util.__exit__ b0f4bbac-3aea-47ad-b577-37ded47536ed  run cost=[3 ms]
2025-08-10 00:47:00.874 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:x401s 第 1 轮深度搜索...
2025-08-10 00:47:00.874 INFO log_util.__aenter__ b0f4bbac-3aea-47ad-b577-37ded47536ed  query_decompose start...
2025-08-10 00:47:00.877 ERROR log_util.__aexit__ b0f4bbac-3aea-47ad-b577-37ded47536ed  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:00.878 INFO log_util.__aexit__ 371b47c1-473a-4aa3-8174-906c6d729ed7 POST /v1/tool/deepsearch cost=[19 ms]
2025-08-10 00:47:00.882 INFO log_util.__aexit__ b0f4bbac-3aea-47ad-b577-37ded47536ed POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:47:06.888 INFO log_util.__aenter__ 2897f422-472e-4292-bbf1-4026b953ae8d POST /v1/tool/deepsearch start...
2025-08-10 00:47:06.891 INFO log_util.__aenter__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12 POST /v1/tool/deepsearch start...
2025-08-10 00:47:06.896 INFO middleware_util.custom_route_handler 2897f422-472e-4292-bbf1-4026b953ae8d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游购物市场","request_id":"geniesession-1754757551055-129:1754757776162-4267:dtt10","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:06.899 INFO middleware_util.custom_route_handler f7f38636-3ad7-468d-8f3d-6b4c1300ee12 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李托运","request_id":"geniesession-1754757551055-129:1754757776162-4267:gx84a","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:06.902 INFO log_util.__enter__ 2897f422-472e-4292-bbf1-4026b953ae8d  run start...
2025-08-10 00:47:06.912 INFO log_util.__exit__ 2897f422-472e-4292-bbf1-4026b953ae8d  run cost=[9 ms]
2025-08-10 00:47:06.916 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:dtt10 第 1 轮深度搜索...
2025-08-10 00:47:06.921 INFO log_util.__aenter__ 2897f422-472e-4292-bbf1-4026b953ae8d  query_decompose start...
2025-08-10 00:47:06.928 ERROR log_util.__aexit__ 2897f422-472e-4292-bbf1-4026b953ae8d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:06.935 INFO log_util.__enter__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  run start...
2025-08-10 00:47:06.939 INFO log_util.__exit__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  run cost=[4 ms]
2025-08-10 00:47:06.946 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:gx84a 第 1 轮深度搜索...
2025-08-10 00:47:06.951 INFO log_util.__aenter__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  query_decompose start...
2025-08-10 00:47:06.962 ERROR log_util.__aexit__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:06.972 INFO log_util.__aexit__ 2897f422-472e-4292-bbf1-4026b953ae8d POST /v1/tool/deepsearch cost=[83 ms]
2025-08-10 00:47:06.984 INFO log_util.__aexit__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12 POST /v1/tool/deepsearch cost=[93 ms]
2025-08-10 00:47:14.172 INFO log_util.__aenter__ f41e659a-0abd-460b-9c58-f7151ade47af POST /v1/tool/deepsearch start...
2025-08-10 00:47:14.172 INFO log_util.__aenter__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8 POST /v1/tool/deepsearch start...
2025-08-10 00:47:14.174 INFO middleware_util.custom_route_handler f41e659a-0abd-460b-9c58-f7151ade47af POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地美食","request_id":"geniesession-1754757551055-129:1754757776162-4267:cyybh","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:14.175 INFO middleware_util.custom_route_handler cc420227-afdb-4b18-bd09-6ccfca2ed7c8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行机场退税","request_id":"geniesession-1754757551055-129:1754757776162-4267:altk3","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:14.176 INFO log_util.__enter__ f41e659a-0abd-460b-9c58-f7151ade47af  run start...
2025-08-10 00:47:14.176 INFO log_util.__exit__ f41e659a-0abd-460b-9c58-f7151ade47af  run cost=[0 ms]
2025-08-10 00:47:14.177 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:cyybh 第 1 轮深度搜索...
2025-08-10 00:47:14.177 INFO log_util.__aenter__ f41e659a-0abd-460b-9c58-f7151ade47af  query_decompose start...
2025-08-10 00:47:14.185 ERROR log_util.__aexit__ f41e659a-0abd-460b-9c58-f7151ade47af  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:14.186 INFO log_util.__enter__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  run start...
2025-08-10 00:47:14.187 INFO log_util.__exit__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  run cost=[1 ms]
2025-08-10 00:47:14.187 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:altk3 第 1 轮深度搜索...
2025-08-10 00:47:14.188 INFO log_util.__aenter__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  query_decompose start...
2025-08-10 00:47:14.190 ERROR log_util.__aexit__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:14.191 INFO log_util.__aexit__ f41e659a-0abd-460b-9c58-f7151ade47af POST /v1/tool/deepsearch cost=[18 ms]
2025-08-10 00:47:14.194 INFO log_util.__aexit__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8 POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:47:20.684 INFO log_util.__aenter__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5 POST /v1/tool/deepsearch start...
2025-08-10 00:47:20.685 INFO log_util.__aenter__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09 POST /v1/tool/deepsearch start...
2025-08-10 00:47:20.687 INFO middleware_util.custom_route_handler 08cf742a-2aa1-4f01-b0ad-4c2c118febf5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李安检","request_id":"geniesession-1754757551055-129:1754757776162-4267:mdvne","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:20.688 INFO middleware_util.custom_route_handler a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游海滩推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:1ugp8","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:20.689 INFO log_util.__enter__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  run start...
2025-08-10 00:47:20.691 INFO log_util.__exit__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  run cost=[2 ms]
2025-08-10 00:47:20.692 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:mdvne 第 1 轮深度搜索...
2025-08-10 00:47:20.693 INFO log_util.__aenter__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  query_decompose start...
2025-08-10 00:47:20.695 ERROR log_util.__aexit__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:20.696 INFO log_util.__enter__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  run start...
2025-08-10 00:47:20.698 INFO log_util.__exit__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  run cost=[2 ms]
2025-08-10 00:47:20.699 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:1ugp8 第 1 轮深度搜索...
2025-08-10 00:47:20.699 INFO log_util.__aenter__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  query_decompose start...
2025-08-10 00:47:20.704 ERROR log_util.__aexit__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:20.705 INFO log_util.__aexit__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5 POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:47:20.708 INFO log_util.__aexit__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09 POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:47:26.890 INFO log_util.__aenter__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a POST /v1/tool/deepsearch start...
2025-08-10 00:47:26.891 INFO log_util.__aenter__ 28b46fdd-cca8-4de0-995f-8db1a3abd877 POST /v1/tool/deepsearch start...
2025-08-10 00:47:26.894 INFO middleware_util.custom_route_handler 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游文化节庆","request_id":"geniesession-1754757551055-129:1754757776162-4267:v4tcc","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:26.896 INFO middleware_util.custom_route_handler 28b46fdd-cca8-4de0-995f-8db1a3abd877 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李寄存","request_id":"geniesession-1754757551055-129:1754757776162-4267:n7rm2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:26.897 INFO log_util.__enter__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  run start...
2025-08-10 00:47:26.901 INFO log_util.__exit__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  run cost=[3 ms]
2025-08-10 00:47:26.907 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:v4tcc 第 1 轮深度搜索...
2025-08-10 00:47:26.909 INFO log_util.__aenter__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  query_decompose start...
2025-08-10 00:47:26.912 ERROR log_util.__aexit__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:26.914 INFO log_util.__enter__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  run start...
2025-08-10 00:47:26.915 INFO log_util.__exit__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  run cost=[2 ms]
2025-08-10 00:47:26.918 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:n7rm2 第 1 轮深度搜索...
2025-08-10 00:47:26.923 INFO log_util.__aenter__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  query_decompose start...
2025-08-10 00:47:26.926 ERROR log_util.__aexit__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:26.928 INFO log_util.__aexit__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a POST /v1/tool/deepsearch cost=[38 ms]
2025-08-10 00:47:26.935 INFO log_util.__aexit__ 28b46fdd-cca8-4de0-995f-8db1a3abd877 POST /v1/tool/deepsearch cost=[43 ms]
2025-08-10 00:47:34.941 INFO log_util.__aenter__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d POST /v1/tool/deepsearch start...
2025-08-10 00:47:34.942 INFO log_util.__aenter__ 391fdf9b-fd8d-474c-b187-ef9384fe51db POST /v1/tool/deepsearch start...
2025-08-10 00:47:34.945 INFO middleware_util.custom_route_handler 448652fb-cb18-4a1c-9fe0-cd3b91af567d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游家庭住宿","request_id":"geniesession-1754757551055-129:1754757776162-4267:zllfq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:34.946 INFO middleware_util.custom_route_handler 391fdf9b-fd8d-474c-b187-ef9384fe51db POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李打包清单","request_id":"geniesession-1754757551055-129:1754757776162-4267:vrz6o","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:34.947 INFO log_util.__enter__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  run start...
2025-08-10 00:47:34.947 INFO log_util.__exit__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  run cost=[0 ms]
2025-08-10 00:47:34.949 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:zllfq 第 1 轮深度搜索...
2025-08-10 00:47:34.951 INFO log_util.__aenter__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  query_decompose start...
2025-08-10 00:47:34.954 ERROR log_util.__aexit__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:34.955 INFO log_util.__enter__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  run start...
2025-08-10 00:47:34.955 INFO log_util.__exit__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  run cost=[0 ms]
2025-08-10 00:47:34.956 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vrz6o 第 1 轮深度搜索...
2025-08-10 00:47:34.956 INFO log_util.__aenter__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  query_decompose start...
2025-08-10 00:47:34.959 ERROR log_util.__aexit__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:34.962 INFO log_util.__aexit__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:47:34.963 INFO log_util.__aexit__ 391fdf9b-fd8d-474c-b187-ef9384fe51db POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:47:41.311 INFO log_util.__aenter__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f POST /v1/tool/deepsearch start...
2025-08-10 00:47:41.312 INFO log_util.__aenter__ 17299e10-6530-443f-9dd4-9cc868b6c9ab POST /v1/tool/deepsearch start...
2025-08-10 00:47:41.314 INFO middleware_util.custom_route_handler 105a909e-6bdb-4b86-b1e8-c168a8ffd66f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李重量限制","request_id":"geniesession-1754757551055-129:1754757776162-4267:ct2r1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:41.314 INFO middleware_util.custom_route_handler 17299e10-6530-443f-9dd4-9cc868b6c9ab POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地交通","request_id":"geniesession-1754757551055-129:1754757776162-4267:smumv","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:41.315 INFO log_util.__enter__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  run start...
2025-08-10 00:47:41.316 INFO log_util.__exit__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  run cost=[0 ms]
2025-08-10 00:47:41.317 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:ct2r1 第 1 轮深度搜索...
2025-08-10 00:47:41.321 INFO log_util.__aenter__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  query_decompose start...
2025-08-10 00:47:41.325 ERROR log_util.__aexit__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:41.326 INFO log_util.__enter__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  run start...
2025-08-10 00:47:41.327 INFO log_util.__exit__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  run cost=[1 ms]
2025-08-10 00:47:41.328 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:smumv 第 1 轮深度搜索...
2025-08-10 00:47:41.328 INFO log_util.__aenter__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  query_decompose start...
2025-08-10 00:47:41.331 ERROR log_util.__aexit__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:41.335 INFO log_util.__aexit__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:47:41.338 INFO log_util.__aexit__ 17299e10-6530-443f-9dd4-9cc868b6c9ab POST /v1/tool/deepsearch cost=[25 ms]
2025-08-10 00:47:47.766 INFO log_util.__aenter__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d POST /v1/tool/deepsearch start...
2025-08-10 00:47:47.768 INFO log_util.__aenter__ 805ae152-2f0a-422c-aa8f-21790bd16024 POST /v1/tool/deepsearch start...
2025-08-10 00:47:47.773 INFO middleware_util.custom_route_handler 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地导游服务","request_id":"geniesession-1754757551055-129:1754757776162-4267:eytjl","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:47.775 INFO middleware_util.custom_route_handler 805ae152-2f0a-422c-aa8f-21790bd16024 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李托运费用","request_id":"geniesession-1754757551055-129:1754757776162-4267:zx0fh","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:47.777 INFO log_util.__enter__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  run start...
2025-08-10 00:47:47.779 INFO log_util.__exit__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  run cost=[1 ms]
2025-08-10 00:47:47.781 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:eytjl 第 1 轮深度搜索...
2025-08-10 00:47:47.783 INFO log_util.__aenter__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  query_decompose start...
2025-08-10 00:47:47.786 ERROR log_util.__aexit__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:47.789 INFO log_util.__enter__ 805ae152-2f0a-422c-aa8f-21790bd16024  run start...
2025-08-10 00:47:47.792 INFO log_util.__exit__ 805ae152-2f0a-422c-aa8f-21790bd16024  run cost=[2 ms]
2025-08-10 00:47:47.793 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:zx0fh 第 1 轮深度搜索...
2025-08-10 00:47:47.795 INFO log_util.__aenter__ 805ae152-2f0a-422c-aa8f-21790bd16024  query_decompose start...
2025-08-10 00:47:47.801 ERROR log_util.__aexit__ 805ae152-2f0a-422c-aa8f-21790bd16024  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:47.806 INFO log_util.__aexit__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d POST /v1/tool/deepsearch cost=[39 ms]
2025-08-10 00:47:47.809 INFO log_util.__aexit__ 805ae152-2f0a-422c-aa8f-21790bd16024 POST /v1/tool/deepsearch cost=[40 ms]
2025-08-10 00:47:54.263 INFO log_util.__aenter__ 2302b52d-0a3a-408d-bf8b-2044785f807a POST /v1/tool/deepsearch start...
2025-08-10 00:47:54.263 INFO log_util.__aenter__ 62283372-0980-4911-93e4-8999b7b22038 POST /v1/tool/deepsearch start...
2025-08-10 00:47:54.265 INFO middleware_util.custom_route_handler 2302b52d-0a3a-408d-bf8b-2044785f807a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李安检注意事项","request_id":"geniesession-1754757551055-129:1754757776162-4267:tdsl1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:54.266 INFO middleware_util.custom_route_handler 62283372-0980-4911-93e4-8999b7b22038 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地特色活动","request_id":"geniesession-1754757551055-129:1754757776162-4267:b7cez","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:54.267 INFO log_util.__enter__ 2302b52d-0a3a-408d-bf8b-2044785f807a  run start...
2025-08-10 00:47:54.267 INFO log_util.__exit__ 2302b52d-0a3a-408d-bf8b-2044785f807a  run cost=[0 ms]
2025-08-10 00:47:54.267 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:tdsl1 第 1 轮深度搜索...
2025-08-10 00:47:54.267 INFO log_util.__aenter__ 2302b52d-0a3a-408d-bf8b-2044785f807a  query_decompose start...
2025-08-10 00:47:54.269 ERROR log_util.__aexit__ 2302b52d-0a3a-408d-bf8b-2044785f807a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:54.272 INFO log_util.__enter__ 62283372-0980-4911-93e4-8999b7b22038  run start...
2025-08-10 00:47:54.272 INFO log_util.__exit__ 62283372-0980-4911-93e4-8999b7b22038  run cost=[0 ms]
2025-08-10 00:47:54.273 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:b7cez 第 1 轮深度搜索...
2025-08-10 00:47:54.274 INFO log_util.__aenter__ 62283372-0980-4911-93e4-8999b7b22038  query_decompose start...
2025-08-10 00:47:54.276 ERROR log_util.__aexit__ 62283372-0980-4911-93e4-8999b7b22038  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:54.277 INFO log_util.__aexit__ 2302b52d-0a3a-408d-bf8b-2044785f807a POST /v1/tool/deepsearch cost=[13 ms]
2025-08-10 00:47:54.281 INFO log_util.__aexit__ 62283372-0980-4911-93e4-8999b7b22038 POST /v1/tool/deepsearch cost=[17 ms]
2025-08-10 00:48:00.338 INFO log_util.__aenter__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f POST /v1/tool/deepsearch start...
2025-08-10 00:48:00.339 INFO log_util.__aenter__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d POST /v1/tool/deepsearch start...
2025-08-10 00:48:00.341 INFO middleware_util.custom_route_handler c60e07c3-855a-44f9-bb04-fd7d493a0f1f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地节日庆典","request_id":"geniesession-1754757551055-129:1754757776162-4267:89n2l","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:48:00.342 INFO middleware_util.custom_route_handler 3d76031f-8908-4c7b-97b4-0d1dc388e00d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李打包建议","request_id":"geniesession-1754757551055-129:1754757776162-4267:vtrym","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:48:00.343 INFO log_util.__enter__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  run start...
2025-08-10 00:48:00.344 INFO log_util.__exit__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  run cost=[0 ms]
2025-08-10 00:48:00.346 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:89n2l 第 1 轮深度搜索...
2025-08-10 00:48:00.347 INFO log_util.__aenter__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  query_decompose start...
2025-08-10 00:48:00.349 ERROR log_util.__aexit__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:48:00.350 INFO log_util.__enter__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  run start...
2025-08-10 00:48:00.354 INFO log_util.__exit__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  run cost=[3 ms]
2025-08-10 00:48:00.355 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vtrym 第 1 轮深度搜索...
2025-08-10 00:48:00.356 INFO log_util.__aenter__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  query_decompose start...
2025-08-10 00:48:00.358 ERROR log_util.__aexit__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:48:00.359 INFO log_util.__aexit__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:48:00.362 INFO log_util.__aexit__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 01:34:22.500 INFO log_util.__aenter__ adacbf79-27d1-40f5-a059-5bc5391f0e34 POST /v1/tool/deepsearch start...
2025-08-10 01:34:22.503 INFO middleware_util.custom_route_handler adacbf79-27d1-40f5-a059-5bc5391f0e34 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛最适合去的天气 2025年8月","request_id":"geniesession-1754760846817-8847:1754760846837-5145:aigzq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:22.507 INFO log_util.__enter__ adacbf79-27d1-40f5-a059-5bc5391f0e34  run start...
2025-08-10 01:34:22.507 INFO log_util.__exit__ adacbf79-27d1-40f5-a059-5bc5391f0e34  run cost=[0 ms]
2025-08-10 01:34:22.508 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:aigzq 第 1 轮深度搜索...
2025-08-10 01:34:22.509 INFO log_util.__aenter__ adacbf79-27d1-40f5-a059-5bc5391f0e34  query_decompose start...
2025-08-10 01:34:22.518 INFO log_util.__enter__ adacbf79-27d1-40f5-a059-5bc5391f0e34 enter ask_llm start...
2025-08-10 01:34:22.520 INFO log_util.__exit__ adacbf79-27d1-40f5-a059-5bc5391f0e34 enter ask_llm cost=[2 ms]
2025-08-10 01:34:22.537 ERROR log_util.__aexit__ adacbf79-27d1-40f5-a059-5bc5391f0e34  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:22.541 INFO log_util.__aexit__ adacbf79-27d1-40f5-a059-5bc5391f0e34 POST /v1/tool/deepsearch cost=[40 ms]
2025-08-10 01:34:30.934 INFO log_util.__aenter__ 94a849d7-7db5-494a-a21a-502af663be79 POST /v1/tool/deepsearch start...
2025-08-10 01:34:30.936 INFO middleware_util.custom_route_handler 94a849d7-7db5-494a-a21a-502af663be79 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛2025年8月天气预报","request_id":"geniesession-1754760846817-8847:1754760846837-5145:rpoho","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:30.938 INFO log_util.__enter__ 94a849d7-7db5-494a-a21a-502af663be79  run start...
2025-08-10 01:34:30.938 INFO log_util.__exit__ 94a849d7-7db5-494a-a21a-502af663be79  run cost=[0 ms]
2025-08-10 01:34:30.938 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:rpoho 第 1 轮深度搜索...
2025-08-10 01:34:30.939 INFO log_util.__aenter__ 94a849d7-7db5-494a-a21a-502af663be79  query_decompose start...
2025-08-10 01:34:30.949 INFO log_util.__enter__ 94a849d7-7db5-494a-a21a-502af663be79 enter ask_llm start...
2025-08-10 01:34:30.952 INFO log_util.__exit__ 94a849d7-7db5-494a-a21a-502af663be79 enter ask_llm cost=[3 ms]
2025-08-10 01:34:30.970 ERROR log_util.__aexit__ 94a849d7-7db5-494a-a21a-502af663be79  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:30.972 INFO log_util.__aexit__ 94a849d7-7db5-494a-a21a-502af663be79 POST /v1/tool/deepsearch cost=[37 ms]
2025-08-10 01:34:39.211 INFO log_util.__aenter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a POST /v1/tool/deepsearch start...
2025-08-10 01:34:39.212 INFO middleware_util.custom_route_handler 14ae021f-ed11-435b-bfe0-4bfb88f0778a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛最佳旅游季节和天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:nw24e","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:39.213 INFO log_util.__enter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  run start...
2025-08-10 01:34:39.213 INFO log_util.__exit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  run cost=[0 ms]
2025-08-10 01:34:39.213 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:nw24e 第 1 轮深度搜索...
2025-08-10 01:34:39.213 INFO log_util.__aenter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  query_decompose start...
2025-08-10 01:34:39.218 INFO log_util.__enter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a enter ask_llm start...
2025-08-10 01:34:39.218 INFO log_util.__exit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a enter ask_llm cost=[0 ms]
2025-08-10 01:34:39.224 ERROR log_util.__aexit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:39.224 INFO log_util.__aexit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a POST /v1/tool/deepsearch cost=[12 ms]
2025-08-10 01:34:45.929 INFO log_util.__aenter__ 8f85da85-6461-4702-96a7-86847418e3ac POST /v1/tool/deepsearch start...
2025-08-10 01:34:45.930 INFO middleware_util.custom_route_handler 8f85da85-6461-4702-96a7-86847418e3ac POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳季节和天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:r90g2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:45.930 INFO log_util.__enter__ 8f85da85-6461-4702-96a7-86847418e3ac  run start...
2025-08-10 01:34:45.931 INFO log_util.__exit__ 8f85da85-6461-4702-96a7-86847418e3ac  run cost=[0 ms]
2025-08-10 01:34:45.931 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:r90g2 第 1 轮深度搜索...
2025-08-10 01:34:45.931 INFO log_util.__aenter__ 8f85da85-6461-4702-96a7-86847418e3ac  query_decompose start...
2025-08-10 01:34:45.933 INFO log_util.__enter__ 8f85da85-6461-4702-96a7-86847418e3ac enter ask_llm start...
2025-08-10 01:34:45.934 INFO log_util.__exit__ 8f85da85-6461-4702-96a7-86847418e3ac enter ask_llm cost=[1 ms]
2025-08-10 01:34:45.938 ERROR log_util.__aexit__ 8f85da85-6461-4702-96a7-86847418e3ac  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:45.939 INFO log_util.__aexit__ 8f85da85-6461-4702-96a7-86847418e3ac POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:34:53.624 INFO log_util.__aenter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e POST /v1/tool/deepsearch start...
2025-08-10 01:34:53.624 INFO middleware_util.custom_route_handler 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳月份和天气特点","request_id":"geniesession-1754760846817-8847:1754760846837-5145:vx6hi","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:53.625 INFO log_util.__enter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  run start...
2025-08-10 01:34:53.625 INFO log_util.__exit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  run cost=[0 ms]
2025-08-10 01:34:53.625 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:vx6hi 第 1 轮深度搜索...
2025-08-10 01:34:53.626 INFO log_util.__aenter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  query_decompose start...
2025-08-10 01:34:53.628 INFO log_util.__enter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e enter ask_llm start...
2025-08-10 01:34:53.628 INFO log_util.__exit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e enter ask_llm cost=[0 ms]
2025-08-10 01:34:53.633 ERROR log_util.__aexit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:53.634 INFO log_util.__aexit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:00.497 INFO log_util.__aenter__ c327b3ad-b30c-4db2-a42b-4dced6712876 POST /v1/tool/deepsearch start...
2025-08-10 01:35:00.498 INFO middleware_util.custom_route_handler c327b3ad-b30c-4db2-a42b-4dced6712876 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛气候特点及最佳旅游时间","request_id":"geniesession-1754760846817-8847:1754760846837-5145:u5vr1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:00.499 INFO log_util.__enter__ c327b3ad-b30c-4db2-a42b-4dced6712876  run start...
2025-08-10 01:35:00.499 INFO log_util.__exit__ c327b3ad-b30c-4db2-a42b-4dced6712876  run cost=[0 ms]
2025-08-10 01:35:00.500 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:u5vr1 第 1 轮深度搜索...
2025-08-10 01:35:00.500 INFO log_util.__aenter__ c327b3ad-b30c-4db2-a42b-4dced6712876  query_decompose start...
2025-08-10 01:35:00.503 INFO log_util.__enter__ c327b3ad-b30c-4db2-a42b-4dced6712876 enter ask_llm start...
2025-08-10 01:35:00.504 INFO log_util.__exit__ c327b3ad-b30c-4db2-a42b-4dced6712876 enter ask_llm cost=[0 ms]
2025-08-10 01:35:00.510 ERROR log_util.__aexit__ c327b3ad-b30c-4db2-a42b-4dced6712876  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:00.511 INFO log_util.__aexit__ c327b3ad-b30c-4db2-a42b-4dced6712876 POST /v1/tool/deepsearch cost=[13 ms]
2025-08-10 01:35:08.013 INFO log_util.__aenter__ 86a278ae-3a47-401a-b139-0676fde3eeaf POST /v1/tool/deepsearch start...
2025-08-10 01:35:08.013 INFO middleware_util.custom_route_handler 86a278ae-3a47-401a-b139-0676fde3eeaf POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节推荐","request_id":"geniesession-1754760846817-8847:1754760846837-5145:su1pn","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:08.014 INFO log_util.__enter__ 86a278ae-3a47-401a-b139-0676fde3eeaf  run start...
2025-08-10 01:35:08.014 INFO log_util.__exit__ 86a278ae-3a47-401a-b139-0676fde3eeaf  run cost=[0 ms]
2025-08-10 01:35:08.014 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:su1pn 第 1 轮深度搜索...
2025-08-10 01:35:08.015 INFO log_util.__aenter__ 86a278ae-3a47-401a-b139-0676fde3eeaf  query_decompose start...
2025-08-10 01:35:08.017 INFO log_util.__enter__ 86a278ae-3a47-401a-b139-0676fde3eeaf enter ask_llm start...
2025-08-10 01:35:08.017 INFO log_util.__exit__ 86a278ae-3a47-401a-b139-0676fde3eeaf enter ask_llm cost=[0 ms]
2025-08-10 01:35:08.021 ERROR log_util.__aexit__ 86a278ae-3a47-401a-b139-0676fde3eeaf  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:08.022 INFO log_util.__aexit__ 86a278ae-3a47-401a-b139-0676fde3eeaf POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:13.146 INFO log_util.__aenter__ 0c338dcc-9dae-458b-aa90-533b05291612 POST /v1/tool/deepsearch start...
2025-08-10 01:35:13.147 INFO middleware_util.custom_route_handler 0c338dcc-9dae-458b-aa90-533b05291612 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间","request_id":"geniesession-1754760846817-8847:1754760846837-5145:39rm9","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:13.147 INFO log_util.__enter__ 0c338dcc-9dae-458b-aa90-533b05291612  run start...
2025-08-10 01:35:13.147 INFO log_util.__exit__ 0c338dcc-9dae-458b-aa90-533b05291612  run cost=[0 ms]
2025-08-10 01:35:13.148 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:39rm9 第 1 轮深度搜索...
2025-08-10 01:35:13.148 INFO log_util.__aenter__ 0c338dcc-9dae-458b-aa90-533b05291612  query_decompose start...
2025-08-10 01:35:13.150 INFO log_util.__enter__ 0c338dcc-9dae-458b-aa90-533b05291612 enter ask_llm start...
2025-08-10 01:35:13.151 INFO log_util.__exit__ 0c338dcc-9dae-458b-aa90-533b05291612 enter ask_llm cost=[1 ms]
2025-08-10 01:35:13.156 ERROR log_util.__aexit__ 0c338dcc-9dae-458b-aa90-533b05291612  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:13.157 INFO log_util.__aexit__ 0c338dcc-9dae-458b-aa90-533b05291612 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:19.067 INFO log_util.__aenter__ 47a200d9-11e0-45e7-b20f-792afb1abab1 POST /v1/tool/deepsearch start...
2025-08-10 01:35:19.068 INFO middleware_util.custom_route_handler 47a200d9-11e0-45e7-b20f-792afb1abab1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游攻略 天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:wplhi","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:19.069 INFO log_util.__enter__ 47a200d9-11e0-45e7-b20f-792afb1abab1  run start...
2025-08-10 01:35:19.069 INFO log_util.__exit__ 47a200d9-11e0-45e7-b20f-792afb1abab1  run cost=[0 ms]
2025-08-10 01:35:19.069 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:wplhi 第 1 轮深度搜索...
2025-08-10 01:35:19.069 INFO log_util.__aenter__ 47a200d9-11e0-45e7-b20f-792afb1abab1  query_decompose start...
2025-08-10 01:35:19.072 INFO log_util.__enter__ 47a200d9-11e0-45e7-b20f-792afb1abab1 enter ask_llm start...
2025-08-10 01:35:19.072 INFO log_util.__exit__ 47a200d9-11e0-45e7-b20f-792afb1abab1 enter ask_llm cost=[0 ms]
2025-08-10 01:35:19.077 ERROR log_util.__aexit__ 47a200d9-11e0-45e7-b20f-792afb1abab1  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:19.078 INFO log_util.__aexit__ 47a200d9-11e0-45e7-b20f-792afb1abab1 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 01:35:24.355 INFO log_util.__aenter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 POST /v1/tool/deepsearch start...
2025-08-10 01:35:24.357 INFO middleware_util.custom_route_handler 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳季节","request_id":"geniesession-1754760846817-8847:1754760846837-5145:xhno5","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:24.357 INFO log_util.__enter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  run start...
2025-08-10 01:35:24.357 INFO log_util.__exit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  run cost=[0 ms]
2025-08-10 01:35:24.358 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:xhno5 第 1 轮深度搜索...
2025-08-10 01:35:24.358 INFO log_util.__aenter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  query_decompose start...
2025-08-10 01:35:24.360 INFO log_util.__enter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 enter ask_llm start...
2025-08-10 01:35:24.360 INFO log_util.__exit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 enter ask_llm cost=[0 ms]
2025-08-10 01:35:24.365 ERROR log_util.__aexit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:24.366 INFO log_util.__aexit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 01:35:29.253 INFO log_util.__aenter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 POST /v1/tool/deepsearch start...
2025-08-10 01:35:29.254 INFO middleware_util.custom_route_handler 6cea7361-d3f5-49fb-ac2c-b802a1fde252 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛气候和旅游季节","request_id":"geniesession-1754760846817-8847:1754760846837-5145:hw1p1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:29.255 INFO log_util.__enter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  run start...
2025-08-10 01:35:29.255 INFO log_util.__exit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  run cost=[0 ms]
2025-08-10 01:35:29.255 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:hw1p1 第 1 轮深度搜索...
2025-08-10 01:35:29.255 INFO log_util.__aenter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  query_decompose start...
2025-08-10 01:35:29.258 INFO log_util.__enter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 enter ask_llm start...
2025-08-10 01:35:29.258 INFO log_util.__exit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 enter ask_llm cost=[0 ms]
2025-08-10 01:35:29.263 ERROR log_util.__aexit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:29.263 INFO log_util.__aexit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:34.092 INFO log_util.__aenter__ a55c6741-5ebe-442c-8964-d34d4238c39d POST /v1/tool/deepsearch start...
2025-08-10 01:35:34.093 INFO middleware_util.custom_route_handler a55c6741-5ebe-442c-8964-d34d4238c39d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间及天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:6osa0","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:34.093 INFO log_util.__enter__ a55c6741-5ebe-442c-8964-d34d4238c39d  run start...
2025-08-10 01:35:34.093 INFO log_util.__exit__ a55c6741-5ebe-442c-8964-d34d4238c39d  run cost=[0 ms]
2025-08-10 01:35:34.094 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:6osa0 第 1 轮深度搜索...
2025-08-10 01:35:34.094 INFO log_util.__aenter__ a55c6741-5ebe-442c-8964-d34d4238c39d  query_decompose start...
2025-08-10 01:35:34.096 INFO log_util.__enter__ a55c6741-5ebe-442c-8964-d34d4238c39d enter ask_llm start...
2025-08-10 01:35:34.096 INFO log_util.__exit__ a55c6741-5ebe-442c-8964-d34d4238c39d enter ask_llm cost=[0 ms]
2025-08-10 01:35:34.101 ERROR log_util.__aexit__ a55c6741-5ebe-442c-8964-d34d4238c39d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:34.102 INFO log_util.__aexit__ a55c6741-5ebe-442c-8964-d34d4238c39d POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:39.115 INFO log_util.__aenter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 POST /v1/tool/deepsearch start...
2025-08-10 01:35:39.116 INFO middleware_util.custom_route_handler cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节推荐及天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:4l80r","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:39.117 INFO log_util.__enter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  run start...
2025-08-10 01:35:39.117 INFO log_util.__exit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  run cost=[0 ms]
2025-08-10 01:35:39.118 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:4l80r 第 1 轮深度搜索...
2025-08-10 01:35:39.118 INFO log_util.__aenter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  query_decompose start...
2025-08-10 01:35:39.120 INFO log_util.__enter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 enter ask_llm start...
2025-08-10 01:35:39.121 INFO log_util.__exit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 enter ask_llm cost=[0 ms]
2025-08-10 01:35:39.125 ERROR log_util.__aexit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:39.125 INFO log_util.__aexit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:44.327 INFO log_util.__aenter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f POST /v1/tool/deepsearch start...
2025-08-10 01:35:44.327 INFO middleware_util.custom_route_handler 778bb32c-f885-4452-9608-7b4d9a2cf63f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳月份","request_id":"geniesession-1754760846817-8847:1754760846837-5145:hmfmb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:44.328 INFO log_util.__enter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  run start...
2025-08-10 01:35:44.328 INFO log_util.__exit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  run cost=[0 ms]
2025-08-10 01:35:44.328 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:hmfmb 第 1 轮深度搜索...
2025-08-10 01:35:44.328 INFO log_util.__aenter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  query_decompose start...
2025-08-10 01:35:44.331 INFO log_util.__enter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f enter ask_llm start...
2025-08-10 01:35:44.331 INFO log_util.__exit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f enter ask_llm cost=[0 ms]
2025-08-10 01:35:44.335 ERROR log_util.__aexit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:44.336 INFO log_util.__aexit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:49.660 INFO log_util.__aenter__ a9e95911-5809-4d68-b6a2-98064e635d8b POST /v1/tool/deepsearch start...
2025-08-10 01:35:49.661 INFO middleware_util.custom_route_handler a9e95911-5809-4d68-b6a2-98064e635d8b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节和天气特点","request_id":"geniesession-1754760846817-8847:1754760846837-5145:f26ho","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:49.662 INFO log_util.__enter__ a9e95911-5809-4d68-b6a2-98064e635d8b  run start...
2025-08-10 01:35:49.662 INFO log_util.__exit__ a9e95911-5809-4d68-b6a2-98064e635d8b  run cost=[0 ms]
2025-08-10 01:35:49.662 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:f26ho 第 1 轮深度搜索...
2025-08-10 01:35:49.662 INFO log_util.__aenter__ a9e95911-5809-4d68-b6a2-98064e635d8b  query_decompose start...
2025-08-10 01:35:49.665 INFO log_util.__enter__ a9e95911-5809-4d68-b6a2-98064e635d8b enter ask_llm start...
2025-08-10 01:35:49.665 INFO log_util.__exit__ a9e95911-5809-4d68-b6a2-98064e635d8b enter ask_llm cost=[0 ms]
2025-08-10 01:35:49.669 ERROR log_util.__aexit__ a9e95911-5809-4d68-b6a2-98064e635d8b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:49.670 INFO log_util.__aexit__ a9e95911-5809-4d68-b6a2-98064e635d8b POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:55.297 INFO log_util.__aenter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f POST /v1/tool/deepsearch start...
2025-08-10 01:35:55.298 INFO middleware_util.custom_route_handler ea1b79d2-cc80-497f-8c4d-5081c3c2236f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳季节和天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:91una","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:55.299 INFO log_util.__enter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  run start...
2025-08-10 01:35:55.299 INFO log_util.__exit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  run cost=[0 ms]
2025-08-10 01:35:55.299 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:91una 第 1 轮深度搜索...
2025-08-10 01:35:55.300 INFO log_util.__aenter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  query_decompose start...
2025-08-10 01:35:55.302 INFO log_util.__enter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f enter ask_llm start...
2025-08-10 01:35:55.302 INFO log_util.__exit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f enter ask_llm cost=[0 ms]
2025-08-10 01:35:55.307 ERROR log_util.__aexit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:55.308 INFO log_util.__aexit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:36:00.481 INFO log_util.__aenter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 POST /v1/tool/deepsearch start...
2025-08-10 01:36:00.482 INFO middleware_util.custom_route_handler 3a9885fb-8800-4398-b217-ba95d7bc9c00 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游天气指南","request_id":"geniesession-1754760846817-8847:1754760846837-5145:4ml7p","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:36:00.482 INFO log_util.__enter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  run start...
2025-08-10 01:36:00.483 INFO log_util.__exit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  run cost=[0 ms]
2025-08-10 01:36:00.483 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:4ml7p 第 1 轮深度搜索...
2025-08-10 01:36:00.483 INFO log_util.__aenter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  query_decompose start...
2025-08-10 01:36:00.485 INFO log_util.__enter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 enter ask_llm start...
2025-08-10 01:36:00.486 INFO log_util.__exit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 enter ask_llm cost=[1 ms]
2025-08-10 01:36:00.490 ERROR log_util.__aexit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:36:00.490 INFO log_util.__aexit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:36:05.557 INFO log_util.__aenter__ e49bae8b-132a-4c32-bd00-b80a76e24297 POST /v1/tool/deepsearch start...
2025-08-10 01:36:05.558 INFO middleware_util.custom_route_handler e49bae8b-132a-4c32-bd00-b80a76e24297 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:bv3hj","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:36:05.558 INFO log_util.__enter__ e49bae8b-132a-4c32-bd00-b80a76e24297  run start...
2025-08-10 01:36:05.559 INFO log_util.__exit__ e49bae8b-132a-4c32-bd00-b80a76e24297  run cost=[0 ms]
2025-08-10 01:36:05.559 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:bv3hj 第 1 轮深度搜索...
2025-08-10 01:36:05.559 INFO log_util.__aenter__ e49bae8b-132a-4c32-bd00-b80a76e24297  query_decompose start...
2025-08-10 01:36:05.562 INFO log_util.__enter__ e49bae8b-132a-4c32-bd00-b80a76e24297 enter ask_llm start...
2025-08-10 01:36:05.562 INFO log_util.__exit__ e49bae8b-132a-4c32-bd00-b80a76e24297 enter ask_llm cost=[0 ms]
2025-08-10 01:36:05.566 ERROR log_util.__aexit__ e49bae8b-132a-4c32-bd00-b80a76e24297  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:36:05.567 INFO log_util.__aexit__ e49bae8b-132a-4c32-bd00-b80a76e24297 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:37:05.313 INFO log_util.__aenter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 POST /v1/tool/deepsearch start...
2025-08-10 01:37:05.314 INFO middleware_util.custom_route_handler 2b90d797-42f5-4e71-9977-af6a44e93bd5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳天气月份","request_id":"geniesession-1754760846817-8847:1754760846837-5145:v4ke7","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:37:05.316 INFO log_util.__enter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  run start...
2025-08-10 01:37:05.316 INFO log_util.__exit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  run cost=[0 ms]
2025-08-10 01:37:05.316 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:v4ke7 第 1 轮深度搜索...
2025-08-10 01:37:05.316 INFO log_util.__aenter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  query_decompose start...
2025-08-10 01:37:05.320 INFO log_util.__enter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 enter ask_llm start...
2025-08-10 01:37:05.320 INFO log_util.__exit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 enter ask_llm cost=[0 ms]
2025-08-10 01:37:05.329 ERROR log_util.__aexit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:37:05.330 INFO log_util.__aexit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 POST /v1/tool/deepsearch cost=[16 ms]
2025-08-10 01:37:10.527 INFO log_util.__aenter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b POST /v1/tool/deepsearch start...
2025-08-10 01:37:10.529 INFO middleware_util.custom_route_handler 6819e5b1-9c56-496a-b9f6-436c9cebc62b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节和天气特点","request_id":"geniesession-1754760846817-8847:1754760846837-5145:gecz4","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:37:10.530 INFO log_util.__enter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  run start...
2025-08-10 01:37:10.530 INFO log_util.__exit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  run cost=[0 ms]
2025-08-10 01:37:10.530 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:gecz4 第 1 轮深度搜索...
2025-08-10 01:37:10.530 INFO log_util.__aenter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  query_decompose start...
2025-08-10 01:37:10.533 INFO log_util.__enter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b enter ask_llm start...
2025-08-10 01:37:10.534 INFO log_util.__exit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b enter ask_llm cost=[1 ms]
2025-08-10 01:37:10.540 ERROR log_util.__aexit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:37:10.541 INFO log_util.__aexit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b POST /v1/tool/deepsearch cost=[14 ms]
2025-08-10 01:44:19.537 INFO log_util.__aenter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c POST /v1/tool/report start...
2025-08-10 01:44:19.538 INFO middleware_util.custom_route_handler c9db99ed-bbb0-46be-aaa0-3ca808a37d9c POST /v1/tool/report body={"contentStream":true,"fileDescription":"巴厘岛最佳旅游时间分析报告","fileName":"巴厘岛最佳旅游时间.html","fileNames":[],"fileType":"html","query":"巴厘岛什么时候去合适","requestId":"geniesession-1754761429065-3169:1754761429086-9933","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并分析巴厘岛的最佳旅游时间，包括旱季和雨季的特点，以及推荐的活动和注意事项。"}
2025-08-10 01:44:19.540 INFO log_util.__enter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c enter report start...
2025-08-10 01:44:19.540 INFO log_util.__exit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c enter report cost=[0 ms]
2025-08-10 01:44:19.540 INFO log_util.__enter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c enter html_report start...
2025-08-10 01:44:19.540 INFO log_util.__exit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c enter html_report cost=[0 ms]
2025-08-10 01:44:19.541 INFO log_util.__aenter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c  download_all_files start...
2025-08-10 01:44:19.541 INFO log_util.__aexit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c  download_all_files cost=[0 ms]
2025-08-10 01:44:19.541 INFO log_util.__enter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c  truncate_files start...
2025-08-10 01:44:19.541 INFO log_util.__exit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c  truncate_files cost=[0 ms]
2025-08-10 01:44:19.541 INFO log_util.__enter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c  truncate_files start...
2025-08-10 01:44:19.541 INFO log_util.__exit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c  truncate_files cost=[0 ms]
2025-08-10 01:44:19.551 INFO log_util.__enter__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c enter ask_llm start...
2025-08-10 01:44:19.551 INFO log_util.__exit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c enter ask_llm cost=[0 ms]
2025-08-10 01:44:19.558 INFO log_util.__aexit__ c9db99ed-bbb0-46be-aaa0-3ca808a37d9c POST /v1/tool/report cost=[21 ms]
2025-08-10 01:44:29.093 INFO log_util.__aenter__ 85cec22b-4c11-4254-98f2-aed725f00873 POST /v1/tool/deepsearch start...
2025-08-10 01:44:29.094 INFO middleware_util.custom_route_handler 85cec22b-4c11-4254-98f2-aed725f00873 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛最佳旅游时间 旱季和雨季特点 推荐活动","request_id":"geniesession-1754761429065-3169:1754761429086-9933:qi3kb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:44:29.095 INFO log_util.__enter__ 85cec22b-4c11-4254-98f2-aed725f00873  run start...
2025-08-10 01:44:29.095 INFO log_util.__exit__ 85cec22b-4c11-4254-98f2-aed725f00873  run cost=[0 ms]
2025-08-10 01:44:29.096 INFO deepsearch.run geniesession-1754761429065-3169:1754761429086-9933:qi3kb 第 1 轮深度搜索...
2025-08-10 01:44:29.096 INFO log_util.__aenter__ 85cec22b-4c11-4254-98f2-aed725f00873  query_decompose start...
2025-08-10 01:44:29.099 INFO log_util.__enter__ 85cec22b-4c11-4254-98f2-aed725f00873 enter ask_llm start...
2025-08-10 01:44:29.099 INFO log_util.__exit__ 85cec22b-4c11-4254-98f2-aed725f00873 enter ask_llm cost=[0 ms]
2025-08-10 01:44:29.104 ERROR log_util.__aexit__ 85cec22b-4c11-4254-98f2-aed725f00873  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:44:29.105 INFO log_util.__aexit__ 85cec22b-4c11-4254-98f2-aed725f00873 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 01:44:37.135 INFO log_util.__aenter__ a7955670-e0f7-4112-a585-0784220c7acc POST /v1/tool/deepsearch start...
2025-08-10 01:44:37.136 INFO middleware_util.custom_route_handler a7955670-e0f7-4112-a585-0784220c7acc POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间 旱季雨季月份 气候特点 推荐活动","request_id":"geniesession-1754761429065-3169:1754761429086-9933:5g9ca","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:44:37.137 INFO log_util.__enter__ a7955670-e0f7-4112-a585-0784220c7acc  run start...
2025-08-10 01:44:37.137 INFO log_util.__exit__ a7955670-e0f7-4112-a585-0784220c7acc  run cost=[0 ms]
2025-08-10 01:44:37.137 INFO deepsearch.run geniesession-1754761429065-3169:1754761429086-9933:5g9ca 第 1 轮深度搜索...
2025-08-10 01:44:37.137 INFO log_util.__aenter__ a7955670-e0f7-4112-a585-0784220c7acc  query_decompose start...
2025-08-10 01:44:37.140 INFO log_util.__enter__ a7955670-e0f7-4112-a585-0784220c7acc enter ask_llm start...
2025-08-10 01:44:37.140 INFO log_util.__exit__ a7955670-e0f7-4112-a585-0784220c7acc enter ask_llm cost=[0 ms]
2025-08-10 01:44:37.143 ERROR log_util.__aexit__ a7955670-e0f7-4112-a585-0784220c7acc  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:44:37.145 INFO log_util.__aexit__ a7955670-e0f7-4112-a585-0784220c7acc POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:44:44.935 INFO log_util.__aenter__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90 POST /v1/tool/deepsearch start...
2025-08-10 01:44:44.936 INFO middleware_util.custom_route_handler 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节 旱季雨季月份 气候特点 最佳旅游时间","request_id":"geniesession-1754761429065-3169:1754761429086-9933:75v0h","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:44:44.937 INFO log_util.__enter__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90  run start...
2025-08-10 01:44:44.937 INFO log_util.__exit__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90  run cost=[0 ms]
2025-08-10 01:44:44.937 INFO deepsearch.run geniesession-1754761429065-3169:1754761429086-9933:75v0h 第 1 轮深度搜索...
2025-08-10 01:44:44.937 INFO log_util.__aenter__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90  query_decompose start...
2025-08-10 01:44:44.940 INFO log_util.__enter__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90 enter ask_llm start...
2025-08-10 01:44:44.940 INFO log_util.__exit__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90 enter ask_llm cost=[0 ms]
2025-08-10 01:44:44.945 ERROR log_util.__aexit__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:44:44.945 INFO log_util.__aexit__ 7a7ad2c9-beb6-4bab-b62f-fe52c771fa90 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:49:21.639 INFO log_util.__aenter__ 6c075741-09cf-4e0b-8969-37b483e49ddb POST /v1/tool/report start...
2025-08-10 01:49:21.641 INFO middleware_util.custom_route_handler 6c075741-09cf-4e0b-8969-37b483e49ddb POST /v1/tool/report body={"contentStream":true,"fileDescription":"巴厘岛最佳旅游时间分析报告","fileName":"巴厘岛最佳旅游时间.html","fileNames":[],"fileType":"html","query":"巴厘岛什么时候去合适","requestId":"geniesession-1754761740810-297:1754761740825-4230","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并总结巴厘岛的最佳旅游时间，包括气候特点、旅游旺季和淡季、推荐月份等信息。"}
2025-08-10 01:49:21.645 INFO log_util.__enter__ 6c075741-09cf-4e0b-8969-37b483e49ddb enter report start...
2025-08-10 01:49:21.646 INFO log_util.__exit__ 6c075741-09cf-4e0b-8969-37b483e49ddb enter report cost=[1 ms]
2025-08-10 01:49:21.651 INFO log_util.__enter__ 6c075741-09cf-4e0b-8969-37b483e49ddb enter html_report start...
2025-08-10 01:49:21.655 INFO log_util.__exit__ 6c075741-09cf-4e0b-8969-37b483e49ddb enter html_report cost=[3 ms]
2025-08-10 01:49:21.656 INFO log_util.__aenter__ 6c075741-09cf-4e0b-8969-37b483e49ddb  download_all_files start...
2025-08-10 01:49:21.657 INFO log_util.__aexit__ 6c075741-09cf-4e0b-8969-37b483e49ddb  download_all_files cost=[1 ms]
2025-08-10 01:49:21.658 INFO log_util.__enter__ 6c075741-09cf-4e0b-8969-37b483e49ddb  truncate_files start...
2025-08-10 01:49:21.658 INFO log_util.__exit__ 6c075741-09cf-4e0b-8969-37b483e49ddb  truncate_files cost=[0 ms]
2025-08-10 01:49:21.659 INFO log_util.__enter__ 6c075741-09cf-4e0b-8969-37b483e49ddb  truncate_files start...
2025-08-10 01:49:21.662 INFO log_util.__exit__ 6c075741-09cf-4e0b-8969-37b483e49ddb  truncate_files cost=[3 ms]
2025-08-10 01:49:21.693 INFO log_util.__enter__ 6c075741-09cf-4e0b-8969-37b483e49ddb enter ask_llm start...
2025-08-10 01:49:21.693 INFO log_util.__exit__ 6c075741-09cf-4e0b-8969-37b483e49ddb enter ask_llm cost=[0 ms]
2025-08-10 01:49:21.724 INFO log_util.__aexit__ 6c075741-09cf-4e0b-8969-37b483e49ddb POST /v1/tool/report cost=[85 ms]
2025-08-10 01:49:34.502 INFO log_util.__aenter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 POST /v1/tool/report start...
2025-08-10 01:49:34.503 INFO middleware_util.custom_route_handler 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 POST /v1/tool/report body={"contentStream":true,"fileDescription":"巴厘岛最佳旅游时间分析报告","fileName":"巴厘岛最佳旅游时间.html","fileNames":[],"fileType":"html","query":"巴厘岛什么时候去合适","requestId":"geniesession-1754761740810-297:1754761740825-4230","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并总结巴厘岛的最佳旅游时间，包括气候特点、旅游旺季和淡季、推荐月份等信息。"}
2025-08-10 01:49:34.504 INFO log_util.__enter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 enter report start...
2025-08-10 01:49:34.504 INFO log_util.__exit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 enter report cost=[0 ms]
2025-08-10 01:49:34.505 INFO log_util.__enter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 enter html_report start...
2025-08-10 01:49:34.505 INFO log_util.__exit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 enter html_report cost=[0 ms]
2025-08-10 01:49:34.505 INFO log_util.__aenter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632  download_all_files start...
2025-08-10 01:49:34.505 INFO log_util.__aexit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632  download_all_files cost=[0 ms]
2025-08-10 01:49:34.505 INFO log_util.__enter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632  truncate_files start...
2025-08-10 01:49:34.506 INFO log_util.__exit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632  truncate_files cost=[1 ms]
2025-08-10 01:49:34.506 INFO log_util.__enter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632  truncate_files start...
2025-08-10 01:49:34.506 INFO log_util.__exit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632  truncate_files cost=[0 ms]
2025-08-10 01:49:34.513 INFO log_util.__enter__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 enter ask_llm start...
2025-08-10 01:49:34.513 INFO log_util.__exit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 enter ask_llm cost=[0 ms]
2025-08-10 01:49:34.518 INFO log_util.__aexit__ 7bcf4cd6-5579-4ae9-b32f-80d5938a2632 POST /v1/tool/report cost=[15 ms]
2025-08-10 01:49:43.736 INFO log_util.__aenter__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599 POST /v1/tool/deepsearch start...
2025-08-10 01:49:43.737 INFO middleware_util.custom_route_handler 0b6c3722-6dae-4bc2-9054-6d7a57e19599 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛最佳旅游时间 气候特点 推荐月份","request_id":"geniesession-1754761740810-297:1754761740825-4230:4a7rz","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:49:43.738 INFO log_util.__enter__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599  run start...
2025-08-10 01:49:43.739 INFO log_util.__exit__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599  run cost=[0 ms]
2025-08-10 01:49:43.739 INFO deepsearch.run geniesession-1754761740810-297:1754761740825-4230:4a7rz 第 1 轮深度搜索...
2025-08-10 01:49:43.739 INFO log_util.__aenter__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599  query_decompose start...
2025-08-10 01:49:43.741 INFO log_util.__enter__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599 enter ask_llm start...
2025-08-10 01:49:43.742 INFO log_util.__exit__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599 enter ask_llm cost=[1 ms]
2025-08-10 01:49:43.747 ERROR log_util.__aexit__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:49:43.748 INFO log_util.__aexit__ 0b6c3722-6dae-4bc2-9054-6d7a57e19599 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 01:49:48.987 INFO log_util.__aenter__ 6bf7faa9-639b-4c39-92b3-c40256b614d6 POST /v1/tool/deepsearch start...
2025-08-10 01:49:48.987 INFO middleware_util.custom_route_handler 6bf7faa9-639b-4c39-92b3-c40256b614d6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节 最佳时间 气候","request_id":"geniesession-1754761740810-297:1754761740825-4230:vogyh","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:49:48.988 INFO log_util.__enter__ 6bf7faa9-639b-4c39-92b3-c40256b614d6  run start...
2025-08-10 01:49:48.988 INFO log_util.__exit__ 6bf7faa9-639b-4c39-92b3-c40256b614d6  run cost=[0 ms]
2025-08-10 01:49:48.988 INFO deepsearch.run geniesession-1754761740810-297:1754761740825-4230:vogyh 第 1 轮深度搜索...
2025-08-10 01:49:48.989 INFO log_util.__aenter__ 6bf7faa9-639b-4c39-92b3-c40256b614d6  query_decompose start...
2025-08-10 01:49:48.991 INFO log_util.__enter__ 6bf7faa9-639b-4c39-92b3-c40256b614d6 enter ask_llm start...
2025-08-10 01:49:48.992 INFO log_util.__exit__ 6bf7faa9-639b-4c39-92b3-c40256b614d6 enter ask_llm cost=[1 ms]
2025-08-10 01:49:48.996 ERROR log_util.__aexit__ 6bf7faa9-639b-4c39-92b3-c40256b614d6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:49:48.997 INFO log_util.__aexit__ 6bf7faa9-639b-4c39-92b3-c40256b614d6 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:49:55.653 INFO log_util.__aenter__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b POST /v1/tool/deepsearch start...
2025-08-10 01:49:55.653 INFO middleware_util.custom_route_handler 5fcc7602-146a-4c15-a655-6efc5fb6ed4b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游攻略 最佳时间","request_id":"geniesession-1754761740810-297:1754761740825-4230:lkyi1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:49:55.654 INFO log_util.__enter__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b  run start...
2025-08-10 01:49:55.654 INFO log_util.__exit__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b  run cost=[0 ms]
2025-08-10 01:49:55.655 INFO deepsearch.run geniesession-1754761740810-297:1754761740825-4230:lkyi1 第 1 轮深度搜索...
2025-08-10 01:49:55.655 INFO log_util.__aenter__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b  query_decompose start...
2025-08-10 01:49:55.657 INFO log_util.__enter__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b enter ask_llm start...
2025-08-10 01:49:55.657 INFO log_util.__exit__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b enter ask_llm cost=[0 ms]
2025-08-10 01:49:55.662 ERROR log_util.__aexit__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:49:55.662 INFO log_util.__aexit__ 5fcc7602-146a-4c15-a655-6efc5fb6ed4b POST /v1/tool/deepsearch cost=[8 ms]
2025-08-10 01:54:08.923 INFO log_util.__aenter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 POST /v1/tool/report start...
2025-08-10 01:54:08.924 INFO middleware_util.custom_route_handler 8052d6ab-dfc5-457e-b1ef-c41ce880d981 POST /v1/tool/report body={"contentStream":true,"fileDescription":"巴厘岛最佳旅游时间分析报告","fileName":"巴厘岛最佳旅游时间.html","fileNames":[],"fileType":"html","query":"巴厘岛什么时候去合适","requestId":"geniesession-1754762021590-7956:1754762021603-3634","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并分析巴厘岛的气候特点和旅游季节信息，说明最佳旅游时间及其原因。"}
2025-08-10 01:54:08.926 INFO log_util.__enter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 enter report start...
2025-08-10 01:54:08.926 INFO log_util.__exit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 enter report cost=[0 ms]
2025-08-10 01:54:08.927 INFO log_util.__enter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 enter html_report start...
2025-08-10 01:54:08.927 INFO log_util.__exit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 enter html_report cost=[0 ms]
2025-08-10 01:54:08.927 INFO log_util.__aenter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  download_all_files start...
2025-08-10 01:54:08.927 INFO log_util.__aexit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  download_all_files cost=[0 ms]
2025-08-10 01:54:08.927 INFO log_util.__enter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  truncate_files start...
2025-08-10 01:54:08.927 INFO log_util.__exit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  truncate_files cost=[0 ms]
2025-08-10 01:54:08.927 INFO log_util.__enter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  truncate_files start...
2025-08-10 01:54:08.928 INFO log_util.__exit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  truncate_files cost=[0 ms]
2025-08-10 01:54:08.936 INFO log_util.__enter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 enter ask_llm start...
2025-08-10 01:54:08.937 INFO log_util.__exit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 enter ask_llm cost=[1 ms]
2025-08-10 01:54:13.519 INFO log_util.__aexit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 POST /v1/tool/report cost=[4595 ms]
2025-08-10 01:54:13.784 INFO log_util.__aenter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 exec ask_llm start...
2025-08-10 01:56:31.822 INFO log_util.__aexit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981 exec ask_llm cost=[138038 ms]
2025-08-10 01:56:31.823 INFO log_util.__aenter__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  upload_file start...
2025-08-10 01:56:31.831 INFO log_util.__aenter__ 0cd5e87a-5bca-4756-a4b4-347b06b01f19 POST /v1/file_tool/upload_file start...
2025-08-10 01:56:31.835 INFO middleware_util.custom_route_handler 0cd5e87a-5bca-4756-a4b4-347b06b01f19 POST /v1/file_tool/upload_file body={"requestId": "geniesession-1754762021590-7956:1754762021603-3634", "fileName": "\u5df4\u5398\u5c9b\u6700\u4f73\u65c5\u6e38\u65f6\u95f4.html", "content": "Html:\n```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>\u5df4\u5398\u5c9b\u6c14\u5019\u6307\u5357\uff1a\u63ed\u79d8\u6700\u4f73\u65c5\u6e38\u5b63\u8282</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js\"></script>\n    <link href=\"https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css\" rel=\"stylesheet\">\n    <style>\n        .hover-underline:hover {\n            text-decoration: underline;\n            text-decoration-color: #007bff;\n        }\n        .chart-container {\n            height: 400px;\n            margin: 20px 0;\n        }\n    </style>\n</head>\n<body class=\"bg-gray-50 font-sans\">\n    <div class=\"container mx-auto px-4 py-8 max-w-4xl\">\n        <header class=\"text-center mb-12\">\n            <h1 class=\"text-4xl font-bold text-blue-800 mb-4\">\u5df4\u5398\u5c9b\u6c14\u5019\u6307\u5357\uff1a\u63ed\u79d8\u6700\u4f73\u65c5\u6e38\u5b63\u8282</h1>\n            <p class=\"text-xl text-gray-600\">\u63a2\u7d22\u70ed\u5e26\u5929\u5802\u7684\u5b8c\u7f8e\u65c5\u884c\u65f6\u95f4</p>\n        </header>\n\n        <section class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u5df4\u5398\u5c9b\u6c14\u5019\u6982\u51b5</h2>\n            <p class=\"text-gray-700 mb-4\">\n                \u5df4\u5398\u5c9b\u5c5e\u4e8e\u5178\u578b\u7684\u70ed\u5e26\u5b63\u98ce\u6c14\u5019\uff0c\u5168\u5e74\u6e29\u6696\u6e7f\u6da6\uff0c\u5e73\u5747\u6c14\u6e29\u572826-32\u2103\u4e4b\u95f4\u3002\u7531\u4e8e\u5730\u5904\u8d64\u9053\u9644\u8fd1\uff0c\u5c9b\u4e0a\u6ca1\u6709\u660e\u663e\u7684\u56db\u5b63\u53d8\u5316\uff0c\u800c\u662f\u5206\u4e3a\u660e\u663e\u7684\u65f1\u5b63\u548c\u96e8\u5b63\u4e24\u4e2a\u5b63\u8282\u3002\n            </p>\n            <p class=\"text-gray-700\">\n                \u8fd9\u79cd\u72ec\u7279\u7684\u6c14\u5019\u7279\u5f81\u4f7f\u5f97\u5df4\u5398\u5c9b\u6210\u4e3a\u5168\u5e74\u7686\u5b9c\u7684\u65c5\u6e38\u76ee\u7684\u5730\uff0c\u4f46\u4e0d\u540c\u5b63\u8282\u7684\u4f53\u9a8c\u5dee\u5f02\u660e\u663e\u3002\u4e86\u89e3\u8fd9\u4e9b\u6c14\u5019\u7279\u70b9\u5c06\u5e2e\u52a9\u6e38\u5ba2\u9009\u62e9\u6700\u9002\u5408\u81ea\u5df1\u9700\u6c42\u7684\u65c5\u884c\u65f6\u95f4\u3002\n            </p>\n        </section>\n\n        <section class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u5b63\u8282\u5212\u5206\u4e0e\u7279\u70b9</h2>\n            \n            <div class=\"grid md:grid-cols-2 gap-6 mb-6\">\n                <div class=\"bg-blue-50 p-4 rounded-lg\">\n                    <h3 class=\"text-xl font-medium text-blue-800 mb-2\">\u65f1\u5b63\uff084\u6708-10\u6708\uff09</h3>\n                    <ul class=\"list-disc pl-5 text-gray-700 space-y-1\">\n                        <li>\u964d\u96e8\u91cf\u5c11\uff0c\u5929\u6c14\u6674\u6717\u5e72\u71e5</li>\n                        <li>\u5e73\u5747\u6c14\u6e29\u7ea628-32\u2103</li>\n                        <li>\u6e7f\u5ea6\u8f83\u4f4e\uff0c\u4f53\u611f\u8212\u9002</li>\n                        <li>\u6d77\u9762\u5e73\u9759\uff0c\u9002\u5408\u6c34\u4e0a\u6d3b\u52a8</li>\n                        <li>\u65c5\u6e38\u65fa\u5b63\uff0c\u6e38\u5ba2\u8f83\u591a</li>\n                    </ul>\n                </div>\n                \n                <div class=\"bg-green-50 p-4 rounded-lg\">\n                    <h3 class=\"text-xl font-medium text-green-800 mb-2\">\u96e8\u5b63\uff0811\u6708-3\u6708\uff09</h3>\n                    <ul class=\"list-disc pl-5 text-gray-700 space-y-1\">\n                        <li>\u964d\u96e8\u9891\u7e41\uff0c\u591a\u4e3a\u77ed\u6682\u9635\u96e8</li>\n                        <li>\u5e73\u5747\u6c14\u6e29\u7ea626-30\u2103</li>\n                        <li>\u6e7f\u5ea6\u8f83\u9ad8\uff0c\u7a7a\u6c14\u6e7f\u6da6</li>\n                        <li>\u690d\u88ab\u8302\u76db\uff0c\u666f\u8272\u66f4\u7eff</li>\n                        <li>\u6e38\u5ba2\u8f83\u5c11\uff0c\u4ef7\u683c\u66f4\u4f18\u60e0</li>\n                    </ul>\n                </div>\n            </div>\n\n            <div class=\"chart-container\" id=\"rainfall-chart\"></div>\n        </section>\n\n        <section class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u6700\u4f73\u65c5\u6e38\u65f6\u95f4\u63a8\u8350</h2>\n            \n            <div class=\"mb-6\">\n                <h3 class=\"text-xl font-medium text-blue-800 mb-2\">5\u6708-9\u6708\uff1a\u9ec4\u91d1\u65c5\u6e38\u671f</h3>\n                <p class=\"text-gray-700 mb-3\">\n                    \u8fd9\u6bb5\u65f6\u95f4\u662f\u5df4\u5398\u5c9b\u6700\u53d7\u6b22\u8fce\u7684\u65c5\u6e38\u5b63\u8282\uff0c\u5929\u6c14\u6674\u6717\u5e72\u71e5\uff0c\u964d\u96e8\u91cf\u6700\u5c11\uff0c\u975e\u5e38\u9002\u5408\u6d77\u6ee9\u6d3b\u52a8\u3001\u6f5c\u6c34\u548c\u6237\u5916\u63a2\u9669\u3002\u7279\u522b\u662f7-8\u6708\uff0c\u867d\u7136\u6e38\u5ba2\u8f83\u591a\uff0c\u4f46\u5404\u79cd\u8282\u65e5\u6d3b\u52a8\u4e30\u5bcc\uff0c\u5982\u5df4\u5398\u5c9b\u827a\u672f\u8282\u7b49\u3002\n                </p>\n                <p class=\"text-gray-700\">\n                    \u9700\u8981\u6ce8\u610f\u7684\u662f\uff0c\u8fd9\u6bb5\u65f6\u95f4\u4e5f\u662f\u65c5\u6e38\u65fa\u5b63\uff0c\u9152\u5e97\u548c\u673a\u7968\u4ef7\u683c\u8f83\u9ad8\uff0c\u5efa\u8bae\u63d0\u524d\u9884\u8ba2\u3002\n                </p>\n            </div>\n            \n            <div class=\"mb-6\">\n                <h3 class=\"text-xl font-medium text-blue-800 mb-2\">4\u6708&10\u6708\uff1a\u8fc7\u6e21\u671f\u4f18\u9009</h3>\n                <p class=\"text-gray-700\">\n                    \u8fd9\u4e24\u4e2a\u6708\u5904\u4e8e\u5b63\u8282\u4ea4\u66ff\u671f\uff0c\u5929\u6c14\u4ecd\u7136\u826f\u597d\uff0c\u964d\u96e8\u91cf\u5f00\u59cb\u589e\u52a0\u4f46\u4e0d\u9891\u7e41\uff0c\u6e38\u5ba2\u76f8\u5bf9\u8f83\u5c11\uff0c\u662f\u6027\u4ef7\u6bd4\u8f83\u9ad8\u7684\u9009\u62e9\u3002\u7279\u522b\u9002\u5408\u5e0c\u671b\u907f\u5f00\u4eba\u7fa4\uff0c\u4eab\u53d7\u66f4\u5b81\u9759\u5047\u671f\u7684\u6e38\u5ba2\u3002\n                </p>\n            </div>\n            \n            <div>\n                <h3 class=\"text-xl font-medium text-blue-800 mb-2\">11\u6708-3\u6708\uff1a\u96e8\u5b63\u7279\u8272\u4f53\u9a8c</h3>\n                <p class=\"text-gray-700 mb-3\">\n                    \u867d\u7136\u964d\u96e8\u8f83\u591a\uff0c\u4f46\u591a\u4e3a\u77ed\u6682\u9635\u96e8\uff0c\u96e8\u540e\u7a7a\u6c14\u6e05\u65b0\uff0c\u690d\u88ab\u66f4\u52a0\u7fe0\u7eff\u3002\u8fd9\u6bb5\u65f6\u95f4\u6e38\u5ba2\u7a00\u5c11\uff0c\u9152\u5e97\u4ef7\u683c\u5927\u5e45\u4e0b\u964d\uff0c\u9002\u5408\u9884\u7b97\u6709\u9650\u7684\u65c5\u884c\u8005\u3002\u6b64\u5916\uff0c\u96e8\u5b63\u4e5f\u662f\u51b2\u6d6a\u7684\u597d\u65f6\u8282\uff0c\u897f\u6d77\u5cb8\u6d6a\u51b5\u6700\u4f73\u3002\n                </p>\n                <p class=\"text-gray-700\">\n                    \u9700\u8981\u6ce8\u610f\u7684\u662f\uff0c1-2\u6708\u964d\u96e8\u6700\u4e3a\u9891\u7e41\uff0c\u53ef\u80fd\u4f1a\u5f71\u54cd\u90e8\u5206\u6237\u5916\u6d3b\u52a8\u3002\n                </p>\n            </div>\n        </section>\n\n        <section class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u6d3b\u52a8\u63a8\u8350\u4e0e\u5b63\u8282\u5339\u914d</h2>\n            \n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full bg-white border border-gray-200\">\n                    <thead class=\"bg-gray-100\">\n                        <tr>\n                            <th class=\"py-2 px-4 border-b text-left\">\u6d3b\u52a8\u7c7b\u578b</th>\n                            <th class=\"py-2 px-4 border-b text-left\">\u6700\u4f73\u5b63\u8282</th>\n                            <th class=\"py-2 px-4 border-b text-left\">\u539f\u56e0\u8bf4\u660e</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"py-2 px-4 border-b\">\u6d77\u6ee9\u5ea6\u5047</td>\n                            <td class=\"py-2 px-4 border-b\">5\u6708-9\u6708</td>\n                            <td class=\"py-2 px-4 border-b\">\u9633\u5149\u5145\u8db3\uff0c\u964d\u96e8\u5c11\uff0c\u6d77\u6c34\u6e05\u6f88</td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"py-2 px-4 border-b\">\u6f5c\u6c34/\u6d6e\u6f5c</td>\n                            <td class=\"py-2 px-4 border-b\">4\u6708-10\u6708</td>\n                            <td class=\"py-2 px-4 border-b\">\u6d77\u9762\u5e73\u9759\uff0c\u80fd\u89c1\u5ea6\u9ad8</td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"py-2 px-4 border-b\">\u51b2\u6d6a</td>\n                            <td class=\"py-2 px-4 border-b\">11\u6708-3\u6708</td>\n                            <td class=\"py-2 px-4 border-b\">\u897f\u6d77\u5cb8\u6d6a\u51b5\u6700\u4f73</td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"py-2 px-4 border-b\">\u6587\u5316\u63a2\u7d22</td>\n                            <td class=\"py-2 px-4 border-b\">\u5168\u5e74\u7686\u5b9c</td>\n                            <td class=\"py-2 px-4 border-b\">\u96e8\u5b63\u5bfa\u5e99\u66f4\u663e\u795e\u79d8\uff0c\u65f1\u8282\u5e86\u6d3b\u52a8\u591a</td>\n                        </tr>\n                        <tr class=\"hover:bg-gray-50\">\n                            <td class=\"py-2 px-4 border-b\">\u5f92\u6b65\u65c5\u884c</td>\n                            <td class=\"py-2 px-4 border-b\">5\u6708-9\u6708</td>\n                            <td class=\"py-2 px-4 border-b\">\u9053\u8def\u5e72\u71e5\uff0c\u89c6\u91ce\u5f00\u9614</td>\n                        </tr>\n                    </tbody>\n                </table>\n            </div>\n        </section>\n\n        <section class=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <h2 class=\"text-2xl font-semibold text-blue-700 mb-4\">2025\u5e748\u6708\u65c5\u6e38\u63d0\u793a</h2>\n            <div class=\"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4\">\n                <div class=\"flex\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-5 w-5 text-yellow-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </div>\n                    <div class=\"ml-3\">\n                        <p class=\"text-sm text-yellow-700\">\n                            \u5f53\u524d\u6b63\u503c2025\u5e748\u6708\uff0c\u5c5e\u4e8e\u5df4\u5398\u5c9b\u65f1\u5b63\u4e2d\u671f\uff0c\u662f\u65c5\u6e38\u7684\u9ec4\u91d1\u65f6\u95f4\u3002\u5929\u6c14\u6674\u6717\u5e72\u71e5\uff0c\u975e\u5e38\u9002\u5408\u5404\u79cd\u6237\u5916\u6d3b\u52a8\u548c\u6c34\u4e0a\u8fd0\u52a8\u3002\u4f46\u8bf7\u6ce8\u610f\u9632\u6652\uff0c\u5e76\u63d0\u524d\u9884\u8ba2\u4f4f\u5bbf\uff0c\u56e0\u4e3a\u8fd9\u662f\u65c5\u6e38\u65fa\u5b63\u3002\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </section>\n\n        <footer class=\"text-center text-gray-500 text-sm mt-12 pt-6 border-t border-gray-200\">\n            <p>Created by Autobots</p>\n            <p>\u9875\u9762\u5185\u5bb9\u5747\u7531 AI \u751f\u6210\uff0c\u4ec5\u4f9b\u53c2\u8003</p>\n        </footer>\n    </div>\n\n    <script>\n        // \u964d\u96e8\u91cf\u56fe\u8868\n        const rainfallChart = echarts.init(document.getElementById('rainfall-chart'));\n        const rainfallOption = {\n            title: {\n                text: '\u5df4\u5398\u5c9b\u6708\u5e73\u5747\u964d\u96e8\u91cf(mm)',\n                left: 'center'\n            },\n            tooltip: {\n                trigger: 'axis',\n                axisPointer: {\n                    type: 'shadow'\n                }\n            },\n            xAxis: {\n                type: 'category',\n                data: ['1\u6708', '2\u6708', '3\u6708', '4\u6708', '5\u6708', '6\u6708', '7\u6708', '8\u6708', '9\u6708', '10\u6708', '11\u6708', '12\u6708'],\n                axisLabel: {\n                    rotate: 45\n                }\n            },\n            yAxis: {\n                type: 'value',\n                name: '\u964d\u96e8\u91cf(mm)'\n            },\n            series: [{\n                data: [345, 274, 234, 88, 93, 53, 55, 25, 47, 63, 179, 276],\n                type: 'bar',\n                itemStyle: {\n                    color: '#4299e1'\n                },\n                label: {\n                    show: true,\n                    position: 'top'\n                }\n            }],\n            color: ['#4299e1']\n        };\n        rainfallChart.setOption(rainfallOption);\n\n        // \u54cd\u5e94\u5f0f\u8c03\u6574\n        window.addEventListener('resize', function() {\n            rainfallChart.resize();\n        });\n    </script>\n</body>\n</html>\n", "description": "Html:\n```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>\u5df4\u5398\u5c9b\u6c14\u5019\u6307\u5357\uff1a\u63ed\u79d8\u6700\u4f73\u65c5\u6e38\u5b63\u8282</title>\n    <"}
2025-08-10 01:56:31.840 INFO log_util.__aenter__ 0cd5e87a-5bca-4756-a4b4-347b06b01f19  add_by_content start...
2025-08-10 01:56:31.843 ERROR log_util.__aexit__ 0cd5e87a-5bca-4756-a4b4-347b06b01f19  add_by_content error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniesession-1754762021590-7956:1754762021603-3634'

2025-08-10 01:56:31.850 ERROR middleware_util.dispatch 0cd5e87a-5bca-4756-a4b4-347b06b01f19 POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniesession-1754762021590-7956:1754762021603-3634'

2025-08-10 01:56:31.853 INFO log_util.__aexit__ 0cd5e87a-5bca-4756-a4b4-347b06b01f19 POST /v1/file_tool/upload_file cost=[22 ms]
2025-08-10 01:56:31.859 ERROR log_util.__aexit__ 8052d6ab-dfc5-457e-b1ef-c41ce880d981  upload_file error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\file_util.py", line 107, in upload_file
    result = json.loads(await response.text())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

2025-08-10 13:37:58.977 INFO log_util.__aenter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 POST /v1/tool/deepsearch start...
2025-08-10 13:37:58.980 INFO middleware_util.custom_route_handler 3c88941d-7d1f-46ee-9e7c-6b15643090b5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅游攻略 2025","request_id":"geniesession-1754804265647-6966:1754804265663-6669:yqekm","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 13:37:58.984 INFO log_util.__enter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  run start...
2025-08-10 13:37:58.985 INFO log_util.__exit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  run cost=[0 ms]
2025-08-10 13:37:58.986 INFO deepsearch.run geniesession-1754804265647-6966:1754804265663-6669:yqekm 第 1 轮深度搜索...
2025-08-10 13:37:58.987 INFO log_util.__aenter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  query_decompose start...
2025-08-10 13:37:58.996 INFO log_util.__enter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 enter ask_llm start...
2025-08-10 13:37:58.998 INFO log_util.__exit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 enter ask_llm cost=[0 ms]
2025-08-10 13:37:59.030 INFO log_util.__aexit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 POST /v1/tool/deepsearch cost=[52 ms]
2025-08-10 13:37:59.231 INFO log_util.__aenter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 exec ask_llm start...
2025-08-10 13:38:04.862 INFO log_util.__aexit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 exec ask_llm cost=[5630 ms]
2025-08-10 13:38:04.865 INFO query_process.query_decompose 3c88941d-7d1f-46ee-9e7c-6b15643090b5 query_decompose think: 为了解决此问题，我需要搜索2025年上海至巴厘岛的航班信息、最新签证政策、当地热门景点开放情况、季节性气候差异、消费水平变化以及疫情后旅游注意事项等。需要进行进一步检索。
2025-08-10 13:38:04.865 INFO log_util.__enter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 enter ask_llm start...
2025-08-10 13:38:04.866 INFO log_util.__exit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 enter ask_llm cost=[0 ms]
2025-08-10 13:38:04.924 INFO log_util.__aenter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 exec ask_llm start...
2025-08-10 13:38:12.550 INFO log_util.__aexit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 exec ask_llm cost=[7625 ms]
2025-08-10 13:38:12.551 INFO query_process.query_decompose 3c88941d-7d1f-46ee-9e7c-6b15643090b5 query_decompose queries: - 2025年8月上海至巴厘岛航班信息及价格比较
- 2025年印尼巴厘岛最新签证政策对中国游客要求
- 2025年巴厘岛热门景点开放时间及门票价格
- 2025年8月巴厘岛天气预测及旅游季节特点
- 疫情后巴厘岛旅游安全注意事项及消费水平变化
2025-08-10 13:38:12.551 INFO log_util.__aexit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  query_decompose cost=[13563 ms]
2025-08-10 13:38:12.657 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.657 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.658 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.661 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.660 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.660 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.664 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.659 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.661 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:12.667 INFO log_util.__aenter__ default-request-id  search_and_dedup start...
2025-08-10 13:38:14.025 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:14.203 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:14.274 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:14.402 WARNING search_engine._parser parser content-type[application/pdf] not parser: url=[https://www.csair.cn/csvideo/2018/20180529_3/balidaolvyougonglue20180529.pdf]
2025-08-10 13:38:14.759 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:15.125 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:16.163 INFO log_util.__aexit__ default-request-id  parser cost=[1960 ms]
2025-08-10 13:38:16.166 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[3505 ms]
2025-08-10 13:38:16.170 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:16.340 WARNING search_engine._parser parser content-type[application/pdf] not parser: url=[https://www.csair.cn/csvideo/2018/20180529_3/balidaolvyougonglue20180529.pdf]
2025-08-10 13:38:18.235 INFO log_util.__aexit__ default-request-id  parser cost=[3110 ms]
2025-08-10 13:38:18.276 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[5609 ms]
2025-08-10 13:38:18.380 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:18.463 INFO log_util.__aexit__ default-request-id  parser cost=[2292 ms]
2025-08-10 13:38:18.464 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[5806 ms]
2025-08-10 13:38:20.008 WARNING search_engine._parser parser error: url=[https://www.boca.gov.tw/sp-foof-countrycp-03-12-c23b0-02-1.html] error=Cannot connect to host www.boca.gov.tw:443 ssl:default [getaddrinfo failed]
2025-08-10 13:38:21.454 INFO log_util.__aexit__ default-request-id  parser cost=[3074 ms]
2025-08-10 13:38:21.456 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[8795 ms]
2025-08-10 13:38:24.392 WARNING search_engine._parser parser error: url=[https://www.bbc.com/zhongwen/trad/world-63870895] error=
2025-08-10 13:38:24.392 WARNING search_engine._parser parser error: url=[https://www.facebook.com/yourfriendinbali/posts/-bali-tourists-impacted-by-major-change-to-visa-rules-effective-immediately-%E5%B3%87%E9%87%8C%E5%B3%B6%E7%B0%BD/1129649319203258/] error=
2025-08-10 13:38:24.394 WARNING search_engine._parser parser error: url=[https://www.youtube.com/watch?v=ms4xrcu5XHk&pp=ygUQI-W3tOWOmOWztuaXheihjA%3D%3D] error=
2025-08-10 13:38:24.422 WARNING search_engine._parser parser error: url=[https://www.bbc.com/zhongwen/simp/world-63870895] error=
2025-08-10 13:38:25.066 INFO log_util.__aexit__ default-request-id  parser cost=[11041 ms]
2025-08-10 13:38:25.076 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[12416 ms]
2025-08-10 13:38:25.105 INFO log_util.__aexit__ default-request-id  parser cost=[10831 ms]
2025-08-10 13:38:25.105 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:25.108 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[12444 ms]
2025-08-10 13:38:25.110 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:25.389 WARNING search_engine._parser parser error: url=[https://www.singaporeair.com/zh-cn/flights-from-shanghai-to-denpasar-bali] error=
2025-08-10 13:38:26.039 INFO log_util.__aexit__ default-request-id  parser cost=[11279 ms]
2025-08-10 13:38:26.040 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[13380 ms]
2025-08-10 13:38:26.041 INFO log_util.__aenter__ default-request-id  parser start...
2025-08-10 13:38:31.019 WARNING search_engine._parser parser error: url=[https://www.boca.gov.tw/sp-foof-countrycp-03-12-c23b0-02-1.html] error=Cannot connect to host www.boca.gov.tw:443 ssl:default [getaddrinfo failed]
2025-08-10 13:38:35.388 WARNING search_engine._parser parser error: url=[https://www.facebook.com/yourfriendinbali/posts/-bali-tourists-impacted-by-major-change-to-visa-rules-effective-immediately-%E5%B3%87%E9%87%8C%E5%B3%B6%E7%B0%BD/1129649319203258/] error=
2025-08-10 13:38:35.388 WARNING search_engine._parser parser error: url=[https://www.bbc.com/zhongwen/trad/world-63870895] error=
2025-08-10 13:38:35.419 WARNING search_engine._parser parser error: url=[https://www.youtube.com/watch?v=ms4xrcu5XHk&pp=ygUQI-W3tOWOmOWztuaXheihjA%3D%3D] error=
2025-08-10 13:38:35.450 WARNING search_engine._parser parser error: url=[https://www.bbc.com/zhongwen/simp/world-63870895] error=
2025-08-10 13:38:35.482 INFO log_util.__aexit__ default-request-id  parser cost=[10376 ms]
2025-08-10 13:38:35.528 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[22870 ms]
2025-08-10 13:38:35.563 INFO log_util.__aexit__ default-request-id  parser cost=[10452 ms]
2025-08-10 13:38:35.564 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[22903 ms]
2025-08-10 13:38:36.384 WARNING search_engine._parser parser error: url=[https://www.singaporeair.com/zh-cn/flights-from-shanghai-to-denpasar-bali] error=
2025-08-10 13:38:36.623 INFO log_util.__aexit__ default-request-id  parser cost=[10582 ms]
2025-08-10 13:38:36.623 INFO log_util.__aexit__ default-request-id  search_and_dedup cost=[23966 ms]
2025-08-10 13:38:36.624 INFO log_util.__enter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  truncate_files start...
2025-08-10 13:38:36.625 INFO log_util.__exit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  truncate_files cost=[1 ms]
2025-08-10 13:38:36.625 INFO log_util.__enter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  answer_question start...
2025-08-10 13:38:36.626 INFO log_util.__exit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5  answer_question cost=[0 ms]
2025-08-10 13:38:36.628 INFO log_util.__enter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 enter ask_llm start...
2025-08-10 13:38:36.628 INFO log_util.__exit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 enter ask_llm cost=[0 ms]
2025-08-10 13:38:36.648 INFO log_util.__aenter__ 6a665142-1ea5-4659-a4bb-19b8140c2419 POST /v1/file_tool/upload_file start...
2025-08-10 13:38:36.649 INFO middleware_util.custom_route_handler 6a665142-1ea5-4659-a4bb-19b8140c2419 POST /v1/file_tool/upload_file body={"content":"{\"2025年印尼巴厘岛最新签证政策对中国游客要求\":[{\"content\":\"瞭解峇里島氣温變化及最佳旅遊季節，提供穿衣搭配建議，優化您的旅行體驗。探索峇里島熱門景點，掌握交通、住宿及飲食貼士，為您的峇里島之旅做好充分準備！\",\"doc_type\":\"web_page\",\"link\":\"https://hk.trip.com/blog/bali-weather-tour/\",\"title\":\"解鎖峇里島絕美景點與特色吃喝地\"},{\"content\":\"幾個月非常不建議去峇里島。而峇里島旅遊最舒服的季節是7月和8月，這段時間的降雨機率是一年之中最低的。\",\"doc_type\":\"web_page\",\"link\":\"https://missslow.com/bali-weather/\",\"title\":\"【2025峇里島天氣指南】最佳旅遊月份？雨季+季節玩法推薦！\"},{\"content\":\"8 月延续了晴朗天气的趋势，使其成为游客的另一个高峰月份。平均气温保持在24°C 至30°C（75°F 至86°F）左右。这是户外探险和文化体验的 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/explore-balis-climate-festivals-under-sunny-skies/\",\"title\":\"探索巴厘岛的气候：晴空下的节日\"},{\"content\":\"峇里島為熱帶季風氣候，與馬爾地夫一樣，並沒有四季之分，只有乾季、雨季2 種差異，所以全年氣溫通常會穩定在攝氏26～28 度之間，非常適合度假。那麼，峇里島的 ...\",\"doc_type\":\"web_page\",\"link\":\"https://blog.curatorstravel.com/popular-tourist-areas/bali\",\"title\":\"峇里島旅遊2025｜必訪8大景點、推薦酒店、行前準備一覽！\"},{\"content\":\"這段期間來峇里島旅遊日照時間長，陽光充足，因此4-10 月通常是峇里島的旅遊旺季，尤其是7-8 月，適逢學生暑假，以及剛好是許多印尼的國定假期期間，機票住宿價格相對較昂貴。\",\"doc_type\":\"web_page\",\"link\":\"https://esim.holafly.com/zh/travel-tips/best-time-to-visit-bali/\",\"title\":\"最佳峇里島旅遊季節，乾季雨季天氣和優缺點分析 - Holafly\"},{\"content\":\"從4 月到9 月可以說是峇里島的乾季，這幾個月份通常氣溫高、降雨量少，最乾燥的月份是8 月。旅客在一年中的這個時間點前來，可以預期大部分日子都是處於乾燥和陽光充裕的天氣， ...\",\"doc_type\":\"web_page\",\"link\":\"https://fokoinside.com/articles/34a455\",\"title\":\"印尼峇里島Bali 天氣大公開，全年度旅遊氣溫＆氣候指南！\"},{\"content\":\"峇里島（Bali）是全年任何時間都適合造訪的絕佳地點。印尼有1萬7千座島嶼，峇里島位於赤道以南8度，大部分時間屬於熱帶、炎熱和潮濕氣候。\",\"doc_type\":\"web_page\",\"link\":\"https://www.vogue.com.tw/article/best-time-to-visit-bali\",\"title\":\"峇里島最佳旅遊時間，旺季、雨季，選對季節讓你的度假體驗更加完美\"},{\"content\":\"- 佩尼达岛：原始自然风光，精灵沙滩、破碎沙滩、水晶湾的海水透明度极高，适合浮潜和徒步探险。 - 阿贡火山：巴厘岛最高活火山，徒步需向导带领，7-9月为最佳 ...\",\"doc_type\":\"web_page\",\"link\":\"https://zhuanlan.zhihu.com/p/23976699062\",\"title\":\"2025年巴厘岛旅游的详细攻略 - 知乎专栏\"},{\"content\":\"【峇里島天氣】2025峇里島出行必備，參考峇里島氣溫，解鎖峇里島絕美景點與特色吃喝地 · 瞭解峇里島氣温變化及最佳旅遊季節，提供穿衣搭配建議，優化您的旅行體驗。探索峇里島 ...\",\"doc_type\":\"web_page\",\"link\":\"https://hk.trip.com/blog/tag-bali-2824/\",\"title\":\"2025年8月峇里島相關攻略推薦 - Trip.com\"},{\"content\":\"终极指南巴厘岛的气候属于热带气候，全年气温温暖，湿度高。但是，季节性变化会严重影响您的旅行计划。旱季从4 月持续到10 月，被认为是游览巴厘岛的最 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/discover-the-best-season-for-bali-festivals-surf-awaits/\",\"title\":\"发现巴厘岛的最佳季节：节日和冲浪等待着您！\"}],\"2025年8月巴厘岛天气预测及旅游季节特点\":[{\"content\":\"... 上海浦东飞往巴厘岛（登巴萨）的航班？ 截至2025年8月，每周有17 班从上海浦东飞往巴厘岛（登巴萨）的航班。 从上海浦东到巴厘岛（登巴萨）的机票什么时候最便宜？ 从上海浦东飞 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.tianxun.com/routes/pvg/dps/shanghai-pudong-to-bali-denpasar.html\",\"title\":\"从上海浦东出发前往巴厘岛（登巴萨）的特价机票，往返 ...\"},{\"content\":\"搜索时选择「添加附近机场」，扩大从上海飞往巴厘岛最便宜航班的搜索范围。选中该选项即可查看所有可出发机场和巴厘岛附近机场的航班价格，为您节省一大笔钱！\",\"doc_type\":\"web_page\",\"link\":\"https://www.tianxun.com/routes/csha/blca/shanghai-to-bali.html\",\"title\":\"从上海飞往巴厘岛的特价航班| Skyscanner\"},{\"content\":\"查找从上海飞往巴厘岛（登巴萨）最便宜的月份 ; 8月. $523 起 ; 9月. $351 起 ; 10月. $370 起 ; 11月. $373 起 ; 12月. $369 起.\",\"doc_type\":\"web_page\",\"link\":\"https://www.skyscanner.com.sg/sg/zh-cn/sgd/routes/csha/dps/shanghai-to-bali-denpasar.html\",\"title\":\"上海飞巴厘岛（登巴萨）的便宜机票 - Skyscanner\"},{\"content\":\"... 上海到巴厘岛(登巴萨)机票的信息价格， ... 2025年8月. CNY3,125*. 已浏览： undefined cmp-undefined 前. 往返/经济舱. 2025年9 ...\",\"doc_type\":\"web_page\",\"link\":\"https://flights.cathaypacific.com/destinations/sc_CN/%E4%BB%8E%E4%B8%8A%E6%B5%B7%E5%88%B0%E4%B8%B9%E5%B8%95%E6%B2%99%E7%9A%84%E8%88%AA%E7%8F%AD\",\"title\":\"上海到巴厘岛(登巴萨)机票价格| 国泰航空- 航班 - Cathay Pacific\"},{\"content\":\"查找从上海飞往巴厘岛的航班，价格低至CNY4,972*. 往返. expand_more. 1 位乘客 ... 请选择另外的月份或选择下方的具体日期以查找机票价格。 chevron_left. 2025年8月.\",\"doc_type\":\"web_page\",\"link\":\"https://www.singaporeair.com/zh-cn/flights-from-shanghai-to-denpasar-bali\",\"title\":\"从上海飞往巴厘岛的航班| 新加坡航空 - Singapore Airlines\"},{\"content\":\"上海飛峇里島日期與建議 8月最平只需HK$3,184，2025年8月29日出發，由吉祥航空執飛。 9月最平只需HK$3,006，9月4日週四出發，由吉祥航空執飛。 10月最平只需HK$2,984 ...\",\"doc_type\":\"web_page\",\"link\":\"https://hk.trip.com/flights/shanghai-to-bali/airfares-sha-dps/\",\"title\":\"上海飛峇里島單程優惠機票搭乘9月 ... - Trip.com\"},{\"content\":\"Skyscanner搜索巴厘岛（登巴萨） (DPS)飞上海(CSHA)的所有航班，比较各大传统航空，廉价航空及旅行社的机票价格，帮您找到巴厘岛（登巴萨） (DPS)飞上海(CSHA)最便宜的机票！\",\"doc_type\":\"web_page\",\"link\":\"https://www.skyscanner.com.sg/sg/zh-cn/sgd/routes/dps/csha/bali-denpasar-to-shanghai.html\",\"title\":\"巴厘岛（登巴萨）飞上海的便宜机票 - Skyscanner\"},{\"content\":\"有24家航空公司从中国飞往巴厘岛。 最热门的航线是从上海上海浦东国际机场飞往库塔伍拉·赖国际机场。 此航班单程平均飞行时间为7小时53分钟，往返机票平均价格为6,875元 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.booking.com/flights/destination/to-region/id/bali.zh-cn.html\",\"title\":\"搜索飞往巴厘岛的航班- 机票 - Booking.com\"},{\"content\":\"搜索并预订从普吉岛到巴厘岛/登巴萨的超值机票 ; 马来西亚巴迪克航空 · 2025年8月27日星期三. RM 432.48 ; 巴迪克航空, 泰国亚洲航空 · 2025年8月29日星期五. RM 442.68 ; 溜走.\",\"doc_type\":\"web_page\",\"link\":\"https://www.traveloka.com/zh-my/flight/route/Phuket-Bali-Denpasar.HKT.DPS\",\"title\":\"普吉岛到巴厘岛/登巴萨机票查询：航班时刻表、票价、飞行时间与预订\"},{\"content\":\"2025年8月12日星期二. RM 1,654.59 · 马来西亚巴迪克航空, 埃及航空. 巴厘岛/登巴萨 ... 建议提前在Traveloka等平台上查询航班信息，比较不同航空公司和中转时间的 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.traveloka.com/zh-my/flight/route/Bali-Denpasar-Cairo.DPS.CAI\",\"title\":\"巴厘岛/登巴萨到开罗机票价格、航班时刻与飞行时间查询| 立即预订\"}],\"2025年巴厘岛热门景点开放时间及门票价格\":[{\"content\":\"... 消费者对于疫情防控更有信心，拾起出行之心，人们旅游需求也持续存在，并且也在疫情放开后的当下超越疫情前的水平。 其实这里仅仅是国内的数据展示 ...\",\"doc_type\":\"web_page\",\"link\":\"https://zhuanlan.zhihu.com/p/*********\",\"title\":\"巴厘岛事件延申| 洞察疫情后国内旅游行业 - 知乎专栏\"},{\"content\":\"疫情前，巴厘岛每年600万游客，中国游客占五分之一左右。 虽然中国突然快速放宽，但还未恢复出境游；欧洲则困于生活成本危机，用于长距离旅行的消费复苏缓慢 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.bbc.com/zhongwen/simp/world-63870895\",\"title\":\"巴厘岛纪行：全球化“金丝雀”的疫后重生 - BBC\"},{\"content\":\"今年3月，在发生了一系列涉及违反交通法规的案件后，巴厘岛开始禁止外国游客骑摩托车。巴厘省省长瓦扬称，许多人不戴头盔、没有执照，骑着摩托车在巴厘岛上 ...\",\"doc_type\":\"web_page\",\"link\":\"http://paper.ce.cn/pad/content/202306/25/content_276338.html\",\"title\":\"“傲娇”巴厘岛\"},{\"content\":\"导读全方位介绍巴厘岛，包括地理、历史、民情、宗教、气候、淡旺季、景点和活动、机场和航班、岛内外交通、签证、出入境、货币兑换、安全提示、网站推荐等。\",\"doc_type\":\"web_page\",\"link\":\"https://zhuanlan.zhihu.com/p/500512664\",\"title\":\"巴厘岛深度体验之全攻略1—自由行超级指南\"},{\"content\":\"Missing: 疫情 消费 水平 变化\",\"doc_type\":\"web_page\",\"link\":\"https://cs.mfa.gov.cn/gyls/lsgz/fwxx/202408/t20240801_11464713.shtml\",\"title\":\"驻登巴萨总领馆提醒暑期赴巴厘岛中国游客注意旅游安全\"},{\"content\":\"2020年因疫情關閉，巴厘島全年遊客量，從600萬驟降到105萬；2021年更慘，據官方數據， 前10個月，僅有45名國際遊客來訪。 巴厘島雖然不大，因為它太依賴旅遊 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.bbc.com/zhongwen/trad/world-63870895\",\"title\":\"巴厘島紀行：全球化「金絲雀」的疫後重生 - BBC\"},{\"content\":\"Missing: 消费 水平 变化\",\"doc_type\":\"web_page\",\"link\":\"https://www.youtube.com/watch?v=ms4xrcu5XHk&pp=ygUQI-W3tOWOmOWztuaXheihjA%3D%3D\",\"title\":\"疫情後第一次峇里島自由行！機票貴嗎？遊客多嗎？疫情嚴重嗎？要 ...\"},{\"content\":\"会议指出，爱国卫生运动是有效预防疾病传播、提升健康水平的重要途径。当前我省正处于基孔肯雅热疫情防控关键期，叠加易于蚊虫孳生的高温湿热天气，疫情防控 ...\",\"doc_type\":\"web_page\",\"link\":\"https://news.dayoo.com/guangdong/202508/03/139996_54856131.htm\",\"title\":\"王伟中出席全省爱国卫生运动工作会议 - 广州日报大洋网\"},{\"content\":\"我们着力促进消费升级，比如说在汽车、家电、餐饮、家政服务等领域。在城市方面，刚刚宣布了在五个城市培育国际消费中心城市。同时在农村，我们加强县域商业体系建设，促进 ...\",\"doc_type\":\"web_page\",\"link\":\"https://ayyywz.com/interview/detail/202108/ff8080817b4fe820017b721769b4000b/\",\"title\":\"中华人民共和国商务部365bet娱乐投注_365bet稳定备用网站_365 ...\"},{\"content\":\"当地时间8月2日，美国媒体报道称，由于受关税政策带来的成本提升影响，美国销售商被迫提高产品售价，这意味美国消费者最终不得不为货架上持续涨价的商品 ...\",\"doc_type\":\"web_page\",\"link\":\"https://news.dayoo.com/world/202508/03/139998_54856277.htm\",\"title\":\"电脑、服装等商品将因关税涨价美媒：美消费者买单！ - 广州日报大洋网\"}],\"2025年8月上海至巴厘岛航班信息及价格比较\":[{\"content\":\"探索2025年峇里島+科莫多9日8夜之旅的最新旅遊路線，內容包括全面的行程規劃、旅遊清單，以及交通、住宿和景點推薦，確保您的旅途愉快。\",\"doc_type\":\"web_page\",\"link\":\"https://hk.trip.com/blog/bali-438-komodo-1448014-9days-itinerary-10012237971/\",\"title\":\"2025年峇里島+科莫多自由行旅遊攻略 - Trip.com\"},{\"content\":\"在聖猴森林公園的猴子都是野生放養，並有人固定餵食照顧，很值得到此一遊。 Google 地圖評論：4.5 / 5; 開放時間：08:00~18:00; 門票：成人80,000 印尼盾、兒童 ...\",\"doc_type\":\"web_page\",\"link\":\"https://blog.curatorstravel.com/popular-tourist-areas/bali\",\"title\":\"峇里島旅遊2025｜必訪8大景點、推薦酒店、行前準備一覽！\"},{\"content\":\"探索2025年峇里島2日1夜之旅的最新旅遊路線，內容包括全面的行程規劃、旅遊清單，以及交通、住宿和景點推薦，確保您的旅途愉快。我們的AI 行程規劃工具將 ...\",\"doc_type\":\"web_page\",\"link\":\"https://hk.trip.com/blog/bali-438-2days-itinerary-9998440313/\",\"title\":\"2025年第一次去峇里島必看（萬勿錯過）：機票、住宿、交通及2日1 ...\"},{\"content\":\"参观公元前11 世纪的巴厘岛印度教寺庙，欣赏传统的凯恰克火舞表演。了解这种引人入胜的舞蹈所讲述的详细故事。享受免排队入场优惠。 欣赏独特的凯恰克火舞，由一群男子使用 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.getyourguide.com/zh-cn/ba-li-dao-l347/duo-ri-you-tc282/\",\"title\":\"2025 巴厘岛最推荐的多日游体验\"},{\"content\":\"热门景点. 库塔海滩. 说到假期，没有比在库塔的热门海滩库塔海滩度过慵懒时光更愉快的了。漫步于海滨或探索这里的日落景色。 库塔海滩. 努沙杜瓦海滩.\",\"doc_type\":\"web_page\",\"link\":\"https://www.expedia.com/cn/Bali.d602651.Travel\",\"title\":\"【 2025 巴厘岛旅游套票优惠】预订机票+酒店必看@Expedia\"},{\"content\":\"开放时间：全天. 门票价格：免费. 景点位置：Kuta Beach, Bali. 如何前往：从巴厘岛国际机场乘坐出租车，15 分钟即可到达. 怎么玩：除了欣赏海滩美景之外，还可以挑战库塔海滩最出.\",\"doc_type\":\"web_page\",\"link\":\"https://www.csair.cn/csvideo/2018/20180529_3/balidaolvyougonglue20180529.pdf\",\"title\":\"[PDF] 巴厘岛\"},{\"content\":\"参加乌布一日游，沉浸在乌布充满活力的文化中，您将花一天时间参观巴厘岛的顶级景点，包括在切金停留参观德格拉朗梯田、Tegenungan 瀑布、乌布猴子保护区、可以寻找巴厘岛 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.getyourguide.com/zh-cn/wu-bu-de-l32246/\",\"title\":\"2025 乌布德必访：热门行程和活动体验 - GetYourGuide\"},{\"content\":\"如果您正在寻找更轻松的体验，请考虑在4 月和9 月的非高峰月份参观。您不仅会享受更少的游客，而且您还会发现许多酒店和餐馆提供诱人的折扣。想象一下，沿着 ...\",\"doc_type\":\"web_page\",\"link\":\"https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/best-times-to-visit-bali-discover-colorful-festivals/\",\"title\":\"访问巴厘岛的最佳时间：探索丰富多彩的节日\"},{\"content\":\"峇里島自由行怎麼玩？無論是海神廟的夕陽美景、巴杜爾火山的徒步日出行程、或是衝浪潛水活動與在地美食，都會讓您在峇里島創造許多美好回憶！\",\"doc_type\":\"web_page\",\"link\":\"https://www.klook.com/zh-TW/blog/trend-mount-batur-sunrise-trekking-bali/\",\"title\":\"【峇里島自由行】峇里島景點一日遊、交通及美食！5天行程安排最充實\"},{\"content\":\"成人价格最低. US$80.00. 元（具体价格视团体规模而定）. 选择日期和旅行者. 2025年8月5日星期二. 2 名成人. 查看供应情况. 免费取消 • 在体验开始前24 小时（当地时间）取消， ...\",\"doc_type\":\"web_page\",\"link\":\"https://cn.tripadvisor.com/AttractionProductReview-g297694-d17486592-Bali_Swing_Activity_and_Tanah_Lot_Sunset_Trip_Packages-Denpasar_Bali.html\",\"title\":\"巴厘岛秋千活动和海神庙日落之旅套餐| 登巴萨, 印度尼西亚 - Tripadvisor\"}],\"疫情后巴厘岛旅游安全注意事项及消费水平变化\":[{\"content\":\"持中国外交、公务护照者可免签入境30天，免签原则上不能延期。常驻使领馆人员须办理签证。 印尼移民法规严厉，中国公民应遵守印尼的法规，不得从事与签证目的不符 ...See more\",\"doc_type\":\"web_page\",\"link\":\"https://cs.mfa.gov.cn/zggmcg/ljmdd/yz_645708/ydnxy_648376/rjjl_648386/\",\"title\":\"签证入境- 印度尼西亚 - 中国领事服务网- 外交部\"},{\"content\":\"驻登巴萨总领馆提醒领区中国游客暑假期间注意旅游安全（2025-05-30） · 驻登 ... 驻登巴萨总领馆提醒中国赴巴厘岛游客遵守当地放飞无人机和风筝有关规定并注意出行 ...See more\",\"doc_type\":\"web_page\",\"link\":\"http://denpasar.china-consulate.gov.cn/lsfw/tzgg/\",\"title\":\"重要通知 - 驻登巴萨总领馆\"},{\"content\":\"... 签证政策核心信息（2025 年5 月更新） 根据印尼移民局及中国领事服务网最新公告， 中国公民持普通护照目前需申请电子落地签（e-VOA）或落地签（VOA）入境印尼 ...\",\"doc_type\":\"web_page\",\"link\":\"https://zhuanlan.zhihu.com/p/1904687297951137854\",\"title\":\"中国公民印度尼西亚签证全攻略（2025 年权威版，附官方来源）\"},{\"content\":\"所有赴印尼旅客登機前不需出示RT-PCR篩檢陰性證明。 · 所有赴印尼旅客不需出示疫苗接種證明。 · 取消相關隔離檢疫措施。 · 旅客無需下載行動電話軟體 ...See more\",\"doc_type\":\"web_page\",\"link\":\"https://www.klook.com/zh-TW/blog/indonesia-covid19-entry-restrictions/\",\"title\":\"【2025印尼/峇里島入境】最新入境規定、簽證申請、文件整理\"},{\"content\":\"壹、 印尼政府於2023年6月7日頒布行政命令，暫停給予包含我國在內的全球159國免簽證待遇，持我國護照欲入境印尼短期商旅之旅客，可申請落地簽證(VOA)或電子落地簽證(eVOA)。See more\",\"doc_type\":\"web_page\",\"link\":\"https://www.boca.gov.tw/sp-foof-countrycp-03-12-c23b0-02-1.html\",\"title\":\"印尼簽證及入境須知\"},{\"content\":\"自2025年6月12日起，印度尼西亚公民可适用240小时过境免签政策便捷来华，中国240小时过境免签政策适用国家范围增至55国。\",\"doc_type\":\"web_page\",\"link\":\"https://www.gov.cn/zhengce/zhengceku/202506/content_7027405.htm\",\"title\":\"国家移民管理局关于新增印度尼西亚为中国240小时过境免签 ...\"},{\"content\":\"GR.01.01 2025 年，將於2025年5月29日生效。他確認，希望延長簽證 和居留許可的峇里島遊客和長期訪客必須先透過印尼官 方移民網站在線註冊申請。遊客可以在 ...See more\",\"doc_type\":\"web_page\",\"link\":\"https://www.facebook.com/yourfriendinbali/posts/-bali-tourists-impacted-by-major-change-to-visa-rules-effective-immediately-%E5%B3%87%E9%87%8C%E5%B3%B6%E7%B0%BD/1129649319203258/\",\"title\":\"希望延長簽證和居留許可的峇里島遊客和長期訪客必須先\"},{\"content\":\"中国游客到巴厘岛可办理落地签。抵达巴厘岛登巴萨机场后，凭有效护照和往返机票在visa on arrival窗口即可办理，允许停留30天，价格25美元。 特别提示. 1、巴厘岛签证上 ...See more\",\"doc_type\":\"web_page\",\"link\":\"https://www.airchina.com.au/AU/CN/info/visa-infor/Indonesia.html\",\"title\":\"印度尼西亚\"},{\"content\":\"国家移民管理局今天发布公告，自2025年6月12日起，印度尼西亚公民可适用240小时过境免签政策便捷来华，中国240小时过境免签政策适用国家增至55国。See more\",\"doc_type\":\"web_page\",\"link\":\"https://www.nia.gov.cn/n897453/c1722989/content.html\",\"title\":\"新增印尼中国240小时过境免签政策适用国家扩展至55国\"},{\"content\":\"根据印尼相关政策要求，所有入境印尼的旅客需要完成两项申报：填写入境卡+健康码。 2024-08-30. 3371 · 10月起，印尼可能对中国实施免签政策！See more\",\"doc_type\":\"web_page\",\"link\":\"https://idn.125visa.com/policy/\",\"title\":\"印度尼西亚签证新政策解读\"}]}","description":"上海到巴厘岛旅游攻略 2025...","fileName":"上海到巴厘岛旅游攻略2025_search_result.txt","requestId":"geniesession-1754804265647-6966:1754804265663-6669"}
2025-08-10 13:38:36.652 INFO log_util.__aenter__ 6a665142-1ea5-4659-a4bb-19b8140c2419  add_by_content start...
2025-08-10 13:38:36.655 INFO log_util.__aenter__ 6a665142-1ea5-4659-a4bb-19b8140c2419  add start...
2025-08-10 13:38:36.655 INFO log_util.__aenter__ 6a665142-1ea5-4659-a4bb-19b8140c2419  get_by_file_id start...
2025-08-10 13:38:36.663 INFO log_util.__aexit__ 6a665142-1ea5-4659-a4bb-19b8140c2419  get_by_file_id cost=[8 ms]
2025-08-10 13:38:36.680 INFO log_util.__aenter__ 6a665142-1ea5-4659-a4bb-19b8140c2419  get_by_file_id start...
2025-08-10 13:38:36.682 INFO log_util.__aexit__ 6a665142-1ea5-4659-a4bb-19b8140c2419  get_by_file_id cost=[2 ms]
2025-08-10 13:38:36.682 INFO log_util.__aexit__ 6a665142-1ea5-4659-a4bb-19b8140c2419  add cost=[27 ms]
2025-08-10 13:38:36.683 INFO log_util.__aexit__ 6a665142-1ea5-4659-a4bb-19b8140c2419  add_by_content cost=[30 ms]
2025-08-10 13:38:36.683 INFO log_util.__aexit__ 6a665142-1ea5-4659-a4bb-19b8140c2419 POST /v1/file_tool/upload_file cost=[35 ms]
2025-08-10 13:38:36.930 INFO log_util.__aenter__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 exec ask_llm start...
2025-08-10 13:42:04.926 INFO log_util.__aexit__ 3c88941d-7d1f-46ee-9e7c-6b15643090b5 exec ask_llm cost=[207995 ms]
2025-08-10 13:42:04.937 INFO log_util.__aenter__ 7dbdae80-287b-4974-be14-1171fb1c6c81 POST /v1/file_tool/upload_file start...
2025-08-10 13:42:04.939 INFO middleware_util.custom_route_handler 7dbdae80-287b-4974-be14-1171fb1c6c81 POST /v1/file_tool/upload_file body={"content":"# 2025年上海至巴厘岛旅游全攻略\n\n## 一、巴厘岛概况与最佳旅游时间\n\n巴厘岛作为印度尼西亚最著名的旅游胜地，以其独特的文化、迷人的海滩和丰富的自然景观吸引着全球游客。根据气候特征，巴厘岛全年可分为**旱季(4-10月)**和**雨季(11-3月)**两个主要季节[[14]](https://blog.curatorstravel.com/popular-tourist-areas/bali)。\n\n**最佳旅游时间**为**7-8月**，这段时间降雨概率最低，平均气温保持在24°C至30°C(75°F至86°F)之间[[13]](https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/explore-balis-climate-festivals-under-sunny-skies/)。4月和9月作为**次佳选择**，游客较少且酒店价格相对优惠[[8]](https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/best-times-to-visit-bali-discover-colorful-festivals/)。\n\n*表：巴厘岛各月份气候特点与旅游适宜度*\n\n| 月份 | 气候特征 | 游客量 | 价格水平 | 推荐指数 |\n|------|----------|--------|----------|----------|\n| 4-6月 | 旱季初期，降雨少 | 中等 | 中等 | ★★★★☆ |\n| 7-8月 | 旱季高峰，几乎无雨 | 高峰 | 高昂 | ★★★★★ |\n| 9-10月 | 旱季末期，偶有阵雨 | 中等 | 中等 | ★★★★☆ |\n| 11-3月 | 雨季，降雨频繁 | 低 | 低廉 | ★★☆☆☆ |\n\n## 二、上海至巴厘岛交通指南\n\n### 1. 航班信息\n\n从上海出发前往巴厘岛(登巴萨国际机场，代码DPS)的航班选择丰富：\n\n- **直飞航班**：主要由中国东方航空、印尼鹰航等航空公司运营，飞行时间约6-7小时[[31]](https://www.tianxun.com/routes/pvg/dps/shanghai-pudong-to-bali-denpasar.html)\n- **中转航班**：常见中转地包括新加坡、吉隆坡、雅加达等，总行程时间8-12小时不等[[35]](https://www.singaporeair.com/zh-cn/flights-from-shanghai-to-denpasar-bali)\n\n*表：2025年8月上海至巴厘岛主要航空公司票价对比*\n\n| 航空公司 | 航班类型 | 最低价格(CNY) | 平均飞行时间 | 备注 |\n|----------|----------|--------------|--------------|------|\n| 中国东方航空 | 直飞 | 3,125 | 6小时30分 | 经济舱往返[[34]](https://flights.cathaypacific.com/destinations/sc_CN/%E4%BB%8E%E4%B8%8A%E6%B5%B7%E5%88%B0%E4%B8%B9%E5%B8%95%E6%B2%99%E7%9A%84%E8%88%AA%E7%8F%AD) |\n| 吉祥航空 | 直飞 | 3,184 | 6小时45分 | 8月29日出发[[36]](https://hk.trip.com/flights/shanghai-to-bali/airfares-sha-dps/) |\n| 新加坡航空 | 中转 | 4,972 | 9小时20分 | 经停新加坡[[35]](https://www.singaporeair.com/zh-cn/flights-from-shanghai-to-denpasar-bali) |\n| 马来西亚航空 | 中转 | 3,800 | 10小时15分 | 经停吉隆坡[[38]](https://www.booking.com/flights/destination/to-region/id/bali.zh-cn.html) |\n\n**机票预订技巧**：\n- 提前2-3个月预订可获得更优惠价格\n- 9月机票价格显著下降，最低可达CNY3,006[[36]](https://hk.trip.com/flights/shanghai-to-bali/airfares-sha-dps/)\n- 考虑使用\"附近机场\"搜索功能扩大选择范围[[32]](https://www.tianxun.com/routes/csha/blca/shanghai-to-bali.html)\n\n### 2. 机场交通\n\n抵达登巴萨国际机场后，前往主要旅游区的交通方式：\n\n- **出租车**：机场正规出租车柜台预订，至库塔约15分钟，费用约IDR150,000(约CNY70)[[6]](https://www.csair.cn/csvideo/2018/20180529_3/balidaolvyougonglue20180529.pdf)\n- **包车服务**：提前预订中文司机，适合家庭或团体，日均约CNY300-400\n- **酒店接送**：部分高端酒店提供免费接送服务\n\n## 三、签证与入境须知\n\n### 1. 签证政策\n\n根据2025年最新规定，中国公民前往巴厘岛旅游需办理以下签证之一：\n\n1. **电子落地签(e-VOA)**：\n   - 有效期30天\n   - 可在线提前申请(官网：https://molina.imigrasi.go.id/)\n   - 费用约USD35[[43]](https://zhuanlan.zhihu.com/p/1904687297951137854)\n\n2. **落地签(VOA)**：\n   - 抵达登巴萨机场后办理\n   - 需准备：护照(有效期6个月以上)、往返机票、酒店预订单\n   - 费用USD25[[48]](https://www.airchina.com.au/AU/CN/info/visa-infor/Indonesia.html)\n\n3. **过境免签**：\n   - 仅适用于经印尼前往第三国的旅客\n   - 停留不超过24小时[[46]](https://www.gov.cn/zhengce/zhengceku/202506/content_7027405.htm)\n\n### 2. 入境要求\n\n- **护照有效期**：至少6个月\n- **往返机票**：需出示确认的返程或续程机票[[41]](https://cs.mfa.gov.cn/zggmcg/ljmdd/yz_645708/ydnxy_648376/rjjl_648386/)\n- **健康申报**：取消COVID-19相关检测和隔离要求[[44]](https://www.klook.com/zh-TW/blog/indonesia-covid19-entry-restrictions/)\n- **海关申报**：禁止携带毒品、武器等违禁品，限制携带烟酒数量\n\n### 3. 重要提醒\n\n- 签证延期需通过印尼移民局官网在线申请[[47]](https://www.facebook.com/yourfriendinbali/posts/-bali-tourists-impacted-by-major-change-to-visa-rules-effective-immediately-%E5%B3%87%E9%87%8C%E5%B3%B6%E7%B0%BD/1129649319203258/)\n- 严格遵守当地法规，避免从事与签证目的不符的活动[[42]](http://denpasar.china-consulate.gov.cn/lsfw/tzgg/)\n- 无人机使用需提前申请许可，违者可能面临罚款[[42]](http://denpasar.china-consulate.gov.cn/lsfw/tzgg/)\n\n## 四、巴厘岛热门景点推荐\n\n### 1. 文化景观类\n\n1. **海神庙(Tanah Lot)**：\n   - 巴厘岛六大寺庙之一，建于16世纪\n   - 最佳参观时间：日落时分\n   - 门票：成人IDR60,000(约CNY28)[[10]](https://cn.tripadvisor.com/AttractionProductReview-g297694-d17486592-Bali_Swing_Activity_and_Tanah_Lot_Sunset_Trip_Packages-Denpasar_Bali.html)\n\n2. **乌布皇宫(Ubud Palace)**：\n   - 乌布王朝的皇家宫殿，展示巴厘岛传统建筑艺术\n   - 每晚有传统舞蹈表演\n   - 门票：IDR50,000(约CNY23)[[7]](https://www.getyourguide.com/zh-cn/wu-bu-de-l32246/)\n\n3. **凯恰克火舞表演**：\n   - 巴厘岛独特的传统舞蹈形式\n   - 表演地点：乌布及周边多个场所\n   - 门票含接送约USD25/人[[4]](https://www.getyourguide.com/zh-cn/ba-li-dao-l347/duo-ri-you-tc282/)\n\n### 2. 自然景观类\n\n1. **圣猴森林公园(Sacred Monkey Forest)**：\n   - 占地约12.5公顷的原始森林\n   - 栖息着600多只巴厘长尾猴\n   - 开放时间：08:00-18:00\n   - 门票：成人IDR80,000(约CNY37)，儿童IDR60,000[[2]](https://blog.curatorstravel.com/popular-tourist-areas/bali)\n\n2. **德格拉朗梯田(Tegalalang Rice Terrace)**：\n   - 联合国教科文组织文化遗产\n   - 最佳拍摄时间：清晨或傍晚\n   - 门票：IDR20,000(约CNY9)[[7]](https://www.getyourguide.com/zh-cn/wu-bu-de-l32246/)\n\n3. **巴杜尔火山(Mount Batur)**：\n   - 活火山，海拔1717米\n   - 日出徒步行程需凌晨2-3点出发\n   - 需向导带领，费用约IDR600,000/人(约CNY280)[[9]](https://www.klook.com/zh-TW/blog/trend-mount-batur-sunrise-trekking-bali/)\n\n### 3. 海滩度假类\n\n1. **库塔海滩(Kuta Beach)**：\n   - 巴厘岛最著名的海滩，适合冲浪初学者\n   - 开放时间：全天免费[[6]](https://www.csair.cn/csvideo/2018/20180529_3/balidaolvyougonglue20180529.pdf)\n   - 周边餐饮、购物设施完善\n\n2. **努沙杜瓦海滩(Nusa Dua Beach)**：\n   - 高端度假区，海水清澈平静\n   - 适合浮潜和水上活动\n   - 部分区域为酒店私有[[5]](https://www.expedia.com/cn/Bali.d602651.Travel)\n\n3. **佩尼达岛(Nusa Penida)**：\n   - 原始自然风光，需乘船前往\n   - 著名景点：精灵沙滩、破碎沙滩\n   - 一日游价格约IDR1,200,000/人(约CNY560)[[18]](https://zhuanlan.zhihu.com/p/23976699062)\n\n*表：巴厘岛主要景点分布及推荐游玩时间*\n\n| 区域 | 主要景点 | 推荐游玩时间 | 适合人群 |\n|------|----------|--------------|----------|\n| 乌布 | 圣猴森林、德格拉朗梯田、乌布皇宫 | 1-2天 | 文化爱好者、自然摄影者 |\n| 库塔 | 库塔海滩、水上乐园、购物中心 | 1天 | 年轻游客、家庭亲子 |\n| 努沙杜瓦 | 努沙杜瓦海滩、高尔夫球场 | 1-2天 | 高端度假者 |\n| 金巴兰 | 金巴兰海滩、海鲜市场 | 半天 | 美食爱好者 |\n| 佩尼达岛 | 精灵沙滩、天使浴池 | 1-2天 | 探险爱好者 |\n\n## 五、行程规划建议\n\n### 1. 经典行程推荐\n\n#### 方案一：5天4晚精华游\n\n- **D1**：抵达巴厘岛 - 库塔海滩日落 - 库塔市区晚餐\n- **D2**：乌布一日游(圣猴森林、德格拉朗梯田、乌布皇宫+传统舞蹈)\n- **D3**：佩尼达岛西线一日游(精灵沙滩、破碎沙滩)\n- **D4**：努沙杜瓦水上活动 - 金巴兰海鲜晚餐\n- **D5**：海神庙参观 - 返程[[3]](https://hk.trip.com/blog/bali-438-2days-itinerary-9998440313/)\n\n#### 方案二：9天8夜深度游(含科莫多)\n\n- **D1-D4**：巴厘岛部分(参考5天行程)\n- **D5**：飞往科莫多 - 下午自由活动\n- **D6**：科莫多国家公园一日游(科莫多龙)\n- **D7**：粉色沙滩 - 帕达尔岛徒步\n- **D8**：返回巴厘岛 - 水疗放松\n- **D9**：返程[[1]](https://hk.trip.com/blog/bali-438-komodo-1448014-9days-itinerary-10012237971/)\n\n### 2. 主题行程推荐\n\n#### 冲浪主题行程\n\n- 库塔海滩(初学者)\n- 仓古海滩(中级)\n- 乌鲁瓦图海滩(高级)\n- 专业冲浪教练指导\n\n#### 文化体验行程\n\n- 巴厘岛传统舞蹈课程\n- 银器制作工作坊\n- 巴厘岛烹饪课程\n- 乡村寺庙参观\n\n#### 蜜月浪漫行程\n\n- 乌鲁瓦图悬崖晚餐\n- 蓝梦岛浮潜\n- 私人游艇日落巡航\n- 高端水疗体验\n\n### 3. 实用建议\n\n- **交通安排**：景点间距离较远，建议包车(日均约IDR600,000/8小时)\n- **时间管理**：热门景点尽量避开10:00-14:00高峰时段\n- **门票预订**：凯恰克舞等表演提前1天预订[[4]](https://www.getyourguide.com/zh-cn/ba-li-dao-l347/duo-ri-you-tc282/)\n- **导游服务**：文化景点建议聘请中文导游(约IDR400,000/天)\n\n## 六、住宿选择指南\n\n### 1. 区域特点分析\n\n1. **库塔/雷吉安**：\n   - 优点：交通便利、夜生活丰富、价格适中\n   - 缺点：较为喧闹、海滩质量一般\n   - 适合：预算有限、喜欢热闹的年轻游客\n\n2. **水明漾**：\n   - 优点：时尚精品店、优质餐厅、设计感酒店\n   - 缺点：价格较高\n   - 适合：追求时尚、注重生活品质的游客\n\n3. **乌布**：\n   - 优点：文化氛围浓厚、自然环境优美\n   - 缺点：距离海滩较远(约1小时车程)\n   - 适合：文化爱好者、瑜伽修行者\n\n4. **努沙杜瓦**：\n   - 优点：高端度假区、私人海滩、设施完善\n   - 缺点：价格昂贵、缺乏本地特色\n   - 适合：家庭度假、蜜月旅行\n\n### 2. 酒店推荐\n\n*表：不同预算酒店推荐(2025年8月参考价格)*\n\n| 类型 | 酒店名称 | 区域 | 价格(CNY/晚) | 特色 |\n|------|----------|------|--------------|------|\n| 经济型 | POP! Hotel Kuta | 库塔 | 300-400 | 位置便利、干净舒适 |\n| 中端 | The Haven Bali | 水明漾 | 600-800 | 设计感强、泳池漂亮 |\n| 高端 | Kayumanis Ubud | 乌布 | 1,500-2,000 | 私人别墅、幽静自然 |\n| 奢华 | The Mulia | 努沙杜瓦 | 2,500+ | 顶级服务、私人海滩 |\n\n**预订技巧**：\n- 通过Expedia等平台预订机票+酒店套餐可节省15-20%[[5]](https://www.expedia.com/cn/Bali.d602651.Travel)\n- 提前3个月预订旺季(7-8月)住宿选择更多\n- 关注酒店官网的早鸟优惠和住赠活动\n\n## 七、餐饮与购物\n\n### 1. 必尝美食\n\n1. **巴厘岛烤猪饭(Babi Guling)**：\n   - 推荐餐厅：Warung Babi Guling Ibu Oka(乌布)\n   - 人均：IDR50,000(约CNY23)\n\n2. **脏鸭餐(Bebek Bengil)**：\n   - 推荐餐厅：Bebek Bengil Dirty Duck Diner(乌布)\n   - 人均：IDR150,000(约CNY70)\n\n3. **海鲜烧烤**：\n   - 推荐地点：金巴兰海滩海鲜市场\n   - 人均：IDR200,000-300,000(约CNY93-140)\n\n4. **印尼炒饭(Nasi Goreng)**：\n   - 街边小摊至高档餐厅均有提供\n   - 价格：IDR30,000-80,000(约CNY14-37)\n\n### 2. 购物推荐\n\n1. **传统市场**：\n   - 乌布市场：手工艺品、木雕、银器\n   - 苏卡瓦提市场：油画、纺织品\n   - 砍价技巧：从标价1/3开始还价\n\n2. **购物中心**：\n   - Beachwalk Shopping Center(库塔)\n   - Bali Collection(努沙杜瓦)\n   - 优势：明码标价、空调环境\n\n3. **特色商品**：\n   - 巴厘岛咖啡(Luwak咖啡)\n   - 天然香皂和精油\n   - 巴厘岛风格服饰\n\n### 3. 消费水平参考\n\n- 普通餐厅：IDR50,000-150,000/人(约CNY23-70)\n- 中档餐厅：IDR150,000-300,000/人(约CNY70-140)\n- 高档餐厅：IDR300,000+/人(约CNY140+)\n- 矿泉水(小瓶)：IDR10,000(约CNY5)\n- 本地啤酒(Bintang)：IDR35,000/瓶(约CNY16)\n\n## 八、实用贴士与安全须知\n\n### 1. 货币与支付\n\n- **货币**","description":"# 2025年上海至巴厘岛旅游全攻略\n\n## 一、巴厘岛概况与最佳旅游时间\n\n巴厘岛作为印度尼西亚最著名的旅游胜地，以其独特的文化、迷人的海滩和丰富的自然景观吸引着全球游客。根据气候特征，巴厘岛全年可分为**旱季(4-10月)**和**雨季(11-3月)**两个主要季节[[14]](https://blog.curatorstravel.com/popular-tourist-areas/bali)。\n\n**最佳旅游时间**为**7-8月**，这段时间降雨概率最低，平均气温保持在24°C至30°C(75°F至86°F)之间[[13]](https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/explore-balis-climate-festivals-under-sunny-skies/)。4月和9月作为**次佳选择**，游客较少且酒店价格相对优惠[[8]](https://www.agoda.com/zh-cn/travel-guides/indonesia/bali/best-times-to-visit-bali-discover-colorful-festivals/)。\n\n*表：巴厘岛各月份气候特点与旅游适宜度*\n\n| 月份 | 气候特征 | 游客量 | 价格水平 | 推荐指数 |\n|------|----------|--------|----------|----------|\n| 4-6月 | 旱季初期，降雨少 | 中等 | 中等 | ★★★★☆ |\n| 7-8月 | 旱季高峰，几乎无雨 | 高峰 | 高昂 | ★★★★★ |\n| 9-10月 | 旱季末期，偶有阵雨 | 中等 | 中等 | ★★★★☆ |\n| 11-3月 | 雨季，降雨频繁 | 低 | 低廉 | ★★☆☆☆ |\n\n## 二、上海至巴厘岛交通指南\n\n### 1. 航班信息\n\n从上海出发前往巴厘岛(登巴萨国际机场，代码DPS)的航班选择丰富：\n\n- **直飞航班**：主要由中国东方航空、印尼鹰航等航空公司运营，飞行时间约6-7小时[[31]](https://www.tianxun.com/routes/pvg/dps/shanghai-pudong-to-bali-denpasar.html)\n- **中转航班**：常见中转地包括新加坡、吉隆坡、雅加达等，总行程时间8-12小时不等[[35]](https://www.singaporeair.com/zh-cn/flights-from-shanghai-to-denpasar-bali)\n\n*表：2025年8月上海至巴厘岛主要航空公司票价对比*\n\n| 航空公司 | 航班类型 | 最低价格(CNY) | 平均飞行时间 | 备注 |\n|----------|----------|--------------|--------------|------|\n| 中国东方航空 | 直飞 | 3,125 | 6小时30分 | 经济舱往返[[34]](https://flights.cathaypacific.com/destinations/sc_CN/%E4%BB%8E%E4%B8%8A%E6%B5%B7%E5%88%B0%E4%B8%B9%E5%B8%95%E6%B2%99%E7%9A%84%E8%88%AA%E7%8F%AD) |\n| 吉祥航空 | 直飞 | 3,184 | 6小时45分 | 8月29日出发[[36]](https://hk.trip.com/...","fileName":"上海到巴厘岛旅游攻略2025的搜索结果.md","requestId":"geniesession-1754804265647-6966:1754804265663-6669"}
2025-08-10 13:42:04.942 INFO log_util.__aenter__ 7dbdae80-287b-4974-be14-1171fb1c6c81  add_by_content start...
2025-08-10 13:42:04.944 INFO log_util.__aenter__ 7dbdae80-287b-4974-be14-1171fb1c6c81  add start...
2025-08-10 13:42:04.944 INFO log_util.__aenter__ 7dbdae80-287b-4974-be14-1171fb1c6c81  get_by_file_id start...
2025-08-10 13:42:04.948 INFO log_util.__aexit__ 7dbdae80-287b-4974-be14-1171fb1c6c81  get_by_file_id cost=[3 ms]
2025-08-10 13:42:04.969 INFO log_util.__aenter__ 7dbdae80-287b-4974-be14-1171fb1c6c81  get_by_file_id start...
2025-08-10 13:42:04.973 INFO log_util.__aexit__ 7dbdae80-287b-4974-be14-1171fb1c6c81  get_by_file_id cost=[3 ms]
2025-08-10 13:42:04.973 INFO log_util.__aexit__ 7dbdae80-287b-4974-be14-1171fb1c6c81  add cost=[29 ms]
2025-08-10 13:42:04.974 INFO log_util.__aexit__ 7dbdae80-287b-4974-be14-1171fb1c6c81  add_by_content cost=[32 ms]
2025-08-10 13:42:04.975 INFO log_util.__aexit__ 7dbdae80-287b-4974-be14-1171fb1c6c81 POST /v1/file_tool/upload_file cost=[38 ms]
2025-08-10 13:42:17.634 INFO log_util.__aenter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e POST /v1/tool/report start...
2025-08-10 13:42:17.640 INFO middleware_util.custom_route_handler 3a6f81b9-ed8a-4b9a-a706-88d12516a48e POST /v1/tool/report body={"contentStream":true,"fileDescription":"2025年上海至巴厘岛旅游全攻略","fileName":"上海到巴厘岛旅游攻略2025.html","fileNames":["上海到巴厘岛旅游攻略2025_search_result.txt","上海到巴厘岛旅游攻略2025的搜索结果.md"],"fileType":"html","query":"帮我调研一下上海到巴厘岛的攻略","requestId":"geniesession-1754804265647-6966:1754804265663-6669","stream":true,"streamMode":{"mode":"token","token":10},"task":"根据已获取的信息，生成一份完整的HTML报告，内容包括巴厘岛概况、最佳旅游时间、交通指南、签证政策、热门景点推荐、行程规划建议、住宿选择、餐饮购物以及实用贴士等。"}
2025-08-10 13:42:17.645 INFO log_util.__enter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e enter report start...
2025-08-10 13:42:17.646 INFO log_util.__exit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e enter report cost=[0 ms]
2025-08-10 13:42:17.646 INFO log_util.__enter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e enter html_report start...
2025-08-10 13:42:17.648 INFO log_util.__exit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e enter html_report cost=[2 ms]
2025-08-10 13:42:17.648 INFO log_util.__aenter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  download_all_files start...
2025-08-10 13:42:17.649 INFO log_util.__aenter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  get_file_content start...
2025-08-10 13:42:17.651 INFO log_util.__aexit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e POST /v1/tool/report cost=[16 ms]
2025-08-10 13:42:17.655 INFO log_util.__aenter__ 7ac7030d-630e-4dbe-a95c-c3b8f12be5aa GET /v1/file_tool/preview/geniesession-1754804265647-6966:1754804265663-6669/上海到巴厘岛旅游攻略2025_search_result.txt start...
2025-08-10 13:42:17.656 INFO log_util.__aenter__ 7ac7030d-630e-4dbe-a95c-c3b8f12be5aa  get_by_file_id start...
2025-08-10 13:42:17.659 INFO log_util.__aexit__ 7ac7030d-630e-4dbe-a95c-c3b8f12be5aa  get_by_file_id cost=[3 ms]
2025-08-10 13:42:17.803 INFO log_util.__aexit__ 7ac7030d-630e-4dbe-a95c-c3b8f12be5aa GET /v1/file_tool/preview/geniesession-1754804265647-6966:1754804265663-6669/上海到巴厘岛旅游攻略2025_search_result.txt cost=[148 ms]
2025-08-10 13:42:17.808 INFO log_util.__aexit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  get_file_content cost=[159 ms]
2025-08-10 13:42:17.809 INFO log_util.__aenter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  get_file_content start...
2025-08-10 13:42:17.812 INFO log_util.__aenter__ 2d5ecfd8-5694-40dd-bdb2-893a4bf52475 GET /v1/file_tool/preview/geniesession-1754804265647-6966:1754804265663-6669/上海到巴厘岛旅游攻略2025的搜索结果.md start...
2025-08-10 13:42:17.814 INFO log_util.__aenter__ 2d5ecfd8-5694-40dd-bdb2-893a4bf52475  get_by_file_id start...
2025-08-10 13:42:17.819 INFO log_util.__aexit__ 2d5ecfd8-5694-40dd-bdb2-893a4bf52475  get_by_file_id cost=[4 ms]
2025-08-10 13:42:17.821 INFO log_util.__aexit__ 2d5ecfd8-5694-40dd-bdb2-893a4bf52475 GET /v1/file_tool/preview/geniesession-1754804265647-6966:1754804265663-6669/上海到巴厘岛旅游攻略2025的搜索结果.md cost=[9 ms]
2025-08-10 13:42:17.824 INFO log_util.__aexit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  get_file_content cost=[14 ms]
2025-08-10 13:42:17.825 INFO log_util.__aexit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  download_all_files cost=[177 ms]
2025-08-10 13:42:17.825 INFO log_util.__enter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  truncate_files start...
2025-08-10 13:42:17.826 INFO log_util.__exit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  truncate_files cost=[0 ms]
2025-08-10 13:42:17.826 INFO log_util.__enter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  truncate_files start...
2025-08-10 13:42:17.827 INFO log_util.__exit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  truncate_files cost=[0 ms]
2025-08-10 13:42:17.847 INFO log_util.__enter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e enter ask_llm start...
2025-08-10 13:42:17.851 INFO log_util.__exit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e enter ask_llm cost=[3 ms]
2025-08-10 13:42:17.997 INFO log_util.__aenter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e exec ask_llm start...
2025-08-10 13:45:39.911 INFO log_util.__aexit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e exec ask_llm cost=[201913 ms]
2025-08-10 13:45:39.918 INFO log_util.__aenter__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  upload_file start...
2025-08-10 13:45:39.921 INFO log_util.__aenter__ 35efc933-8707-4293-8e0e-043aecc9e2c9 POST /v1/file_tool/upload_file start...
2025-08-10 13:45:39.922 INFO middleware_util.custom_route_handler 35efc933-8707-4293-8e0e-043aecc9e2c9 POST /v1/file_tool/upload_file body={"requestId": "geniesession-1754804265647-6966:1754804265663-6669", "fileName": "\u4e0a\u6d77\u5230\u5df4\u5398\u5c9b\u65c5\u6e38\u653b\u75652025.html", "content": "\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>2025\u5e74\u5df4\u5398\u5c9b\u65c5\u6e38\u5168\u653b\u7565 - \u70ed\u5e26\u5929\u5802\u7684\u5b8c\u7f8e\u5047\u671f</title>\n    <link href=\"https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css\" rel=\"stylesheet\">\n    <script src=\"https://unpkg.com/echarts@5.4.3/dist/echarts.min.js\"></script>\n    <style>\n        .hover-underline:hover {\n            text-decoration: underline;\n            text-decoration-color: #007bff;\n        }\n        .chart-container {\n            height: 400px;\n            margin: 20px 0;\n        }\n        .citation {\n            color: #007bff;\n            cursor: pointer;\n        }\n        .citation:hover {\n            text-decoration: underline;\n        }\n    </style>\n</head>\n<body class=\"bg-gray-50 font-sans\">\n    <div class=\"container mx-auto px-4 py-8 max-w-6xl\">\n        <!-- \u5934\u90e8 -->\n        <header class=\"text-center mb-12\">\n            <h1 class=\"text-4xl font-bold text-blue-800 mb-4\">2025\u5e74\u5df4\u5398\u5c9b\u65c5\u6e38\u5168\u653b\u7565</h1>\n            <p class=\"text-xl text-gray-600\">\u70ed\u5e26\u5929\u5802\u7684\u5b8c\u7f8e\u5047\u671f\u6307\u5357</p>\n            <div class=\"mt-6 bg-blue-100 p-4 rounded-lg\">\n                <p class=\"text-blue-800\">\u66f4\u65b0\u65e5\u671f\uff1a2025\u5e748\u670810\u65e5 | \u6db5\u76d6\u6c14\u5019\u3001\u4ea4\u901a\u3001\u7b7e\u8bc1\u3001\u666f\u70b9\u3001\u4f4f\u5bbf\u7b49\u5168\u65b9\u4f4d\u4fe1\u606f</p>\n            </div>\n        </header>\n\n        <!-- \u5bfc\u822a -->\n        <nav class=\"sticky top-0 bg-white shadow-md rounded-lg mb-8 z-10\">\n            <ul class=\"flex flex-wrap justify-center py-4\">\n                <li class=\"mx-2\"><a href=\"#overview\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u6982\u51b5</a></li>\n                <li class=\"mx-2\"><a href=\"#best-time\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u6700\u4f73\u65f6\u95f4</a></li>\n                <li class=\"mx-2\"><a href=\"#transport\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u4ea4\u901a\u6307\u5357</a></li>\n                <li class=\"mx-2\"><a href=\"#visa\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u7b7e\u8bc1\u653f\u7b56</a></li>\n                <li class=\"mx-2\"><a href=\"#attractions\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u666f\u70b9\u63a8\u8350</a></li>\n                <li class=\"mx-2\"><a href=\"#itinerary\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u884c\u7a0b\u89c4\u5212</a></li>\n                <li class=\"mx-2\"><a href=\"#accommodation\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u4f4f\u5bbf\u9009\u62e9</a></li>\n                <li class=\"mx-2\"><a href=\"#dining\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u9910\u996e\u8d2d\u7269</a></li>\n                <li class=\"mx-2\"><a href=\"#tips\" class=\"text-blue-700 hover:text-blue-900 font-medium\">\u5b9e\u7528\u8d34\u58eb</a></li>\n            </ul>\n        </nav>\n\n        <!-- \u4e3b\u8981\u5185\u5bb9 -->\n        <main>\n            <!-- \u6982\u51b5\u90e8\u5206 -->\n            <section id=\"overview\" class=\"mb-16\">\n                <h2 class=\"text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2\">\u5df4\u5398\u5c9b\u6982\u51b5</h2>\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <p class=\"mb-4\">\u5df4\u5398\u5c9b\u4f5c\u4e3a\u5370\u5ea6\u5c3c\u897f\u4e9a\u6700\u8457\u540d\u7684\u65c5\u6e38\u80dc\u5730\uff0c\u4ee5\u5176\u72ec\u7279\u7684\u6587\u5316\u3001\u8ff7\u4eba\u7684\u6d77\u6ee9\u548c\u4e30\u5bcc\u7684\u81ea\u7136\u666f\u89c2\u5438\u5f15\u7740\u5168\u7403\u6e38\u5ba2\u3002\u6839\u636e\u6c14\u5019\u7279\u5f81\uff0c\u5df4\u5398\u5c9b\u5168\u5e74\u53ef\u5206\u4e3a<strong>\u65f1\u5b63(4-10\u6708)</strong>\u548c<strong>\u96e8\u5b63(11-3\u6708)</strong>\u4e24\u4e2a\u4e3b\u8981\u5b63\u8282\u3002</p>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mt-6\">\n                        <div>\n                            <h3 class=\"text-xl font-semibold text-blue-700 mb-3\">\u6c14\u5019\u7279\u70b9</h3>\n                            <p>\u5cc7\u91cc\u5cf6\u70ba\u71b1\u5e36\u5b63\u98a8\u6c23\u5019\uff0c\u8207\u99ac\u723e\u5730\u592b\u4e00\u6a23\uff0c\u4e26\u6c92\u6709\u56db\u5b63\u4e4b\u5206\uff0c\u53ea\u6709\u4e7e\u5b63\u3001\u96e8\u5b632\u7a2e\u5dee\u7570\uff0c\u6240\u4ee5\u5168\u5e74\u6c23\u6eab\u901a\u5e38\u6703\u7a69\u5b9a\u5728\u651d\u6c0f26\uff5e28\u5ea6\u4e4b\u9593\uff0c\u975e\u5e38\u9069\u5408\u5ea6\u5047\u3002</p>\n                        </div>\n                        <div>\n                            <h3 class=\"text-xl font-semibold text-blue-700 mb-3\">\u65c5\u6e38\u7279\u8272</h3>\n                            <p>\u5cc7\u91cc\u5cf6\u81ea\u7531\u884c\u600e\u9ebc\u73a9\uff1f\u7121\u8ad6\u662f\u6d77\u795e\u5edf\u7684\u5915\u967d\u7f8e\u666f\u3001\u5df4\u675c\u723e\u706b\u5c71\u7684\u5f92\u6b65\u65e5\u51fa\u884c\u7a0b\u3001\u6216\u662f\u885d\u6d6a\u6f5b\u6c34\u6d3b\u52d5\u8207\u5728\u5730\u7f8e\u98df\uff0c\u90fd\u6703\u8b93\u60a8\u5728\u5cc7\u91cc\u5cf6\u5275\u9020\u8a31\u591a\u7f8e\u597d\u56de\u61b6\uff01</p>\n                        </div>\n                    </div>\n                </div>\n            </section>\n\n            <!-- \u6700\u4f73\u65c5\u6e38\u65f6\u95f4 -->\n            <section id=\"best-time\" class=\"mb-16\">\n                <h2 class=\"text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2\">\u6700\u4f73\u65c5\u6e38\u65f6\u95f4</h2>\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <p class=\"mb-4\"><strong>\u6700\u4f73\u65c5\u6e38\u65f6\u95f4</strong>\u4e3a<strong>7-8\u6708</strong>\uff0c\u8fd9\u6bb5\u65f6\u95f4\u964d\u96e8\u6982\u7387\u6700\u4f4e\uff0c\u5e73\u5747\u6c14\u6e29\u4fdd\u6301\u572824\u00b0C\u81f330\u00b0C(75\u00b0F\u81f386\u00b0F)\u4e4b\u95f4\u30024\u6708\u548c9\u6708\u4f5c\u4e3a<strong>\u6b21\u4f73\u9009\u62e9</strong>\uff0c\u6e38\u5ba2\u8f83\u5c11\u4e14\u9152\u5e97\u4ef7\u683c\u76f8\u5bf9\u4f18\u60e0\u3002</p>\n                    \n                    <div class=\"chart-container\" id=\"climate-chart\"></div>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mt-6\">\n                        <div>\n                            <h3 class=\"text-xl font-semibold text-blue-700 mb-3\">\u65fa\u5b63\u7279\u70b9</h3>\n                            <p>\u9019\u6bb5\u671f\u9593\u4f86\u5cc7\u91cc\u5cf6\u65c5\u904a\u65e5\u7167\u6642\u9593\u9577\uff0c\u967d\u5149\u5145\u8db3\uff0c\u56e0\u6b644-10\u6708\u901a\u5e38\u662f\u5cc7\u91cc\u5cf6\u7684\u65c5\u904a\u65fa\u5b63\uff0c\u5c24\u5176\u662f7-8\u6708\uff0c\u9069\u9022\u5b78\u751f\u6691\u5047\uff0c\u4ee5\u53ca\u525b\u597d\u662f\u8a31\u591a\u5370\u5c3c\u7684\u570b\u5b9a\u5047\u671f\u671f\u9593\uff0c\u6a5f\u7968\u4f4f\u5bbf\u50f9\u683c\u76f8\u5c0d\u8f03\u6602\u8cb4\u3002</p>\n                        </div>\n                        <div>\n                            <h3 class=\"text-xl font-semibold text-blue-700 mb-3\">\u6de1\u5b63\u4f18\u52bf</h3>\n                            <p>\u5982\u679c\u60a8\u6b63\u5728\u5bfb\u627e\u66f4\u8f7b\u677e\u7684\u4f53\u9a8c\uff0c\u8bf7\u8003\u8651\u57284\u6708\u548c9\u6708\u7684\u975e\u9ad8\u5cf0\u6708\u4efd\u53c2\u89c2\u3002\u60a8\u4e0d\u4ec5\u4f1a\u4eab\u53d7\u66f4\u5c11\u7684\u6e38\u5ba2\uff0c\u800c\u4e14\u60a8\u8fd8\u4f1a\u53d1\u73b0\u8bb8\u591a\u9152\u5e97\u548c\u9910\u9986\u63d0\u4f9b\u8bf1\u4eba\u7684\u6298\u6263\u3002</p>\n                        </div>\n                    </div>\n                </div>\n            </section>\n\n            <!-- \u4ea4\u901a\u6307\u5357 -->\n            <section id=\"transport\" class=\"mb-16\">\n                <h2 class=\"text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2\">\u4ea4\u901a\u6307\u5357</h2>\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h3 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u822a\u73ed\u4fe1\u606f</h3>\n                    <p class=\"mb-4\">\u4ece\u4e0a\u6d77\u51fa\u53d1\u524d\u5f80\u5df4\u5398\u5c9b(\u767b\u5df4\u8428\u56fd\u9645\u673a\u573a\uff0c\u4ee3\u7801DPS)\u7684\u822a\u73ed\u9009\u62e9\u4e30\u5bcc\uff1a</p>\n                    \n                    <div class=\"overflow-x-auto\">\n                        <table class=\"min-w-full bg-white border border-gray-200\">\n                            <thead class=\"bg-blue-100\">\n                                <tr>\n                                    <th class=\"py-2 px-4 border-b\">\u822a\u7a7a\u516c\u53f8</th>\n                                    <th class=\"py-2 px-4 border-b\">\u822a\u73ed\u7c7b\u578b</th>\n                                    <th class=\"py-2 px-4 border-b\">\u6700\u4f4e\u4ef7\u683c(CNY)</th>\n                                    <th class=\"py-2 px-4 border-b\">\u5e73\u5747\u98de\u884c\u65f6\u95f4</th>\n                                </tr>\n                            </thead>\n                            <tbody>\n                                <tr>\n                                    <td class=\"py-2 px-4 border-b\">\u4e2d\u56fd\u4e1c\u65b9\u822a\u7a7a</td>\n                                    <td class=\"py-2 px-4 border-b\">\u76f4\u98de</td>\n                                    <td class=\"py-2 px-4 border-b\">3,125</td>\n                                    <td class=\"py-2 px-4 border-b\">6\u5c0f\u65f630\u5206</td>\n                                </tr>\n                                <tr class=\"bg-gray-50\">\n                                    <td class=\"py-2 px-4 border-b\">\u5409\u7965\u822a\u7a7a</td>\n                                    <td class=\"py-2 px-4 border-b\">\u76f4\u98de</td>\n                                    <td class=\"py-2 px-4 border-b\">3,184</td>\n                                    <td class=\"py-2 px-4 border-b\">6\u5c0f\u65f645\u5206</td>\n                                </tr>\n                                <tr>\n                                    <td class=\"py-2 px-4 border-b\">\u65b0\u52a0\u5761\u822a\u7a7a</td>\n                                    <td class=\"py-2 px-4 border-b\">\u4e2d\u8f6c</td>\n                                    <td class=\"py-2 px-4 border-b\">4,972</td>\n                                    <td class=\"py-2 px-4 border-b\">9\u5c0f\u65f620\u5206</td>\n                                </tr>\n                                <tr class=\"bg-gray-50\">\n                                    <td class=\"py-2 px-4 border-b\">\u9a6c\u6765\u897f\u4e9a\u822a\u7a7a</td>\n                                    <td class=\"py-2 px-4 border-b\">\u4e2d\u8f6c</td>\n                                    <td class=\"py-2 px-4 border-b\">3,800</td>\n                                    <td class=\"py-2 px-4 border-b\">10\u5c0f\u65f615\u5206</td>\n                                </tr>\n                            </tbody>\n                        </table>\n                    </div>\n                    \n                    <div class=\"mt-6\">\n                        <h4 class=\"text-lg font-semibold text-blue-700 mb-2\">\u673a\u7968\u9884\u8ba2\u6280\u5de7</h4>\n                        <ul class=\"list-disc pl-5 space-y-1\">\n                            <li>\u63d0\u524d2-3\u4e2a\u6708\u9884\u8ba2\u53ef\u83b7\u5f97\u66f4\u4f18\u60e0\u4ef7\u683c</li>\n                            <li>9\u6708\u673a\u7968\u4ef7\u683c\u663e\u8457\u4e0b\u964d\uff0c\u6700\u4f4e\u53ef\u8fbeCNY3,006</li>\n                            <li>\u8003\u8651\u4f7f\u7528\"\u9644\u8fd1\u673a\u573a\"\u641c\u7d22\u529f\u80fd\u6269\u5927\u9009\u62e9\u8303\u56f4</li>\n                        </ul>\n                    </div>\n                    \n                    <h3 class=\"text-2xl font-semibold text-blue-700 mt-8 mb-4\">\u673a\u573a\u4ea4\u901a</h3>\n                    <p class=\"mb-4\">\u62b5\u8fbe\u767b\u5df4\u8428\u56fd\u9645\u673a\u573a\u540e\uff0c\u524d\u5f80\u4e3b\u8981\u65c5\u6e38\u533a\u7684\u4ea4\u901a\u65b9\u5f0f\uff1a</p>\n                    <ul class=\"list-disc pl-5 space-y-1\">\n                        <li><strong>\u51fa\u79df\u8f66</strong>\uff1a\u673a\u573a\u6b63\u89c4\u51fa\u79df\u8f66\u67dc\u53f0\u9884\u8ba2\uff0c\u81f3\u5e93\u5854\u7ea615\u5206\u949f\uff0c\u8d39\u7528\u7ea6IDR150,000(\u7ea6CNY70)</li>\n                        <li><strong>\u5305\u8f66\u670d\u52a1</strong>\uff1a\u63d0\u524d\u9884\u8ba2\u4e2d\u6587\u53f8\u673a\uff0c\u9002\u5408\u5bb6\u5ead\u6216\u56e2\u4f53\uff0c\u65e5\u5747\u7ea6CNY300-400</li>\n                        <li><strong>\u9152\u5e97\u63a5\u9001</strong>\uff1a\u90e8\u5206\u9ad8\u7aef\u9152\u5e97\u63d0\u4f9b\u514d\u8d39\u63a5\u9001\u670d\u52a1</li>\n                    </ul>\n                </div>\n            </section>\n\n            <!-- \u7b7e\u8bc1\u653f\u7b56 -->\n            <section id=\"visa\" class=\"mb-16\">\n                <h2 class=\"text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2\">\u7b7e\u8bc1\u4e0e\u5165\u5883\u987b\u77e5</h2>\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <h3 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u7b7e\u8bc1\u653f\u7b56</h3>\n                    <p class=\"mb-4\">\u6839\u636e2025\u5e74\u6700\u65b0\u89c4\u5b9a\uff0c\u4e2d\u56fd\u516c\u6c11\u524d\u5f80\u5df4\u5398\u5c9b\u65c5\u6e38\u9700\u529e\u7406\u4ee5\u4e0b\u7b7e\u8bc1\u4e4b\u4e00\uff1a</p>\n                    \n                    <div class=\"grid md:grid-cols-2 gap-6 mb-6\">\n                        <div class=\"border border-blue-200 rounded-lg p-4\">\n                            <h4 class=\"text-lg font-semibold text-blue-700 mb-2\">\u7535\u5b50\u843d\u5730\u7b7e(e-VOA)</h4>\n                            <ul class=\"list-disc pl-5 space-y-1\">\n                                <li>\u6709\u6548\u671f30\u5929</li>\n                                <li>\u53ef\u5728\u7ebf\u63d0\u524d\u7533\u8bf7(\u5b98\u7f51\uff1ahttps://molina.imigrasi.go.id/)</li>\n                                <li>\u8d39\u7528\u7ea6USD35</li>\n                            </ul>\n                        </div>\n                        <div class=\"border border-blue-200 rounded-lg p-4\">\n                            <h4 class=\"text-lg font-semibold text-blue-700 mb-2\">\u843d\u5730\u7b7e(VOA)</h4>\n                            <ul class=\"list-disc pl-5 space-y-1\">\n                                <li>\u62b5\u8fbe\u767b\u5df4\u8428\u673a\u573a\u540e\u529e\u7406</li>\n                                <li>\u9700\u51c6\u5907\uff1a\u62a4\u7167(\u6709\u6548\u671f6\u4e2a\u6708\u4ee5\u4e0a)\u3001\u5f80\u8fd4\u673a\u7968\u3001\u9152\u5e97\u9884\u8ba2\u5355</li>\n                                <li>\u8d39\u7528USD25</li>\n                            </ul>\n                        </div>\n                    </div>\n                    \n                    <h3 class=\"text-2xl font-semibold text-blue-700 mt-8 mb-4\">\u5165\u5883\u8981\u6c42</h3>\n                    <ul class=\"list-disc pl-5 space-y-1 mb-6\">\n                        <li><strong>\u62a4\u7167\u6709\u6548\u671f</strong>\uff1a\u81f3\u5c116\u4e2a\u6708</li>\n                        <li><strong>\u5f80\u8fd4\u673a\u7968</strong>\uff1a\u9700\u51fa\u793a\u786e\u8ba4\u7684\u8fd4\u7a0b\u6216\u7eed\u7a0b\u673a\u7968</li>\n                        <li><strong>\u5065\u5eb7\u7533\u62a5</strong>\uff1a\u53d6\u6d88COVID-19\u76f8\u5173\u68c0\u6d4b\u548c\u9694\u79bb\u8981\u6c42</li>\n                        <li><strong>\u6d77\u5173\u7533\u62a5</strong>\uff1a\u7981\u6b62\u643a\u5e26\u6bd2\u54c1\u3001\u6b66\u5668\u7b49\u8fdd\u7981\u54c1\uff0c\u9650\u5236\u643a\u5e26\u70df\u9152\u6570\u91cf</li>\n                    </ul>\n                    \n                    <div class=\"bg-yellow-100 border-l-4 border-yellow-500 p-4\">\n                        <h4 class=\"text-lg font-semibold text-yellow-800 mb-2\">\u91cd\u8981\u63d0\u9192</h4>\n                        <ul class=\"list-disc pl-5 space-y-1\">\n                            <li>\u7b7e\u8bc1\u5ef6\u671f\u9700\u901a\u8fc7\u5370\u5c3c\u79fb\u6c11\u5c40\u5b98\u7f51\u5728\u7ebf\u7533\u8bf7</li>\n                            <li>\u4e25\u683c\u9075\u5b88\u5f53\u5730\u6cd5\u89c4\uff0c\u907f\u514d\u4ece\u4e8b\u4e0e\u7b7e\u8bc1\u76ee\u7684\u4e0d\u7b26\u7684\u6d3b\u52a8</li>\n                            <li>\u65e0\u4eba\u673a\u4f7f\u7528\u9700\u63d0\u524d\u7533\u8bf7\u8bb8\u53ef\uff0c\u8fdd\u8005\u53ef\u80fd\u9762\u4e34\u7f5a\u6b3e</li>\n                        </ul>\n                    </div>\n                </div>\n            </section>\n\n            <!-- \u666f\u70b9\u63a8\u8350 -->\n            <section id=\"attractions\" class=\"mb-16\">\n                <h2 class=\"text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2\">\u70ed\u95e8\u666f\u70b9\u63a8\u8350</h2>\n                <div class=\"bg-white rounded-lg shadow-md p-6\">\n                    <div class=\"grid md:grid-cols-3 gap-6\">\n                        <!-- \u6587\u5316\u666f\u89c2 -->\n                        <div>\n                            <h3 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u6587\u5316\u666f\u89c2\u7c7b</h3>\n                            <div class=\"space-y-4\">\n                                <div class=\"border border-gray-200 rounded-lg p-4\">\n                                    <h4 class=\"text-lg font-semibold mb-2\">\u6d77\u795e\u5e99(Tanah Lot)</h4>\n                                    <p class=\"text-sm mb-2\">\u5df4\u5398\u5c9b\u516d\u5927\u5bfa\u5e99\u4e4b\u4e00\uff0c\u5efa\u4e8e16\u4e16\u7eaa</p>\n                                    <p class=\"text-sm\"><strong>\u95e8\u7968\uff1a</strong>\u6210\u4ebaIDR60,000(\u7ea6CNY28)</p>\n                                </div>\n                                <div class=\"border border-gray-200 rounded-lg p-4\">\n                                    <h4 class=\"text-lg font-semibold mb-2\">\u4e4c\u5e03\u7687\u5bab(Ubud Palace)</h4>\n                                    <p class=\"text-sm mb-2\">\u4e4c\u5e03\u738b\u671d\u7684\u7687\u5bb6\u5bab\u6bbf\uff0c\u5c55\u793a\u5df4\u5398\u5c9b\u4f20\u7edf\u5efa\u7b51\u827a\u672f</p>\n                                    <p class=\"text-sm\"><strong>\u95e8\u7968\uff1a</strong>IDR50,000(\u7ea6CNY23)</p>\n                                </div>\n                            </div>\n                        </div>\n                        \n                        <!-- \u81ea\u7136\u666f\u89c2 -->\n                        <div>\n                            <h3 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u81ea\u7136\u666f\u89c2\u7c7b</h3>\n                            <div class=\"space-y-4\">\n                                <div class=\"border border-gray-200 rounded-lg p-4\">\n                                    <h4 class=\"text-lg font-semibold mb-2\">\u5723\u7334\u68ee\u6797\u516c\u56ed(Sacred Monkey Forest)</h4>\n                                    <p class=\"text-sm mb-2\">\u5360\u5730\u7ea612.5\u516c\u9877\u7684\u539f\u59cb\u68ee\u6797\uff0c\u6816\u606f\u7740600\u591a\u53ea\u5df4\u5398\u957f\u5c3e\u7334</p>\n                                    <p class=\"text-sm\"><strong>\u95e8\u7968\uff1a</strong>\u6210\u4ebaIDR80,000(\u7ea6CNY37)</p>\n                                </div>\n                                <div class=\"border border-gray-200 rounded-lg p-4\">\n                                    <h4 class=\"text-lg font-semibold mb-2\">\u5df4\u675c\u5c14\u706b\u5c71(Mount Batur)</h4>\n                                    <p class=\"text-sm mb-2\">\u6d3b\u706b\u5c71\uff0c\u6d77\u62d41717\u7c73\uff0c\u65e5\u51fa\u5f92\u6b65\u884c\u7a0b\u9700\u51cc\u66682-3\u70b9\u51fa\u53d1</p>\n                                    <p class=\"text-sm\"><strong>\u8d39\u7528\uff1a</strong>\u7ea6IDR600,000/\u4eba(\u7ea6CNY280)</p>\n                                </div>\n                            </div>\n                        </div>\n                        \n                        <!-- \u6d77\u6ee9\u5ea6\u5047 -->\n                        <div>\n                            <h3 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u6d77\u6ee9\u5ea6\u5047\u7c7b</h3>\n                            <div class=\"space-y-4\">\n                                <div class=\"border border-gray-200 rounded-lg p-4\">\n                                    <h4 class=\"text-lg font-semibold mb-2\">\u5e93\u5854\u6d77\u6ee9(Kuta Beach)</h4>\n                                    <p class=\"text-sm mb-2\">\u5df4\u5398\u5c9b\u6700\u8457\u540d\u7684\u6d77\u6ee9\uff0c\u9002\u5408\u51b2\u6d6a\u521d\u5b66\u8005</p>\n                                    <p class=\"text-sm\"><strong>\u5f00\u653e\u65f6\u95f4\uff1a</strong>\u5168\u5929\u514d\u8d39</p>\n                                </div>\n                                <div class=\"border border-gray-200 rounded-lg p-4\">\n                                    <h4 class=\"text-lg font-semibold mb-2\">\u4f69\u5c3c\u8fbe\u5c9b(Nusa Penida)</h4>\n                                    <p class=\"text-sm mb-2\">\u539f\u59cb\u81ea\u7136\u98ce\u5149\uff0c\u8457\u540d\u666f\u70b9\uff1a\u7cbe\u7075\u6c99\u6ee9\u3001\u7834\u788e\u6c99\u6ee9</p>\n                                    <p class=\"text-sm\"><strong>\u4e00\u65e5\u6e38\u4ef7\u683c\uff1a</strong>\u7ea6IDR1,200,000/\u4eba(\u7ea6CNY560)</p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <div class=\"mt-8\">\n                        <h3 class=\"text-2xl font-semibold text-blue-700 mb-4\">\u666f\u70b9\u5206\u5e03\u4e0e\u63a8\u8350\u6e38\u73a9\u65f6\u95f4</h3>\n                        <div class=\"overflow-x-auto\">\n                            <table class=\"min-w-full bg-white border border-gray-200\">\n                                <thead class=\"bg-blue-100\">\n                                    <tr>\n                                        <th", "description": "\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>2025\u5e74\u5df4\u5398\u5c9b\u65c5\u6e38\u5168\u653b\u7565 - \u70ed\u5e26\u5929\u5802\u7684\u5b8c\u7f8e\u5047\u671f</title>\n    <link"}
2025-08-10 13:45:39.924 INFO log_util.__aenter__ 35efc933-8707-4293-8e0e-043aecc9e2c9  add_by_content start...
2025-08-10 13:45:39.927 INFO log_util.__aenter__ 35efc933-8707-4293-8e0e-043aecc9e2c9  add start...
2025-08-10 13:45:39.929 INFO log_util.__aenter__ 35efc933-8707-4293-8e0e-043aecc9e2c9  get_by_file_id start...
2025-08-10 13:45:39.933 INFO log_util.__aexit__ 35efc933-8707-4293-8e0e-043aecc9e2c9  get_by_file_id cost=[4 ms]
2025-08-10 13:45:39.948 INFO log_util.__aenter__ 35efc933-8707-4293-8e0e-043aecc9e2c9  get_by_file_id start...
2025-08-10 13:45:39.951 INFO log_util.__aexit__ 35efc933-8707-4293-8e0e-043aecc9e2c9  get_by_file_id cost=[2 ms]
2025-08-10 13:45:39.953 INFO log_util.__aexit__ 35efc933-8707-4293-8e0e-043aecc9e2c9  add cost=[25 ms]
2025-08-10 13:45:39.953 INFO log_util.__aexit__ 35efc933-8707-4293-8e0e-043aecc9e2c9  add_by_content cost=[28 ms]
2025-08-10 13:45:39.954 INFO log_util.__aexit__ 35efc933-8707-4293-8e0e-043aecc9e2c9 POST /v1/file_tool/upload_file cost=[32 ms]
2025-08-10 13:45:39.957 INFO log_util.__aexit__ 3a6f81b9-ed8a-4b9a-a706-88d12516a48e  upload_file cost=[38 ms]
2025-08-10 14:18:00.594 INFO log_util.__aenter__ 72b810ae-f1c5-440f-8e05-eeb394ecb5c6 GET /v1/file_tool/preview/geniesession-1754804265647-6966:1754804265663-6669/上海到巴厘岛旅游攻略2025.html start...
2025-08-10 14:18:00.595 INFO log_util.__aenter__ 72b810ae-f1c5-440f-8e05-eeb394ecb5c6  get_by_file_id start...
2025-08-10 14:18:00.598 INFO log_util.__aexit__ 72b810ae-f1c5-440f-8e05-eeb394ecb5c6  get_by_file_id cost=[3 ms]
2025-08-10 14:18:00.599 INFO log_util.__aexit__ 72b810ae-f1c5-440f-8e05-eeb394ecb5c6 GET /v1/file_tool/preview/geniesession-1754804265647-6966:1754804265663-6669/上海到巴厘岛旅游攻略2025.html cost=[5 ms]
