2025-08-10 00:40:53.035 INFO log_util.__aenter__ e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file start...
2025-08-10 00:40:53.036 INFO middleware_util.custom_route_handler e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file body={"content":"搜索结果已保存，包含理想汽车的负面新闻和争议事件。","description":"整理关于理想汽车的负面新闻内容","fileName":"理想汽车负面新闻.md","requestId":"geniesession-1754757551055-129:1754757612700-5946"}
2025-08-10 00:40:53.038 INFO log_util.__aenter__ e680ec57-22e1-485b-ba4f-667debb5a95c  add_by_content start...
2025-08-10 00:40:53.040 ERROR log_util.__aexit__ e680ec57-22e1-485b-ba4f-667debb5a95c  add_by_content error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniesession-1754757551055-129:1754757612700-5946'

2025-08-10 00:40:53.050 ERROR middleware_util.dispatch e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniesession-1754757551055-129:1754757612700-5946'

2025-08-10 00:40:53.051 INFO log_util.__aexit__ e680ec57-22e1-485b-ba4f-667debb5a95c POST /v1/file_tool/upload_file cost=[15 ms]
2025-08-10 00:41:02.725 INFO log_util.__aenter__ 154404a5-8ed4-4daa-959d-da11c7f24a22 POST /v1/tool/report start...
2025-08-10 00:41:02.726 INFO middleware_util.custom_route_handler 154404a5-8ed4-4daa-959d-da11c7f24a22 POST /v1/tool/report body={"contentStream":true,"fileDescription":"关于理想汽车的负面新闻整理报告","fileName":"理想汽车负面新闻报告.html","fileNames":[],"fileType":"html","query":"帮我查找一下有关 理想汽车 有关的负面新闻","requestId":"geniesession-1754757551055-129:1754757612700-5946","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并分析关于理想汽车的负面新闻，包括争议事件、用户投诉等内容，形成一份详细的HTML报告。"}
2025-08-10 00:41:02.730 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter report start...
2025-08-10 00:41:02.730 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter report cost=[0 ms]
2025-08-10 00:41:02.730 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter html_report start...
2025-08-10 00:41:02.731 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22 enter html_report cost=[1 ms]
2025-08-10 00:41:02.731 INFO log_util.__aenter__ 154404a5-8ed4-4daa-959d-da11c7f24a22  download_all_files start...
2025-08-10 00:41:02.731 INFO log_util.__aexit__ 154404a5-8ed4-4daa-959d-da11c7f24a22  download_all_files cost=[0 ms]
2025-08-10 00:41:02.732 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files start...
2025-08-10 00:41:02.732 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files cost=[0 ms]
2025-08-10 00:41:02.732 INFO log_util.__enter__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files start...
2025-08-10 00:41:02.733 INFO log_util.__exit__ 154404a5-8ed4-4daa-959d-da11c7f24a22  truncate_files cost=[0 ms]
2025-08-10 00:41:02.735 INFO log_util.__aexit__ 154404a5-8ed4-4daa-959d-da11c7f24a22 POST /v1/tool/report cost=[9 ms]
2025-08-10 00:41:11.511 INFO log_util.__aenter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c POST /v1/tool/report start...
2025-08-10 00:41:11.512 INFO middleware_util.custom_route_handler c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c POST /v1/tool/report body={"contentStream":true,"fileDescription":"关于理想汽车的负面新闻整理报告","fileName":"理想汽车负面新闻报告.md","fileNames":[],"fileType":"markdown","query":"帮我查找一下有关 理想汽车 有关的负面新闻","requestId":"geniesession-1754757551055-129:1754757612700-5946","stream":true,"streamMode":{"mode":"token","token":10},"task":"整理并分析关于理想汽车的负面新闻，包括争议事件、用户投诉等内容，形成一份详细的Markdown报告。"}
2025-08-10 00:41:11.513 INFO log_util.__enter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter report start...
2025-08-10 00:41:11.514 INFO log_util.__exit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter report cost=[0 ms]
2025-08-10 00:41:11.514 INFO log_util.__enter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter markdown_report start...
2025-08-10 00:41:11.515 INFO log_util.__exit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c enter markdown_report cost=[1 ms]
2025-08-10 00:41:11.515 INFO log_util.__aenter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  download_all_files start...
2025-08-10 00:41:11.515 INFO log_util.__aexit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  download_all_files cost=[0 ms]
2025-08-10 00:41:11.516 INFO log_util.__enter__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  truncate_files start...
2025-08-10 00:41:11.517 INFO log_util.__exit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c  truncate_files cost=[0 ms]
2025-08-10 00:41:11.518 INFO log_util.__aexit__ c8fa6180-4f4e-4e4e-a8ca-43d2eb69402c POST /v1/tool/report cost=[7 ms]
2025-08-10 00:41:16.788 INFO log_util.__aenter__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8 POST /v1/tool/deepsearch start...
2025-08-10 00:41:16.791 INFO middleware_util.custom_route_handler 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"理想汽车负面新闻 2025年","request_id":"geniesession-1754757551055-129:1754757612700-5946:n48zq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:41:16.794 INFO log_util.__enter__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  run start...
2025-08-10 00:41:16.795 INFO log_util.__exit__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  run cost=[0 ms]
2025-08-10 00:41:16.796 INFO deepsearch.run geniesession-1754757551055-129:1754757612700-5946:n48zq 第 1 轮深度搜索...
2025-08-10 00:41:16.796 INFO log_util.__aenter__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  query_decompose start...
2025-08-10 00:41:16.799 ERROR log_util.__aexit__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:41:16.800 INFO log_util.__aexit__ 7345c3d6-1e18-4834-b9b7-3e333bc1b7d8 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 00:43:14.920 INFO log_util.__aenter__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b POST /v1/tool/deepsearch start...
2025-08-10 00:43:14.922 INFO middleware_util.custom_route_handler 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅游攻略","request_id":"geniesession-1754757551055-129:1754757776162-4267:8c3aq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:14.925 INFO log_util.__enter__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  run start...
2025-08-10 00:43:14.925 INFO log_util.__exit__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  run cost=[0 ms]
2025-08-10 00:43:14.926 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:8c3aq 第 1 轮深度搜索...
2025-08-10 00:43:14.926 INFO log_util.__aenter__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  query_decompose start...
2025-08-10 00:43:14.930 ERROR log_util.__aexit__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:14.931 INFO log_util.__aexit__ 5acb9f71-53b6-4f0f-84dc-64e8bd1f6e4b POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 00:43:24.324 INFO log_util.__aenter__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1 POST /v1/tool/deepsearch start...
2025-08-10 00:43:24.325 INFO log_util.__aenter__ 7d8bb099-2077-4d25-b886-162d749dd4a4 POST /v1/tool/deepsearch start...
2025-08-10 00:43:24.327 INFO middleware_util.custom_route_handler 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛航班信息","request_id":"geniesession-1754757551055-129:1754757776162-4267:q6eks","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:24.329 INFO middleware_util.custom_route_handler 7d8bb099-2077-4d25-b886-162d749dd4a4 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游景点推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:xpghw","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:24.329 INFO log_util.__enter__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  run start...
2025-08-10 00:43:24.330 INFO log_util.__exit__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  run cost=[1 ms]
2025-08-10 00:43:24.331 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:q6eks 第 1 轮深度搜索...
2025-08-10 00:43:24.332 INFO log_util.__aenter__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  query_decompose start...
2025-08-10 00:43:24.335 ERROR log_util.__aexit__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:24.337 INFO log_util.__enter__ 7d8bb099-2077-4d25-b886-162d749dd4a4  run start...
2025-08-10 00:43:24.338 INFO log_util.__exit__ 7d8bb099-2077-4d25-b886-162d749dd4a4  run cost=[1 ms]
2025-08-10 00:43:24.339 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:xpghw 第 1 轮深度搜索...
2025-08-10 00:43:24.341 INFO log_util.__aenter__ 7d8bb099-2077-4d25-b886-162d749dd4a4  query_decompose start...
2025-08-10 00:43:24.343 ERROR log_util.__aexit__ 7d8bb099-2077-4d25-b886-162d749dd4a4  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:24.345 INFO log_util.__aexit__ 7e031a6f-e7e8-4e93-b2a1-f1f5eb96a8c1 POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:43:24.347 INFO log_util.__aexit__ 7d8bb099-2077-4d25-b886-162d749dd4a4 POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:43:32.897 INFO log_util.__aenter__ 3254fc2b-397f-4747-8d88-e6998df0a32f POST /v1/tool/deepsearch start...
2025-08-10 00:43:32.898 INFO log_util.__aenter__ 4a8ece0e-a361-451d-a535-9efe01afbcdd POST /v1/tool/deepsearch start...
2025-08-10 00:43:32.901 INFO middleware_util.custom_route_handler 3254fc2b-397f-4747-8d88-e6998df0a32f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海飞巴厘岛旅行指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:kifgj","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:32.902 INFO middleware_util.custom_route_handler 4a8ece0e-a361-451d-a535-9efe01afbcdd POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛自由行攻略","request_id":"geniesession-1754757551055-129:1754757776162-4267:9co8q","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:32.904 INFO log_util.__enter__ 3254fc2b-397f-4747-8d88-e6998df0a32f  run start...
2025-08-10 00:43:32.905 INFO log_util.__exit__ 3254fc2b-397f-4747-8d88-e6998df0a32f  run cost=[1 ms]
2025-08-10 00:43:32.908 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:kifgj 第 1 轮深度搜索...
2025-08-10 00:43:32.909 INFO log_util.__aenter__ 3254fc2b-397f-4747-8d88-e6998df0a32f  query_decompose start...
2025-08-10 00:43:32.912 ERROR log_util.__aexit__ 3254fc2b-397f-4747-8d88-e6998df0a32f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:32.914 INFO log_util.__enter__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  run start...
2025-08-10 00:43:32.915 INFO log_util.__exit__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  run cost=[0 ms]
2025-08-10 00:43:32.918 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:9co8q 第 1 轮深度搜索...
2025-08-10 00:43:32.918 INFO log_util.__aenter__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  query_decompose start...
2025-08-10 00:43:32.924 ERROR log_util.__aexit__ 4a8ece0e-a361-451d-a535-9efe01afbcdd  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:32.927 INFO log_util.__aexit__ 3254fc2b-397f-4747-8d88-e6998df0a32f POST /v1/tool/deepsearch cost=[29 ms]
2025-08-10 00:43:32.931 INFO log_util.__aexit__ 4a8ece0e-a361-451d-a535-9efe01afbcdd POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:43:39.952 INFO log_util.__aenter__ 64427b48-b277-423e-8093-4ec3326cae99 POST /v1/tool/deepsearch start...
2025-08-10 00:43:39.958 INFO middleware_util.custom_route_handler 64427b48-b277-423e-8093-4ec3326cae99 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛自由行详细攻略","request_id":"geniesession-1754757551055-129:1754757776162-4267:5gjim","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:39.962 INFO log_util.__enter__ 64427b48-b277-423e-8093-4ec3326cae99  run start...
2025-08-10 00:43:39.967 INFO log_util.__exit__ 64427b48-b277-423e-8093-4ec3326cae99  run cost=[5 ms]
2025-08-10 00:43:39.969 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:5gjim 第 1 轮深度搜索...
2025-08-10 00:43:39.974 INFO log_util.__aenter__ 64427b48-b277-423e-8093-4ec3326cae99  query_decompose start...
2025-08-10 00:43:39.979 ERROR log_util.__aexit__ 64427b48-b277-423e-8093-4ec3326cae99  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:39.983 INFO log_util.__aexit__ 64427b48-b277-423e-8093-4ec3326cae99 POST /v1/tool/deepsearch cost=[30 ms]
2025-08-10 00:43:48.570 INFO log_util.__aenter__ c8eb451e-2bcc-422a-9d20-c630a94035cb POST /v1/tool/deepsearch start...
2025-08-10 00:43:48.571 INFO middleware_util.custom_route_handler c8eb451e-2bcc-422a-9d20-c630a94035cb POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游必去景点","request_id":"geniesession-1754757551055-129:1754757776162-4267:kslue","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:48.572 INFO log_util.__enter__ c8eb451e-2bcc-422a-9d20-c630a94035cb  run start...
2025-08-10 00:43:48.572 INFO log_util.__exit__ c8eb451e-2bcc-422a-9d20-c630a94035cb  run cost=[0 ms]
2025-08-10 00:43:48.572 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:kslue 第 1 轮深度搜索...
2025-08-10 00:43:48.572 INFO log_util.__aenter__ c8eb451e-2bcc-422a-9d20-c630a94035cb  query_decompose start...
2025-08-10 00:43:48.574 ERROR log_util.__aexit__ c8eb451e-2bcc-422a-9d20-c630a94035cb  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:48.574 INFO log_util.__aexit__ c8eb451e-2bcc-422a-9d20-c630a94035cb POST /v1/tool/deepsearch cost=[4 ms]
2025-08-10 00:43:48.575 INFO log_util.__aenter__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b POST /v1/tool/deepsearch start...
2025-08-10 00:43:48.576 INFO middleware_util.custom_route_handler 3e5d05ec-3b54-48ab-a22c-997af447bb2b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛交通方式","request_id":"geniesession-1754757551055-129:1754757776162-4267:4iiv6","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:48.579 INFO log_util.__enter__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  run start...
2025-08-10 00:43:48.580 INFO log_util.__exit__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  run cost=[0 ms]
2025-08-10 00:43:48.580 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:4iiv6 第 1 轮深度搜索...
2025-08-10 00:43:48.580 INFO log_util.__aenter__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  query_decompose start...
2025-08-10 00:43:48.581 ERROR log_util.__aexit__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:48.581 INFO log_util.__aexit__ 3e5d05ec-3b54-48ab-a22c-997af447bb2b POST /v1/tool/deepsearch cost=[6 ms]
2025-08-10 00:43:57.943 INFO log_util.__aenter__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011 POST /v1/tool/deepsearch start...
2025-08-10 00:43:57.944 INFO log_util.__aenter__ e3139530-f419-4548-b244-2c74991df845 POST /v1/tool/deepsearch start...
2025-08-10 00:43:57.945 INFO middleware_util.custom_route_handler 9b9ab384-dda4-4bab-9cb4-434b7a52a011 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛最佳旅行季节","request_id":"geniesession-1754757551055-129:1754757776162-4267:z6g2i","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:57.945 INFO middleware_util.custom_route_handler e3139530-f419-4548-b244-2c74991df845 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游签证要求","request_id":"geniesession-1754757551055-129:1754757776162-4267:plla3","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:43:57.946 INFO log_util.__enter__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  run start...
2025-08-10 00:43:57.946 INFO log_util.__exit__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  run cost=[0 ms]
2025-08-10 00:43:57.946 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:z6g2i 第 1 轮深度搜索...
2025-08-10 00:43:57.946 INFO log_util.__aenter__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  query_decompose start...
2025-08-10 00:43:57.947 ERROR log_util.__aexit__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:57.948 INFO log_util.__enter__ e3139530-f419-4548-b244-2c74991df845  run start...
2025-08-10 00:43:57.948 INFO log_util.__exit__ e3139530-f419-4548-b244-2c74991df845  run cost=[0 ms]
2025-08-10 00:43:57.948 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:plla3 第 1 轮深度搜索...
2025-08-10 00:43:57.948 INFO log_util.__aenter__ e3139530-f419-4548-b244-2c74991df845  query_decompose start...
2025-08-10 00:43:57.949 ERROR log_util.__aexit__ e3139530-f419-4548-b244-2c74991df845  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:43:57.949 INFO log_util.__aexit__ 9b9ab384-dda4-4bab-9cb4-434b7a52a011 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-10 00:43:57.951 INFO log_util.__aexit__ e3139530-f419-4548-b244-2c74991df845 POST /v1/tool/deepsearch cost=[6 ms]
2025-08-10 00:44:05.346 INFO log_util.__aenter__ e6cd3884-a6e7-4706-b667-608eba324db7 POST /v1/tool/deepsearch start...
2025-08-10 00:44:05.347 INFO middleware_util.custom_route_handler e6cd3884-a6e7-4706-b667-608eba324db7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛自由行攻略 2025","request_id":"geniesession-1754757551055-129:1754757776162-4267:n9e3d","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:05.347 INFO log_util.__enter__ e6cd3884-a6e7-4706-b667-608eba324db7  run start...
2025-08-10 00:44:05.347 INFO log_util.__exit__ e6cd3884-a6e7-4706-b667-608eba324db7  run cost=[0 ms]
2025-08-10 00:44:05.348 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:n9e3d 第 1 轮深度搜索...
2025-08-10 00:44:05.348 INFO log_util.__aenter__ e6cd3884-a6e7-4706-b667-608eba324db7  query_decompose start...
2025-08-10 00:44:05.349 ERROR log_util.__aexit__ e6cd3884-a6e7-4706-b667-608eba324db7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:05.349 INFO log_util.__aexit__ e6cd3884-a6e7-4706-b667-608eba324db7 POST /v1/tool/deepsearch cost=[2 ms]
2025-08-10 00:44:13.563 INFO log_util.__aenter__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc POST /v1/tool/deepsearch start...
2025-08-10 00:44:13.564 INFO middleware_util.custom_route_handler 7287f9e2-0646-45a7-99b2-1c3f56c941fc POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛航班及住宿推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:dt0eb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:13.564 INFO log_util.__aenter__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3 POST /v1/tool/deepsearch start...
2025-08-10 00:44:13.565 INFO log_util.__enter__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  run start...
2025-08-10 00:44:13.565 INFO log_util.__exit__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  run cost=[0 ms]
2025-08-10 00:44:13.565 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:dt0eb 第 1 轮深度搜索...
2025-08-10 00:44:13.565 INFO log_util.__aenter__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  query_decompose start...
2025-08-10 00:44:13.566 ERROR log_util.__aexit__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:13.566 INFO log_util.__aexit__ 7287f9e2-0646-45a7-99b2-1c3f56c941fc POST /v1/tool/deepsearch cost=[3 ms]
2025-08-10 00:44:13.567 INFO middleware_util.custom_route_handler 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游注意事项","request_id":"geniesession-1754757551055-129:1754757776162-4267:lrb6s","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:13.568 INFO log_util.__enter__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  run start...
2025-08-10 00:44:13.568 INFO log_util.__exit__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  run cost=[0 ms]
2025-08-10 00:44:13.569 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:lrb6s 第 1 轮深度搜索...
2025-08-10 00:44:13.569 INFO log_util.__aenter__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  query_decompose start...
2025-08-10 00:44:13.570 ERROR log_util.__aexit__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:13.574 INFO log_util.__aexit__ 938ea0e9-4a36-4ddf-9c1f-c98878e5adb3 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 00:44:22.788 INFO log_util.__aenter__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5 POST /v1/tool/deepsearch start...
2025-08-10 00:44:22.789 INFO log_util.__aenter__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9 POST /v1/tool/deepsearch start...
2025-08-10 00:44:22.789 INFO middleware_util.custom_route_handler 4487d75d-e6b8-453d-a2e3-9c24f46441e5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海飞巴厘岛旅行贴士","request_id":"geniesession-1754757551055-129:1754757776162-4267:w874r","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:22.790 INFO middleware_util.custom_route_handler 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛自由行行程规划","request_id":"geniesession-1754757551055-129:1754757776162-4267:0ldpu","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:22.790 INFO log_util.__enter__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  run start...
2025-08-10 00:44:22.790 INFO log_util.__exit__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  run cost=[0 ms]
2025-08-10 00:44:22.790 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:w874r 第 1 轮深度搜索...
2025-08-10 00:44:22.791 INFO log_util.__aenter__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  query_decompose start...
2025-08-10 00:44:22.792 ERROR log_util.__aexit__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:22.792 INFO log_util.__enter__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  run start...
2025-08-10 00:44:22.792 INFO log_util.__exit__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  run cost=[0 ms]
2025-08-10 00:44:22.792 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:0ldpu 第 1 轮深度搜索...
2025-08-10 00:44:22.792 INFO log_util.__aenter__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  query_decompose start...
2025-08-10 00:44:22.793 ERROR log_util.__aexit__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:22.794 INFO log_util.__aexit__ 4487d75d-e6b8-453d-a2e3-9c24f46441e5 POST /v1/tool/deepsearch cost=[6 ms]
2025-08-10 00:44:22.794 INFO log_util.__aexit__ 96d3dfbb-ad11-41c5-b49c-df18c55cc7b9 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-10 00:44:31.934 INFO log_util.__aenter__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2 POST /v1/tool/deepsearch start...
2025-08-10 00:44:31.934 INFO log_util.__aenter__ 96033014-87f2-44e4-a714-c4617201b7f2 POST /v1/tool/deepsearch start...
2025-08-10 00:44:31.935 INFO middleware_util.custom_route_handler 41e9b646-c24b-4d13-9a8b-478c445b2ea2 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛机票预订指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:pc2ta","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:31.936 INFO middleware_util.custom_route_handler 96033014-87f2-44e4-a714-c4617201b7f2 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛热门景点介绍","request_id":"geniesession-1754757551055-129:1754757776162-4267:bsz98","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:31.937 INFO log_util.__enter__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  run start...
2025-08-10 00:44:31.937 INFO log_util.__exit__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  run cost=[0 ms]
2025-08-10 00:44:31.937 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:pc2ta 第 1 轮深度搜索...
2025-08-10 00:44:31.937 INFO log_util.__aenter__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  query_decompose start...
2025-08-10 00:44:31.939 ERROR log_util.__aexit__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:31.939 INFO log_util.__enter__ 96033014-87f2-44e4-a714-c4617201b7f2  run start...
2025-08-10 00:44:31.939 INFO log_util.__exit__ 96033014-87f2-44e4-a714-c4617201b7f2  run cost=[0 ms]
2025-08-10 00:44:31.939 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:bsz98 第 1 轮深度搜索...
2025-08-10 00:44:31.941 INFO log_util.__aenter__ 96033014-87f2-44e4-a714-c4617201b7f2  query_decompose start...
2025-08-10 00:44:31.943 ERROR log_util.__aexit__ 96033014-87f2-44e4-a714-c4617201b7f2  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:31.944 INFO log_util.__aexit__ 41e9b646-c24b-4d13-9a8b-478c445b2ea2 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 00:44:31.945 INFO log_util.__aexit__ 96033014-87f2-44e4-a714-c4617201b7f2 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 00:44:43.052 INFO log_util.__aenter__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8 POST /v1/tool/deepsearch start...
2025-08-10 00:44:43.055 INFO log_util.__aenter__ c3cea461-d8cf-4a82-aac8-04a75e368ccf POST /v1/tool/deepsearch start...
2025-08-10 00:44:43.062 INFO middleware_util.custom_route_handler 9618064b-f7d5-4459-aeed-32dd1b62b3a8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游美食推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:vqn64","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:43.064 INFO middleware_util.custom_route_handler c3cea461-d8cf-4a82-aac8-04a75e368ccf POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行必备物品","request_id":"geniesession-1754757551055-129:1754757776162-4267:i3npb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:43.071 INFO log_util.__enter__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  run start...
2025-08-10 00:44:43.077 INFO log_util.__exit__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  run cost=[6 ms]
2025-08-10 00:44:43.081 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vqn64 第 1 轮深度搜索...
2025-08-10 00:44:43.083 INFO log_util.__aenter__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  query_decompose start...
2025-08-10 00:44:43.093 ERROR log_util.__aexit__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:43.097 INFO log_util.__enter__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  run start...
2025-08-10 00:44:43.100 INFO log_util.__exit__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  run cost=[2 ms]
2025-08-10 00:44:43.107 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:i3npb 第 1 轮深度搜索...
2025-08-10 00:44:43.110 INFO log_util.__aenter__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  query_decompose start...
2025-08-10 00:44:43.116 ERROR log_util.__aexit__ c3cea461-d8cf-4a82-aac8-04a75e368ccf  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:43.123 INFO log_util.__aexit__ 9618064b-f7d5-4459-aeed-32dd1b62b3a8 POST /v1/tool/deepsearch cost=[70 ms]
2025-08-10 00:44:43.131 INFO log_util.__aexit__ c3cea461-d8cf-4a82-aac8-04a75e368ccf POST /v1/tool/deepsearch cost=[75 ms]
2025-08-10 00:44:49.955 INFO log_util.__aenter__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd POST /v1/tool/deepsearch start...
2025-08-10 00:44:49.959 INFO log_util.__aenter__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c POST /v1/tool/deepsearch start...
2025-08-10 00:44:49.963 INFO middleware_util.custom_route_handler 5f9a6841-9a3c-4a11-a57e-859795df5bbd POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行预算","request_id":"geniesession-1754757551055-129:1754757776162-4267:u06a0","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:49.966 INFO middleware_util.custom_route_handler 3ce02035-aa78-4e0b-a094-f84d5fe5a51c POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游文化体验","request_id":"geniesession-1754757551055-129:1754757776162-4267:794gs","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:49.969 INFO log_util.__enter__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  run start...
2025-08-10 00:44:49.973 INFO log_util.__exit__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  run cost=[4 ms]
2025-08-10 00:44:49.979 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:u06a0 第 1 轮深度搜索...
2025-08-10 00:44:49.983 INFO log_util.__aenter__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  query_decompose start...
2025-08-10 00:44:49.995 ERROR log_util.__aexit__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:49.999 INFO log_util.__enter__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  run start...
2025-08-10 00:44:50.007 INFO log_util.__exit__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  run cost=[7 ms]
2025-08-10 00:44:50.014 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:794gs 第 1 轮深度搜索...
2025-08-10 00:44:50.021 INFO log_util.__aenter__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  query_decompose start...
2025-08-10 00:44:50.032 ERROR log_util.__aexit__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:50.038 INFO log_util.__aexit__ 5f9a6841-9a3c-4a11-a57e-859795df5bbd POST /v1/tool/deepsearch cost=[82 ms]
2025-08-10 00:44:50.048 INFO log_util.__aexit__ 3ce02035-aa78-4e0b-a094-f84d5fe5a51c POST /v1/tool/deepsearch cost=[89 ms]
2025-08-10 00:44:56.436 INFO log_util.__aenter__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b POST /v1/tool/deepsearch start...
2025-08-10 00:44:56.438 INFO log_util.__aenter__ 3836e4e6-452e-499b-b304-940db5e41a9f POST /v1/tool/deepsearch start...
2025-08-10 00:44:56.441 INFO middleware_util.custom_route_handler 6bbd9269-4cc8-4810-b25c-c2aab8efe18b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游安全注意事项","request_id":"geniesession-1754757551055-129:1754757776162-4267:4v992","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:56.441 INFO middleware_util.custom_route_handler 3836e4e6-452e-499b-b304-940db5e41a9f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行常见问题","request_id":"geniesession-1754757551055-129:1754757776162-4267:92fhg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:44:56.443 INFO log_util.__enter__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  run start...
2025-08-10 00:44:56.446 INFO log_util.__exit__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  run cost=[2 ms]
2025-08-10 00:44:56.447 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:4v992 第 1 轮深度搜索...
2025-08-10 00:44:56.447 INFO log_util.__aenter__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  query_decompose start...
2025-08-10 00:44:56.455 ERROR log_util.__aexit__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:56.458 INFO log_util.__enter__ 3836e4e6-452e-499b-b304-940db5e41a9f  run start...
2025-08-10 00:44:56.459 INFO log_util.__exit__ 3836e4e6-452e-499b-b304-940db5e41a9f  run cost=[0 ms]
2025-08-10 00:44:56.460 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:92fhg 第 1 轮深度搜索...
2025-08-10 00:44:56.460 INFO log_util.__aenter__ 3836e4e6-452e-499b-b304-940db5e41a9f  query_decompose start...
2025-08-10 00:44:56.465 ERROR log_util.__aexit__ 3836e4e6-452e-499b-b304-940db5e41a9f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:44:56.467 INFO log_util.__aexit__ 6bbd9269-4cc8-4810-b25c-c2aab8efe18b POST /v1/tool/deepsearch cost=[30 ms]
2025-08-10 00:44:56.471 INFO log_util.__aexit__ 3836e4e6-452e-499b-b304-940db5e41a9f POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:45:02.951 INFO log_util.__aenter__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a POST /v1/tool/deepsearch start...
2025-08-10 00:45:02.952 INFO log_util.__aenter__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f POST /v1/tool/deepsearch start...
2025-08-10 00:45:02.954 INFO middleware_util.custom_route_handler 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游购物指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:vyf49","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:02.955 INFO middleware_util.custom_route_handler 636f6ac9-00a6-47ae-9201-8f7b51babc9f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行保险推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:jnxce","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:02.957 INFO log_util.__enter__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  run start...
2025-08-10 00:45:02.957 INFO log_util.__exit__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  run cost=[0 ms]
2025-08-10 00:45:02.958 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vyf49 第 1 轮深度搜索...
2025-08-10 00:45:02.958 INFO log_util.__aenter__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  query_decompose start...
2025-08-10 00:45:02.963 ERROR log_util.__aexit__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:02.964 INFO log_util.__enter__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  run start...
2025-08-10 00:45:02.964 INFO log_util.__exit__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  run cost=[0 ms]
2025-08-10 00:45:02.965 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:jnxce 第 1 轮深度搜索...
2025-08-10 00:45:02.966 INFO log_util.__aenter__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  query_decompose start...
2025-08-10 00:45:02.968 ERROR log_util.__aexit__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:02.972 INFO log_util.__aexit__ 6e6f2fd1-6352-4367-b112-6a3e9a9bcc6a POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:45:02.973 INFO log_util.__aexit__ 636f6ac9-00a6-47ae-9201-8f7b51babc9f POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:45:11.247 INFO log_util.__aenter__ 6a54b467-7187-4252-99b8-5573f1e44fae POST /v1/tool/deepsearch start...
2025-08-10 00:45:11.249 INFO log_util.__aenter__ c565b832-52ac-44f2-8410-e7191967452c POST /v1/tool/deepsearch start...
2025-08-10 00:45:11.253 INFO middleware_util.custom_route_handler 6a54b467-7187-4252-99b8-5573f1e44fae POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游住宿推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:c7w6b","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:11.258 INFO middleware_util.custom_route_handler c565b832-52ac-44f2-8410-e7191967452c POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行季节选择","request_id":"geniesession-1754757551055-129:1754757776162-4267:nmr77","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:11.265 INFO log_util.__enter__ 6a54b467-7187-4252-99b8-5573f1e44fae  run start...
2025-08-10 00:45:11.271 INFO log_util.__exit__ 6a54b467-7187-4252-99b8-5573f1e44fae  run cost=[5 ms]
2025-08-10 00:45:11.277 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:c7w6b 第 1 轮深度搜索...
2025-08-10 00:45:11.280 INFO log_util.__aenter__ 6a54b467-7187-4252-99b8-5573f1e44fae  query_decompose start...
2025-08-10 00:45:11.286 ERROR log_util.__aexit__ 6a54b467-7187-4252-99b8-5573f1e44fae  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:11.292 INFO log_util.__enter__ c565b832-52ac-44f2-8410-e7191967452c  run start...
2025-08-10 00:45:11.295 INFO log_util.__exit__ c565b832-52ac-44f2-8410-e7191967452c  run cost=[3 ms]
2025-08-10 00:45:11.296 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:nmr77 第 1 轮深度搜索...
2025-08-10 00:45:11.300 INFO log_util.__aenter__ c565b832-52ac-44f2-8410-e7191967452c  query_decompose start...
2025-08-10 00:45:11.305 ERROR log_util.__aexit__ c565b832-52ac-44f2-8410-e7191967452c  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:11.309 INFO log_util.__aexit__ 6a54b467-7187-4252-99b8-5573f1e44fae POST /v1/tool/deepsearch cost=[62 ms]
2025-08-10 00:45:11.317 INFO log_util.__aexit__ c565b832-52ac-44f2-8410-e7191967452c POST /v1/tool/deepsearch cost=[68 ms]
2025-08-10 00:45:17.091 INFO log_util.__aenter__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f POST /v1/tool/deepsearch start...
2025-08-10 00:45:17.091 INFO log_util.__aenter__ 0a084bde-521f-4d07-be8e-d11977f52e0b POST /v1/tool/deepsearch start...
2025-08-10 00:45:17.093 INFO middleware_util.custom_route_handler a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游交通指南","request_id":"geniesession-1754757551055-129:1754757776162-4267:mk3ek","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:17.095 INFO middleware_util.custom_route_handler 0a084bde-521f-4d07-be8e-d11977f52e0b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行签证办理","request_id":"geniesession-1754757551055-129:1754757776162-4267:6f472","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:17.095 INFO log_util.__enter__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  run start...
2025-08-10 00:45:17.096 INFO log_util.__exit__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  run cost=[0 ms]
2025-08-10 00:45:17.096 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:mk3ek 第 1 轮深度搜索...
2025-08-10 00:45:17.098 INFO log_util.__aenter__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  query_decompose start...
2025-08-10 00:45:17.102 ERROR log_util.__aexit__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:17.106 INFO log_util.__enter__ 0a084bde-521f-4d07-be8e-d11977f52e0b  run start...
2025-08-10 00:45:17.107 INFO log_util.__exit__ 0a084bde-521f-4d07-be8e-d11977f52e0b  run cost=[1 ms]
2025-08-10 00:45:17.107 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:6f472 第 1 轮深度搜索...
2025-08-10 00:45:17.109 INFO log_util.__aenter__ 0a084bde-521f-4d07-be8e-d11977f52e0b  query_decompose start...
2025-08-10 00:45:17.112 ERROR log_util.__aexit__ 0a084bde-521f-4d07-be8e-d11977f52e0b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:17.115 INFO log_util.__aexit__ a0d33cb0-cdb4-4cc6-985a-cb00a2b69c3f POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:45:17.116 INFO log_util.__aexit__ 0a084bde-521f-4d07-be8e-d11977f52e0b POST /v1/tool/deepsearch cost=[24 ms]
2025-08-10 00:45:23.723 INFO log_util.__aenter__ 27e0b650-54cc-4a33-affb-fecc4df56cf1 POST /v1/tool/deepsearch start...
2025-08-10 00:45:23.726 INFO log_util.__aenter__ d560fe3e-2a65-40f8-9fb1-10c987531873 POST /v1/tool/deepsearch start...
2025-08-10 00:45:23.729 INFO middleware_util.custom_route_handler 27e0b650-54cc-4a33-affb-fecc4df56cf1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间","request_id":"geniesession-1754757551055-129:1754757776162-4267:3yz40","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:23.733 INFO middleware_util.custom_route_handler d560fe3e-2a65-40f8-9fb1-10c987531873 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行费用估算","request_id":"geniesession-1754757551055-129:1754757776162-4267:xnify","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:23.741 INFO log_util.__enter__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  run start...
2025-08-10 00:45:23.746 INFO log_util.__exit__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  run cost=[5 ms]
2025-08-10 00:45:23.751 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:3yz40 第 1 轮深度搜索...
2025-08-10 00:45:23.759 INFO log_util.__aenter__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  query_decompose start...
2025-08-10 00:45:23.763 ERROR log_util.__aexit__ 27e0b650-54cc-4a33-affb-fecc4df56cf1  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:23.772 INFO log_util.__enter__ d560fe3e-2a65-40f8-9fb1-10c987531873  run start...
2025-08-10 00:45:23.775 INFO log_util.__exit__ d560fe3e-2a65-40f8-9fb1-10c987531873  run cost=[3 ms]
2025-08-10 00:45:23.778 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:xnify 第 1 轮深度搜索...
2025-08-10 00:45:23.781 INFO log_util.__aenter__ d560fe3e-2a65-40f8-9fb1-10c987531873  query_decompose start...
2025-08-10 00:45:23.784 ERROR log_util.__aexit__ d560fe3e-2a65-40f8-9fb1-10c987531873  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:23.791 INFO log_util.__aexit__ 27e0b650-54cc-4a33-affb-fecc4df56cf1 POST /v1/tool/deepsearch cost=[68 ms]
2025-08-10 00:45:23.797 INFO log_util.__aexit__ d560fe3e-2a65-40f8-9fb1-10c987531873 POST /v1/tool/deepsearch cost=[71 ms]
2025-08-10 00:45:30.589 INFO log_util.__aenter__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7 POST /v1/tool/deepsearch start...
2025-08-10 00:45:30.591 INFO log_util.__aenter__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8 POST /v1/tool/deepsearch start...
2025-08-10 00:45:30.592 INFO middleware_util.custom_route_handler 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李准备","request_id":"geniesession-1754757551055-129:1754757776162-4267:qdadv","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:30.594 INFO middleware_util.custom_route_handler 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游必玩项目","request_id":"geniesession-1754757551055-129:1754757776162-4267:j2ie2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:30.596 INFO log_util.__enter__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  run start...
2025-08-10 00:45:30.597 INFO log_util.__exit__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  run cost=[0 ms]
2025-08-10 00:45:30.598 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:qdadv 第 1 轮深度搜索...
2025-08-10 00:45:30.621 INFO log_util.__aenter__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  query_decompose start...
2025-08-10 00:45:30.647 ERROR log_util.__aexit__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:30.666 INFO log_util.__enter__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  run start...
2025-08-10 00:45:30.685 INFO log_util.__exit__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  run cost=[19 ms]
2025-08-10 00:45:30.703 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:j2ie2 第 1 轮深度搜索...
2025-08-10 00:45:30.722 INFO log_util.__aenter__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  query_decompose start...
2025-08-10 00:45:30.742 ERROR log_util.__aexit__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:30.767 INFO log_util.__aexit__ 589b5ea3-8d86-46c6-8ce9-2c049bd95ba7 POST /v1/tool/deepsearch cost=[178 ms]
2025-08-10 00:45:30.787 INFO log_util.__aexit__ 4dd9b002-d3c0-4b12-b09f-df6405ec5ee8 POST /v1/tool/deepsearch cost=[196 ms]
2025-08-10 00:45:37.232 INFO log_util.__aenter__ ac369ff3-d580-4da4-9b6d-c1bacd14f138 POST /v1/tool/deepsearch start...
2025-08-10 00:45:37.233 INFO log_util.__aenter__ 7c303f9b-6868-40eb-896e-05ee97de10db POST /v1/tool/deepsearch start...
2025-08-10 00:45:37.235 INFO middleware_util.custom_route_handler ac369ff3-d580-4da4-9b6d-c1bacd14f138 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地习俗","request_id":"geniesession-1754757551055-129:1754757776162-4267:4eysp","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:37.237 INFO middleware_util.custom_route_handler 7c303f9b-6868-40eb-896e-05ee97de10db POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行语言沟通","request_id":"geniesession-1754757551055-129:1754757776162-4267:o30gy","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:37.239 INFO log_util.__enter__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  run start...
2025-08-10 00:45:37.244 INFO log_util.__exit__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  run cost=[5 ms]
2025-08-10 00:45:37.248 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:4eysp 第 1 轮深度搜索...
2025-08-10 00:45:37.249 INFO log_util.__aenter__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  query_decompose start...
2025-08-10 00:45:37.256 ERROR log_util.__aexit__ ac369ff3-d580-4da4-9b6d-c1bacd14f138  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:37.258 INFO log_util.__enter__ 7c303f9b-6868-40eb-896e-05ee97de10db  run start...
2025-08-10 00:45:37.259 INFO log_util.__exit__ 7c303f9b-6868-40eb-896e-05ee97de10db  run cost=[0 ms]
2025-08-10 00:45:37.262 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:o30gy 第 1 轮深度搜索...
2025-08-10 00:45:37.266 INFO log_util.__aenter__ 7c303f9b-6868-40eb-896e-05ee97de10db  query_decompose start...
2025-08-10 00:45:37.271 ERROR log_util.__aexit__ 7c303f9b-6868-40eb-896e-05ee97de10db  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:37.273 INFO log_util.__aexit__ ac369ff3-d580-4da4-9b6d-c1bacd14f138 POST /v1/tool/deepsearch cost=[41 ms]
2025-08-10 00:45:37.276 INFO log_util.__aexit__ 7c303f9b-6868-40eb-896e-05ee97de10db POST /v1/tool/deepsearch cost=[43 ms]
2025-08-10 00:45:43.728 INFO log_util.__aenter__ f6c8477c-f759-4da8-a41e-69d67111a229 POST /v1/tool/deepsearch start...
2025-08-10 00:45:43.731 INFO log_util.__aenter__ 57f69525-2f8a-45e7-9a8c-11fccd091d73 POST /v1/tool/deepsearch start...
2025-08-10 00:45:43.737 INFO middleware_util.custom_route_handler f6c8477c-f759-4da8-a41e-69d67111a229 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游货币兑换","request_id":"geniesession-1754757551055-129:1754757776162-4267:fofqy","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:43.739 INFO middleware_util.custom_route_handler 57f69525-2f8a-45e7-9a8c-11fccd091d73 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行网络通讯","request_id":"geniesession-1754757551055-129:1754757776162-4267:mketw","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:43.744 INFO log_util.__enter__ f6c8477c-f759-4da8-a41e-69d67111a229  run start...
2025-08-10 00:45:43.747 INFO log_util.__exit__ f6c8477c-f759-4da8-a41e-69d67111a229  run cost=[2 ms]
2025-08-10 00:45:43.749 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:fofqy 第 1 轮深度搜索...
2025-08-10 00:45:43.752 INFO log_util.__aenter__ f6c8477c-f759-4da8-a41e-69d67111a229  query_decompose start...
2025-08-10 00:45:43.758 ERROR log_util.__aexit__ f6c8477c-f759-4da8-a41e-69d67111a229  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:43.760 INFO log_util.__enter__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  run start...
2025-08-10 00:45:43.764 INFO log_util.__exit__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  run cost=[3 ms]
2025-08-10 00:45:43.766 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:mketw 第 1 轮深度搜索...
2025-08-10 00:45:43.769 INFO log_util.__aenter__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  query_decompose start...
2025-08-10 00:45:43.778 ERROR log_util.__aexit__ 57f69525-2f8a-45e7-9a8c-11fccd091d73  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:43.781 INFO log_util.__aexit__ f6c8477c-f759-4da8-a41e-69d67111a229 POST /v1/tool/deepsearch cost=[52 ms]
2025-08-10 00:45:43.789 INFO log_util.__aexit__ 57f69525-2f8a-45e7-9a8c-11fccd091d73 POST /v1/tool/deepsearch cost=[58 ms]
2025-08-10 00:45:50.172 INFO log_util.__aenter__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f POST /v1/tool/deepsearch start...
2025-08-10 00:45:50.175 INFO log_util.__aenter__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd POST /v1/tool/deepsearch start...
2025-08-10 00:45:50.177 INFO middleware_util.custom_route_handler aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行健康安全","request_id":"geniesession-1754757551055-129:1754757776162-4267:gofe2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:50.179 INFO middleware_util.custom_route_handler 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游摄影地点","request_id":"geniesession-1754757551055-129:1754757776162-4267:ce5c9","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:50.181 INFO log_util.__enter__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  run start...
2025-08-10 00:45:50.184 INFO log_util.__exit__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  run cost=[3 ms]
2025-08-10 00:45:50.195 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:gofe2 第 1 轮深度搜索...
2025-08-10 00:45:50.203 INFO log_util.__aenter__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  query_decompose start...
2025-08-10 00:45:50.214 ERROR log_util.__aexit__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:50.222 INFO log_util.__enter__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  run start...
2025-08-10 00:45:50.229 INFO log_util.__exit__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  run cost=[6 ms]
2025-08-10 00:45:50.236 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:ce5c9 第 1 轮深度搜索...
2025-08-10 00:45:50.244 INFO log_util.__aenter__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  query_decompose start...
2025-08-10 00:45:50.254 ERROR log_util.__aexit__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:50.265 INFO log_util.__aexit__ aad3bbdb-70fa-4c12-8d71-6a4d2ecc975f POST /v1/tool/deepsearch cost=[92 ms]
2025-08-10 00:45:50.287 INFO log_util.__aexit__ 5cb96553-fe5d-458d-ad9c-e6dcb9b45edd POST /v1/tool/deepsearch cost=[111 ms]
2025-08-10 00:45:57.029 INFO log_util.__aenter__ 9d444181-3169-4be7-bcf2-f046ddefd2d6 POST /v1/tool/deepsearch start...
2025-08-10 00:45:57.032 INFO log_util.__aenter__ d73697cb-3ddd-4039-9b58-c4d68699ea59 POST /v1/tool/deepsearch start...
2025-08-10 00:45:57.034 INFO middleware_util.custom_route_handler 9d444181-3169-4be7-bcf2-f046ddefd2d6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行环保建议","request_id":"geniesession-1754757551055-129:1754757776162-4267:81wt1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:57.037 INFO middleware_util.custom_route_handler d73697cb-3ddd-4039-9b58-c4d68699ea59 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游夜生活推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:9mfvg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:45:57.039 INFO log_util.__enter__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  run start...
2025-08-10 00:45:57.041 INFO log_util.__exit__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  run cost=[2 ms]
2025-08-10 00:45:57.042 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:81wt1 第 1 轮深度搜索...
2025-08-10 00:45:57.043 INFO log_util.__aenter__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  query_decompose start...
2025-08-10 00:45:57.049 ERROR log_util.__aexit__ 9d444181-3169-4be7-bcf2-f046ddefd2d6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:57.052 INFO log_util.__enter__ d73697cb-3ddd-4039-9b58-c4d68699ea59  run start...
2025-08-10 00:45:57.054 INFO log_util.__exit__ d73697cb-3ddd-4039-9b58-c4d68699ea59  run cost=[2 ms]
2025-08-10 00:45:57.057 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:9mfvg 第 1 轮深度搜索...
2025-08-10 00:45:57.058 INFO log_util.__aenter__ d73697cb-3ddd-4039-9b58-c4d68699ea59  query_decompose start...
2025-08-10 00:45:57.062 ERROR log_util.__aexit__ d73697cb-3ddd-4039-9b58-c4d68699ea59  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:45:57.066 INFO log_util.__aexit__ 9d444181-3169-4be7-bcf2-f046ddefd2d6 POST /v1/tool/deepsearch cost=[36 ms]
2025-08-10 00:45:57.073 INFO log_util.__aexit__ d73697cb-3ddd-4039-9b58-c4d68699ea59 POST /v1/tool/deepsearch cost=[41 ms]
2025-08-10 00:46:05.778 INFO log_util.__aenter__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861 POST /v1/tool/deepsearch start...
2025-08-10 00:46:05.780 INFO log_util.__aenter__ 4e0bfd72-4d0c-408d-9458-a61c613f9308 POST /v1/tool/deepsearch start...
2025-08-10 00:46:05.782 INFO middleware_util.custom_route_handler 274f707b-4a08-4bfc-b7ce-a9c1ed19f861 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行紧急联系方式","request_id":"geniesession-1754757551055-129:1754757776162-4267:z6clg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:05.783 INFO middleware_util.custom_route_handler 4e0bfd72-4d0c-408d-9458-a61c613f9308 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游亲子活动","request_id":"geniesession-1754757551055-129:1754757776162-4267:3kh5k","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:05.785 INFO log_util.__enter__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  run start...
2025-08-10 00:46:05.789 INFO log_util.__exit__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  run cost=[4 ms]
2025-08-10 00:46:05.790 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:z6clg 第 1 轮深度搜索...
2025-08-10 00:46:05.791 INFO log_util.__aenter__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  query_decompose start...
2025-08-10 00:46:05.793 ERROR log_util.__aexit__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:05.796 INFO log_util.__enter__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  run start...
2025-08-10 00:46:05.802 INFO log_util.__exit__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  run cost=[5 ms]
2025-08-10 00:46:05.806 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:3kh5k 第 1 轮深度搜索...
2025-08-10 00:46:05.810 INFO log_util.__aenter__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  query_decompose start...
2025-08-10 00:46:05.816 ERROR log_util.__aexit__ 4e0bfd72-4d0c-408d-9458-a61c613f9308  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:05.823 INFO log_util.__aexit__ 274f707b-4a08-4bfc-b7ce-a9c1ed19f861 POST /v1/tool/deepsearch cost=[44 ms]
2025-08-10 00:46:05.829 INFO log_util.__aexit__ 4e0bfd72-4d0c-408d-9458-a61c613f9308 POST /v1/tool/deepsearch cost=[48 ms]
2025-08-10 00:46:12.347 INFO log_util.__aenter__ 8e62b739-7d46-414e-adee-13b6fa21dba6 POST /v1/tool/deepsearch start...
2025-08-10 00:46:12.347 INFO log_util.__aenter__ d18e399f-1688-442d-8a35-e6846d33f56f POST /v1/tool/deepsearch start...
2025-08-10 00:46:12.349 INFO middleware_util.custom_route_handler 8e62b739-7d46-414e-adee-13b6fa21dba6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游潜水地点","request_id":"geniesession-1754757551055-129:1754757776162-4267:hrnoa","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:12.350 INFO middleware_util.custom_route_handler d18e399f-1688-442d-8a35-e6846d33f56f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李限制","request_id":"geniesession-1754757551055-129:1754757776162-4267:l2tbc","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:12.351 INFO log_util.__enter__ 8e62b739-7d46-414e-adee-13b6fa21dba6  run start...
2025-08-10 00:46:12.351 INFO log_util.__exit__ 8e62b739-7d46-414e-adee-13b6fa21dba6  run cost=[0 ms]
2025-08-10 00:46:12.352 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:hrnoa 第 1 轮深度搜索...
2025-08-10 00:46:12.352 INFO log_util.__aenter__ 8e62b739-7d46-414e-adee-13b6fa21dba6  query_decompose start...
2025-08-10 00:46:12.356 ERROR log_util.__aexit__ 8e62b739-7d46-414e-adee-13b6fa21dba6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:12.359 INFO log_util.__enter__ d18e399f-1688-442d-8a35-e6846d33f56f  run start...
2025-08-10 00:46:12.363 INFO log_util.__exit__ d18e399f-1688-442d-8a35-e6846d33f56f  run cost=[3 ms]
2025-08-10 00:46:12.364 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:l2tbc 第 1 轮深度搜索...
2025-08-10 00:46:12.364 INFO log_util.__aenter__ d18e399f-1688-442d-8a35-e6846d33f56f  query_decompose start...
2025-08-10 00:46:12.366 ERROR log_util.__aexit__ d18e399f-1688-442d-8a35-e6846d33f56f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:12.367 INFO log_util.__aexit__ 8e62b739-7d46-414e-adee-13b6fa21dba6 POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:46:12.369 INFO log_util.__aexit__ d18e399f-1688-442d-8a35-e6846d33f56f POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:46:18.777 INFO log_util.__aenter__ 69122c3a-9006-4d56-9cd1-96b54245c8af POST /v1/tool/deepsearch start...
2025-08-10 00:46:18.778 INFO log_util.__aenter__ a4fdbd67-6571-413a-a0a4-cbff8cea5787 POST /v1/tool/deepsearch start...
2025-08-10 00:46:18.780 INFO middleware_util.custom_route_handler 69122c3a-9006-4d56-9cd1-96b54245c8af POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行天气情况","request_id":"geniesession-1754757551055-129:1754757776162-4267:9bs5u","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:18.780 INFO middleware_util.custom_route_handler a4fdbd67-6571-413a-a0a4-cbff8cea5787 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游SPA推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:lslk2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:18.782 INFO log_util.__enter__ 69122c3a-9006-4d56-9cd1-96b54245c8af  run start...
2025-08-10 00:46:18.782 INFO log_util.__exit__ 69122c3a-9006-4d56-9cd1-96b54245c8af  run cost=[0 ms]
2025-08-10 00:46:18.783 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:9bs5u 第 1 轮深度搜索...
2025-08-10 00:46:18.783 INFO log_util.__aenter__ 69122c3a-9006-4d56-9cd1-96b54245c8af  query_decompose start...
2025-08-10 00:46:18.787 ERROR log_util.__aexit__ 69122c3a-9006-4d56-9cd1-96b54245c8af  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:18.790 INFO log_util.__enter__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  run start...
2025-08-10 00:46:18.791 INFO log_util.__exit__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  run cost=[0 ms]
2025-08-10 00:46:18.792 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:lslk2 第 1 轮深度搜索...
2025-08-10 00:46:18.792 INFO log_util.__aenter__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  query_decompose start...
2025-08-10 00:46:18.795 ERROR log_util.__aexit__ a4fdbd67-6571-413a-a0a4-cbff8cea5787  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:18.799 INFO log_util.__aexit__ 69122c3a-9006-4d56-9cd1-96b54245c8af POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:46:18.801 INFO log_util.__aexit__ a4fdbd67-6571-413a-a0a4-cbff8cea5787 POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:46:24.806 INFO log_util.__aenter__ a3104a22-7915-4e90-9c45-20c4b955ce7f POST /v1/tool/deepsearch start...
2025-08-10 00:46:24.807 INFO log_util.__aenter__ 0ea06094-a830-4a40-93d9-a79dceb05589 POST /v1/tool/deepsearch start...
2025-08-10 00:46:24.809 INFO middleware_util.custom_route_handler a3104a22-7915-4e90-9c45-20c4b955ce7f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游婚礼场地","request_id":"geniesession-1754757551055-129:1754757776162-4267:rlvty","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:24.810 INFO middleware_util.custom_route_handler 0ea06094-a830-4a40-93d9-a79dceb05589 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行交通卡","request_id":"geniesession-1754757551055-129:1754757776162-4267:ipx8t","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:24.811 INFO log_util.__enter__ a3104a22-7915-4e90-9c45-20c4b955ce7f  run start...
2025-08-10 00:46:24.812 INFO log_util.__exit__ a3104a22-7915-4e90-9c45-20c4b955ce7f  run cost=[0 ms]
2025-08-10 00:46:24.812 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:rlvty 第 1 轮深度搜索...
2025-08-10 00:46:24.814 INFO log_util.__aenter__ a3104a22-7915-4e90-9c45-20c4b955ce7f  query_decompose start...
2025-08-10 00:46:24.819 ERROR log_util.__aexit__ a3104a22-7915-4e90-9c45-20c4b955ce7f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:24.819 INFO log_util.__enter__ 0ea06094-a830-4a40-93d9-a79dceb05589  run start...
2025-08-10 00:46:24.824 INFO log_util.__exit__ 0ea06094-a830-4a40-93d9-a79dceb05589  run cost=[4 ms]
2025-08-10 00:46:24.826 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:ipx8t 第 1 轮深度搜索...
2025-08-10 00:46:24.828 INFO log_util.__aenter__ 0ea06094-a830-4a40-93d9-a79dceb05589  query_decompose start...
2025-08-10 00:46:24.831 ERROR log_util.__aexit__ 0ea06094-a830-4a40-93d9-a79dceb05589  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:24.838 INFO log_util.__aexit__ a3104a22-7915-4e90-9c45-20c4b955ce7f POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:46:24.843 INFO log_util.__aexit__ 0ea06094-a830-4a40-93d9-a79dceb05589 POST /v1/tool/deepsearch cost=[36 ms]
2025-08-10 00:46:31.099 INFO log_util.__aenter__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6 POST /v1/tool/deepsearch start...
2025-08-10 00:46:31.101 INFO log_util.__aenter__ 54aeee17-6562-44d7-b08d-ff440413aae7 POST /v1/tool/deepsearch start...
2025-08-10 00:46:31.103 INFO middleware_util.custom_route_handler 707d004f-c54d-4254-9e6e-6e6fb035f4e6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游冲浪地点","request_id":"geniesession-1754757551055-129:1754757776162-4267:396nt","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:31.105 INFO middleware_util.custom_route_handler 54aeee17-6562-44d7-b08d-ff440413aae7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李打包技巧","request_id":"geniesession-1754757551055-129:1754757776162-4267:vetgk","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:31.106 INFO log_util.__enter__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  run start...
2025-08-10 00:46:31.107 INFO log_util.__exit__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  run cost=[1 ms]
2025-08-10 00:46:31.109 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:396nt 第 1 轮深度搜索...
2025-08-10 00:46:31.113 INFO log_util.__aenter__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  query_decompose start...
2025-08-10 00:46:31.116 ERROR log_util.__aexit__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:31.118 INFO log_util.__enter__ 54aeee17-6562-44d7-b08d-ff440413aae7  run start...
2025-08-10 00:46:31.121 INFO log_util.__exit__ 54aeee17-6562-44d7-b08d-ff440413aae7  run cost=[2 ms]
2025-08-10 00:46:31.123 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vetgk 第 1 轮深度搜索...
2025-08-10 00:46:31.125 INFO log_util.__aenter__ 54aeee17-6562-44d7-b08d-ff440413aae7  query_decompose start...
2025-08-10 00:46:31.129 ERROR log_util.__aexit__ 54aeee17-6562-44d7-b08d-ff440413aae7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:31.132 INFO log_util.__aexit__ 707d004f-c54d-4254-9e6e-6e6fb035f4e6 POST /v1/tool/deepsearch cost=[33 ms]
2025-08-10 00:46:31.137 INFO log_util.__aexit__ 54aeee17-6562-44d7-b08d-ff440413aae7 POST /v1/tool/deepsearch cost=[36 ms]
2025-08-10 00:46:37.355 INFO log_util.__aenter__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6 POST /v1/tool/deepsearch start...
2025-08-10 00:46:37.356 INFO log_util.__aenter__ 48193c12-0aab-4bf2-9981-0a6f710604ca POST /v1/tool/deepsearch start...
2025-08-10 00:46:37.359 INFO middleware_util.custom_route_handler 09eab9c4-a584-47bd-b8ed-a16f7089a3c6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行机场接送","request_id":"geniesession-1754757551055-129:1754757776162-4267:jiyc1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:37.360 INFO middleware_util.custom_route_handler 48193c12-0aab-4bf2-9981-0a6f710604ca POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游瑜伽体验","request_id":"geniesession-1754757551055-129:1754757776162-4267:u6n1g","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:37.361 INFO log_util.__enter__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  run start...
2025-08-10 00:46:37.363 INFO log_util.__exit__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  run cost=[2 ms]
2025-08-10 00:46:37.366 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:jiyc1 第 1 轮深度搜索...
2025-08-10 00:46:37.367 INFO log_util.__aenter__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  query_decompose start...
2025-08-10 00:46:37.373 ERROR log_util.__aexit__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:37.374 INFO log_util.__enter__ 48193c12-0aab-4bf2-9981-0a6f710604ca  run start...
2025-08-10 00:46:37.377 INFO log_util.__exit__ 48193c12-0aab-4bf2-9981-0a6f710604ca  run cost=[3 ms]
2025-08-10 00:46:37.378 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:u6n1g 第 1 轮深度搜索...
2025-08-10 00:46:37.380 INFO log_util.__aenter__ 48193c12-0aab-4bf2-9981-0a6f710604ca  query_decompose start...
2025-08-10 00:46:37.385 ERROR log_util.__aexit__ 48193c12-0aab-4bf2-9981-0a6f710604ca  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:37.386 INFO log_util.__aexit__ 09eab9c4-a584-47bd-b8ed-a16f7089a3c6 POST /v1/tool/deepsearch cost=[31 ms]
2025-08-10 00:46:37.388 INFO log_util.__aexit__ 48193c12-0aab-4bf2-9981-0a6f710604ca POST /v1/tool/deepsearch cost=[32 ms]
2025-08-10 00:46:45.786 INFO log_util.__aenter__ eca75d18-5941-4b08-b51c-2bfc23b3acf2 POST /v1/tool/deepsearch start...
2025-08-10 00:46:45.788 INFO log_util.__aenter__ 20d85433-f78b-4881-9cbe-87327045435d POST /v1/tool/deepsearch start...
2025-08-10 00:46:45.791 INFO middleware_util.custom_route_handler eca75d18-5941-4b08-b51c-2bfc23b3acf2 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游徒步路线","request_id":"geniesession-1754757551055-129:1754757776162-4267:kkfo4","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:45.792 INFO middleware_util.custom_route_handler 20d85433-f78b-4881-9cbe-87327045435d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行货币兑换建议","request_id":"geniesession-1754757551055-129:1754757776162-4267:p59z6","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:45.795 INFO log_util.__enter__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  run start...
2025-08-10 00:46:45.797 INFO log_util.__exit__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  run cost=[1 ms]
2025-08-10 00:46:45.797 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:kkfo4 第 1 轮深度搜索...
2025-08-10 00:46:45.798 INFO log_util.__aenter__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  query_decompose start...
2025-08-10 00:46:45.800 ERROR log_util.__aexit__ eca75d18-5941-4b08-b51c-2bfc23b3acf2  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:45.801 INFO log_util.__enter__ 20d85433-f78b-4881-9cbe-87327045435d  run start...
2025-08-10 00:46:45.803 INFO log_util.__exit__ 20d85433-f78b-4881-9cbe-87327045435d  run cost=[3 ms]
2025-08-10 00:46:45.805 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:p59z6 第 1 轮深度搜索...
2025-08-10 00:46:45.806 INFO log_util.__aenter__ 20d85433-f78b-4881-9cbe-87327045435d  query_decompose start...
2025-08-10 00:46:45.808 ERROR log_util.__aexit__ 20d85433-f78b-4881-9cbe-87327045435d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:45.809 INFO log_util.__aexit__ eca75d18-5941-4b08-b51c-2bfc23b3acf2 POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:46:45.811 INFO log_util.__aexit__ 20d85433-f78b-4881-9cbe-87327045435d POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:46:52.005 INFO log_util.__aenter__ f002619b-f64d-4064-8349-736cef248950 POST /v1/tool/deepsearch start...
2025-08-10 00:46:52.007 INFO log_util.__aenter__ e7b88e70-d6cf-42e3-a768-201754b841c4 POST /v1/tool/deepsearch start...
2025-08-10 00:46:52.008 INFO middleware_util.custom_route_handler f002619b-f64d-4064-8349-736cef248950 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行语言翻译工具","request_id":"geniesession-1754757551055-129:1754757776162-4267:konc4","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:52.009 INFO middleware_util.custom_route_handler e7b88e70-d6cf-42e3-a768-201754b841c4 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游水上活动","request_id":"geniesession-1754757551055-129:1754757776162-4267:bes5r","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:46:52.010 INFO log_util.__enter__ f002619b-f64d-4064-8349-736cef248950  run start...
2025-08-10 00:46:52.010 INFO log_util.__exit__ f002619b-f64d-4064-8349-736cef248950  run cost=[0 ms]
2025-08-10 00:46:52.011 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:konc4 第 1 轮深度搜索...
2025-08-10 00:46:52.011 INFO log_util.__aenter__ f002619b-f64d-4064-8349-736cef248950  query_decompose start...
2025-08-10 00:46:52.016 ERROR log_util.__aexit__ f002619b-f64d-4064-8349-736cef248950  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:52.017 INFO log_util.__enter__ e7b88e70-d6cf-42e3-a768-201754b841c4  run start...
2025-08-10 00:46:52.019 INFO log_util.__exit__ e7b88e70-d6cf-42e3-a768-201754b841c4  run cost=[1 ms]
2025-08-10 00:46:52.019 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:bes5r 第 1 轮深度搜索...
2025-08-10 00:46:52.019 INFO log_util.__aenter__ e7b88e70-d6cf-42e3-a768-201754b841c4  query_decompose start...
2025-08-10 00:46:52.021 ERROR log_util.__aexit__ e7b88e70-d6cf-42e3-a768-201754b841c4  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:46:52.022 INFO log_util.__aexit__ f002619b-f64d-4064-8349-736cef248950 POST /v1/tool/deepsearch cost=[17 ms]
2025-08-10 00:46:52.027 INFO log_util.__aexit__ e7b88e70-d6cf-42e3-a768-201754b841c4 POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:47:00.858 INFO log_util.__aenter__ 371b47c1-473a-4aa3-8174-906c6d729ed7 POST /v1/tool/deepsearch start...
2025-08-10 00:47:00.859 INFO log_util.__aenter__ b0f4bbac-3aea-47ad-b577-37ded47536ed POST /v1/tool/deepsearch start...
2025-08-10 00:47:00.862 INFO middleware_util.custom_route_handler 371b47c1-473a-4aa3-8174-906c6d729ed7 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行SIM卡购买","request_id":"geniesession-1754757551055-129:1754757776162-4267:atp72","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:00.863 INFO middleware_util.custom_route_handler b0f4bbac-3aea-47ad-b577-37ded47536ed POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游日落观赏点","request_id":"geniesession-1754757551055-129:1754757776162-4267:x401s","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:00.864 INFO log_util.__enter__ 371b47c1-473a-4aa3-8174-906c6d729ed7  run start...
2025-08-10 00:47:00.864 INFO log_util.__exit__ 371b47c1-473a-4aa3-8174-906c6d729ed7  run cost=[0 ms]
2025-08-10 00:47:00.865 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:atp72 第 1 轮深度搜索...
2025-08-10 00:47:00.868 INFO log_util.__aenter__ 371b47c1-473a-4aa3-8174-906c6d729ed7  query_decompose start...
2025-08-10 00:47:00.871 ERROR log_util.__aexit__ 371b47c1-473a-4aa3-8174-906c6d729ed7  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:00.871 INFO log_util.__enter__ b0f4bbac-3aea-47ad-b577-37ded47536ed  run start...
2025-08-10 00:47:00.874 INFO log_util.__exit__ b0f4bbac-3aea-47ad-b577-37ded47536ed  run cost=[3 ms]
2025-08-10 00:47:00.874 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:x401s 第 1 轮深度搜索...
2025-08-10 00:47:00.874 INFO log_util.__aenter__ b0f4bbac-3aea-47ad-b577-37ded47536ed  query_decompose start...
2025-08-10 00:47:00.877 ERROR log_util.__aexit__ b0f4bbac-3aea-47ad-b577-37ded47536ed  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:00.878 INFO log_util.__aexit__ 371b47c1-473a-4aa3-8174-906c6d729ed7 POST /v1/tool/deepsearch cost=[19 ms]
2025-08-10 00:47:00.882 INFO log_util.__aexit__ b0f4bbac-3aea-47ad-b577-37ded47536ed POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:47:06.888 INFO log_util.__aenter__ 2897f422-472e-4292-bbf1-4026b953ae8d POST /v1/tool/deepsearch start...
2025-08-10 00:47:06.891 INFO log_util.__aenter__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12 POST /v1/tool/deepsearch start...
2025-08-10 00:47:06.896 INFO middleware_util.custom_route_handler 2897f422-472e-4292-bbf1-4026b953ae8d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游购物市场","request_id":"geniesession-1754757551055-129:1754757776162-4267:dtt10","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:06.899 INFO middleware_util.custom_route_handler f7f38636-3ad7-468d-8f3d-6b4c1300ee12 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李托运","request_id":"geniesession-1754757551055-129:1754757776162-4267:gx84a","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:06.902 INFO log_util.__enter__ 2897f422-472e-4292-bbf1-4026b953ae8d  run start...
2025-08-10 00:47:06.912 INFO log_util.__exit__ 2897f422-472e-4292-bbf1-4026b953ae8d  run cost=[9 ms]
2025-08-10 00:47:06.916 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:dtt10 第 1 轮深度搜索...
2025-08-10 00:47:06.921 INFO log_util.__aenter__ 2897f422-472e-4292-bbf1-4026b953ae8d  query_decompose start...
2025-08-10 00:47:06.928 ERROR log_util.__aexit__ 2897f422-472e-4292-bbf1-4026b953ae8d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:06.935 INFO log_util.__enter__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  run start...
2025-08-10 00:47:06.939 INFO log_util.__exit__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  run cost=[4 ms]
2025-08-10 00:47:06.946 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:gx84a 第 1 轮深度搜索...
2025-08-10 00:47:06.951 INFO log_util.__aenter__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  query_decompose start...
2025-08-10 00:47:06.962 ERROR log_util.__aexit__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:06.972 INFO log_util.__aexit__ 2897f422-472e-4292-bbf1-4026b953ae8d POST /v1/tool/deepsearch cost=[83 ms]
2025-08-10 00:47:06.984 INFO log_util.__aexit__ f7f38636-3ad7-468d-8f3d-6b4c1300ee12 POST /v1/tool/deepsearch cost=[93 ms]
2025-08-10 00:47:14.172 INFO log_util.__aenter__ f41e659a-0abd-460b-9c58-f7151ade47af POST /v1/tool/deepsearch start...
2025-08-10 00:47:14.172 INFO log_util.__aenter__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8 POST /v1/tool/deepsearch start...
2025-08-10 00:47:14.174 INFO middleware_util.custom_route_handler f41e659a-0abd-460b-9c58-f7151ade47af POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地美食","request_id":"geniesession-1754757551055-129:1754757776162-4267:cyybh","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:14.175 INFO middleware_util.custom_route_handler cc420227-afdb-4b18-bd09-6ccfca2ed7c8 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行机场退税","request_id":"geniesession-1754757551055-129:1754757776162-4267:altk3","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:14.176 INFO log_util.__enter__ f41e659a-0abd-460b-9c58-f7151ade47af  run start...
2025-08-10 00:47:14.176 INFO log_util.__exit__ f41e659a-0abd-460b-9c58-f7151ade47af  run cost=[0 ms]
2025-08-10 00:47:14.177 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:cyybh 第 1 轮深度搜索...
2025-08-10 00:47:14.177 INFO log_util.__aenter__ f41e659a-0abd-460b-9c58-f7151ade47af  query_decompose start...
2025-08-10 00:47:14.185 ERROR log_util.__aexit__ f41e659a-0abd-460b-9c58-f7151ade47af  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:14.186 INFO log_util.__enter__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  run start...
2025-08-10 00:47:14.187 INFO log_util.__exit__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  run cost=[1 ms]
2025-08-10 00:47:14.187 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:altk3 第 1 轮深度搜索...
2025-08-10 00:47:14.188 INFO log_util.__aenter__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  query_decompose start...
2025-08-10 00:47:14.190 ERROR log_util.__aexit__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:14.191 INFO log_util.__aexit__ f41e659a-0abd-460b-9c58-f7151ade47af POST /v1/tool/deepsearch cost=[18 ms]
2025-08-10 00:47:14.194 INFO log_util.__aexit__ cc420227-afdb-4b18-bd09-6ccfca2ed7c8 POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 00:47:20.684 INFO log_util.__aenter__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5 POST /v1/tool/deepsearch start...
2025-08-10 00:47:20.685 INFO log_util.__aenter__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09 POST /v1/tool/deepsearch start...
2025-08-10 00:47:20.687 INFO middleware_util.custom_route_handler 08cf742a-2aa1-4f01-b0ad-4c2c118febf5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李安检","request_id":"geniesession-1754757551055-129:1754757776162-4267:mdvne","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:20.688 INFO middleware_util.custom_route_handler a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游海滩推荐","request_id":"geniesession-1754757551055-129:1754757776162-4267:1ugp8","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:20.689 INFO log_util.__enter__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  run start...
2025-08-10 00:47:20.691 INFO log_util.__exit__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  run cost=[2 ms]
2025-08-10 00:47:20.692 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:mdvne 第 1 轮深度搜索...
2025-08-10 00:47:20.693 INFO log_util.__aenter__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  query_decompose start...
2025-08-10 00:47:20.695 ERROR log_util.__aexit__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:20.696 INFO log_util.__enter__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  run start...
2025-08-10 00:47:20.698 INFO log_util.__exit__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  run cost=[2 ms]
2025-08-10 00:47:20.699 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:1ugp8 第 1 轮深度搜索...
2025-08-10 00:47:20.699 INFO log_util.__aenter__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  query_decompose start...
2025-08-10 00:47:20.704 ERROR log_util.__aexit__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:20.705 INFO log_util.__aexit__ 08cf742a-2aa1-4f01-b0ad-4c2c118febf5 POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:47:20.708 INFO log_util.__aexit__ a1bcef0f-4b87-43c7-90c9-8fbeb53b3d09 POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:47:26.890 INFO log_util.__aenter__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a POST /v1/tool/deepsearch start...
2025-08-10 00:47:26.891 INFO log_util.__aenter__ 28b46fdd-cca8-4de0-995f-8db1a3abd877 POST /v1/tool/deepsearch start...
2025-08-10 00:47:26.894 INFO middleware_util.custom_route_handler 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游文化节庆","request_id":"geniesession-1754757551055-129:1754757776162-4267:v4tcc","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:26.896 INFO middleware_util.custom_route_handler 28b46fdd-cca8-4de0-995f-8db1a3abd877 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李寄存","request_id":"geniesession-1754757551055-129:1754757776162-4267:n7rm2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:26.897 INFO log_util.__enter__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  run start...
2025-08-10 00:47:26.901 INFO log_util.__exit__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  run cost=[3 ms]
2025-08-10 00:47:26.907 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:v4tcc 第 1 轮深度搜索...
2025-08-10 00:47:26.909 INFO log_util.__aenter__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  query_decompose start...
2025-08-10 00:47:26.912 ERROR log_util.__aexit__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:26.914 INFO log_util.__enter__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  run start...
2025-08-10 00:47:26.915 INFO log_util.__exit__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  run cost=[2 ms]
2025-08-10 00:47:26.918 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:n7rm2 第 1 轮深度搜索...
2025-08-10 00:47:26.923 INFO log_util.__aenter__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  query_decompose start...
2025-08-10 00:47:26.926 ERROR log_util.__aexit__ 28b46fdd-cca8-4de0-995f-8db1a3abd877  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:26.928 INFO log_util.__aexit__ 8b3d8c2b-3e5b-46cd-b773-b52b8f8ff96a POST /v1/tool/deepsearch cost=[38 ms]
2025-08-10 00:47:26.935 INFO log_util.__aexit__ 28b46fdd-cca8-4de0-995f-8db1a3abd877 POST /v1/tool/deepsearch cost=[43 ms]
2025-08-10 00:47:34.941 INFO log_util.__aenter__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d POST /v1/tool/deepsearch start...
2025-08-10 00:47:34.942 INFO log_util.__aenter__ 391fdf9b-fd8d-474c-b187-ef9384fe51db POST /v1/tool/deepsearch start...
2025-08-10 00:47:34.945 INFO middleware_util.custom_route_handler 448652fb-cb18-4a1c-9fe0-cd3b91af567d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游家庭住宿","request_id":"geniesession-1754757551055-129:1754757776162-4267:zllfq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:34.946 INFO middleware_util.custom_route_handler 391fdf9b-fd8d-474c-b187-ef9384fe51db POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李打包清单","request_id":"geniesession-1754757551055-129:1754757776162-4267:vrz6o","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:34.947 INFO log_util.__enter__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  run start...
2025-08-10 00:47:34.947 INFO log_util.__exit__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  run cost=[0 ms]
2025-08-10 00:47:34.949 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:zllfq 第 1 轮深度搜索...
2025-08-10 00:47:34.951 INFO log_util.__aenter__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  query_decompose start...
2025-08-10 00:47:34.954 ERROR log_util.__aexit__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:34.955 INFO log_util.__enter__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  run start...
2025-08-10 00:47:34.955 INFO log_util.__exit__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  run cost=[0 ms]
2025-08-10 00:47:34.956 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vrz6o 第 1 轮深度搜索...
2025-08-10 00:47:34.956 INFO log_util.__aenter__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  query_decompose start...
2025-08-10 00:47:34.959 ERROR log_util.__aexit__ 391fdf9b-fd8d-474c-b187-ef9384fe51db  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:34.962 INFO log_util.__aexit__ 448652fb-cb18-4a1c-9fe0-cd3b91af567d POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:47:34.963 INFO log_util.__aexit__ 391fdf9b-fd8d-474c-b187-ef9384fe51db POST /v1/tool/deepsearch cost=[21 ms]
2025-08-10 00:47:41.311 INFO log_util.__aenter__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f POST /v1/tool/deepsearch start...
2025-08-10 00:47:41.312 INFO log_util.__aenter__ 17299e10-6530-443f-9dd4-9cc868b6c9ab POST /v1/tool/deepsearch start...
2025-08-10 00:47:41.314 INFO middleware_util.custom_route_handler 105a909e-6bdb-4b86-b1e8-c168a8ffd66f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李重量限制","request_id":"geniesession-1754757551055-129:1754757776162-4267:ct2r1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:41.314 INFO middleware_util.custom_route_handler 17299e10-6530-443f-9dd4-9cc868b6c9ab POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地交通","request_id":"geniesession-1754757551055-129:1754757776162-4267:smumv","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:41.315 INFO log_util.__enter__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  run start...
2025-08-10 00:47:41.316 INFO log_util.__exit__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  run cost=[0 ms]
2025-08-10 00:47:41.317 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:ct2r1 第 1 轮深度搜索...
2025-08-10 00:47:41.321 INFO log_util.__aenter__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  query_decompose start...
2025-08-10 00:47:41.325 ERROR log_util.__aexit__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:41.326 INFO log_util.__enter__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  run start...
2025-08-10 00:47:41.327 INFO log_util.__exit__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  run cost=[1 ms]
2025-08-10 00:47:41.328 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:smumv 第 1 轮深度搜索...
2025-08-10 00:47:41.328 INFO log_util.__aenter__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  query_decompose start...
2025-08-10 00:47:41.331 ERROR log_util.__aexit__ 17299e10-6530-443f-9dd4-9cc868b6c9ab  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:41.335 INFO log_util.__aexit__ 105a909e-6bdb-4b86-b1e8-c168a8ffd66f POST /v1/tool/deepsearch cost=[23 ms]
2025-08-10 00:47:41.338 INFO log_util.__aexit__ 17299e10-6530-443f-9dd4-9cc868b6c9ab POST /v1/tool/deepsearch cost=[25 ms]
2025-08-10 00:47:47.766 INFO log_util.__aenter__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d POST /v1/tool/deepsearch start...
2025-08-10 00:47:47.768 INFO log_util.__aenter__ 805ae152-2f0a-422c-aa8f-21790bd16024 POST /v1/tool/deepsearch start...
2025-08-10 00:47:47.773 INFO middleware_util.custom_route_handler 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地导游服务","request_id":"geniesession-1754757551055-129:1754757776162-4267:eytjl","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:47.775 INFO middleware_util.custom_route_handler 805ae152-2f0a-422c-aa8f-21790bd16024 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李托运费用","request_id":"geniesession-1754757551055-129:1754757776162-4267:zx0fh","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:47.777 INFO log_util.__enter__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  run start...
2025-08-10 00:47:47.779 INFO log_util.__exit__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  run cost=[1 ms]
2025-08-10 00:47:47.781 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:eytjl 第 1 轮深度搜索...
2025-08-10 00:47:47.783 INFO log_util.__aenter__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  query_decompose start...
2025-08-10 00:47:47.786 ERROR log_util.__aexit__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:47.789 INFO log_util.__enter__ 805ae152-2f0a-422c-aa8f-21790bd16024  run start...
2025-08-10 00:47:47.792 INFO log_util.__exit__ 805ae152-2f0a-422c-aa8f-21790bd16024  run cost=[2 ms]
2025-08-10 00:47:47.793 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:zx0fh 第 1 轮深度搜索...
2025-08-10 00:47:47.795 INFO log_util.__aenter__ 805ae152-2f0a-422c-aa8f-21790bd16024  query_decompose start...
2025-08-10 00:47:47.801 ERROR log_util.__aexit__ 805ae152-2f0a-422c-aa8f-21790bd16024  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:47.806 INFO log_util.__aexit__ 18d12a5c-7ab3-4c93-ab2c-d2ad15d53b7d POST /v1/tool/deepsearch cost=[39 ms]
2025-08-10 00:47:47.809 INFO log_util.__aexit__ 805ae152-2f0a-422c-aa8f-21790bd16024 POST /v1/tool/deepsearch cost=[40 ms]
2025-08-10 00:47:54.263 INFO log_util.__aenter__ 2302b52d-0a3a-408d-bf8b-2044785f807a POST /v1/tool/deepsearch start...
2025-08-10 00:47:54.263 INFO log_util.__aenter__ 62283372-0980-4911-93e4-8999b7b22038 POST /v1/tool/deepsearch start...
2025-08-10 00:47:54.265 INFO middleware_util.custom_route_handler 2302b52d-0a3a-408d-bf8b-2044785f807a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李安检注意事项","request_id":"geniesession-1754757551055-129:1754757776162-4267:tdsl1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:54.266 INFO middleware_util.custom_route_handler 62283372-0980-4911-93e4-8999b7b22038 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地特色活动","request_id":"geniesession-1754757551055-129:1754757776162-4267:b7cez","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:47:54.267 INFO log_util.__enter__ 2302b52d-0a3a-408d-bf8b-2044785f807a  run start...
2025-08-10 00:47:54.267 INFO log_util.__exit__ 2302b52d-0a3a-408d-bf8b-2044785f807a  run cost=[0 ms]
2025-08-10 00:47:54.267 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:tdsl1 第 1 轮深度搜索...
2025-08-10 00:47:54.267 INFO log_util.__aenter__ 2302b52d-0a3a-408d-bf8b-2044785f807a  query_decompose start...
2025-08-10 00:47:54.269 ERROR log_util.__aexit__ 2302b52d-0a3a-408d-bf8b-2044785f807a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:54.272 INFO log_util.__enter__ 62283372-0980-4911-93e4-8999b7b22038  run start...
2025-08-10 00:47:54.272 INFO log_util.__exit__ 62283372-0980-4911-93e4-8999b7b22038  run cost=[0 ms]
2025-08-10 00:47:54.273 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:b7cez 第 1 轮深度搜索...
2025-08-10 00:47:54.274 INFO log_util.__aenter__ 62283372-0980-4911-93e4-8999b7b22038  query_decompose start...
2025-08-10 00:47:54.276 ERROR log_util.__aexit__ 62283372-0980-4911-93e4-8999b7b22038  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:47:54.277 INFO log_util.__aexit__ 2302b52d-0a3a-408d-bf8b-2044785f807a POST /v1/tool/deepsearch cost=[13 ms]
2025-08-10 00:47:54.281 INFO log_util.__aexit__ 62283372-0980-4911-93e4-8999b7b22038 POST /v1/tool/deepsearch cost=[17 ms]
2025-08-10 00:48:00.338 INFO log_util.__aenter__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f POST /v1/tool/deepsearch start...
2025-08-10 00:48:00.339 INFO log_util.__aenter__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d POST /v1/tool/deepsearch start...
2025-08-10 00:48:00.341 INFO middleware_util.custom_route_handler c60e07c3-855a-44f9-bb04-fd7d493a0f1f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游当地节日庆典","request_id":"geniesession-1754757551055-129:1754757776162-4267:89n2l","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:48:00.342 INFO middleware_util.custom_route_handler 3d76031f-8908-4c7b-97b4-0d1dc388e00d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"上海到巴厘岛旅行行李打包建议","request_id":"geniesession-1754757551055-129:1754757776162-4267:vtrym","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 00:48:00.343 INFO log_util.__enter__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  run start...
2025-08-10 00:48:00.344 INFO log_util.__exit__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  run cost=[0 ms]
2025-08-10 00:48:00.346 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:89n2l 第 1 轮深度搜索...
2025-08-10 00:48:00.347 INFO log_util.__aenter__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  query_decompose start...
2025-08-10 00:48:00.349 ERROR log_util.__aexit__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:48:00.350 INFO log_util.__enter__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  run start...
2025-08-10 00:48:00.354 INFO log_util.__exit__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  run cost=[3 ms]
2025-08-10 00:48:00.355 INFO deepsearch.run geniesession-1754757551055-129:1754757776162-4267:vtrym 第 1 轮深度搜索...
2025-08-10 00:48:00.356 INFO log_util.__aenter__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  query_decompose start...
2025-08-10 00:48:00.358 ERROR log_util.__aexit__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\environment\python3.11\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-10 00:48:00.359 INFO log_util.__aexit__ c60e07c3-855a-44f9-bb04-fd7d493a0f1f POST /v1/tool/deepsearch cost=[20 ms]
2025-08-10 00:48:00.362 INFO log_util.__aexit__ 3d76031f-8908-4c7b-97b4-0d1dc388e00d POST /v1/tool/deepsearch cost=[22 ms]
2025-08-10 01:34:22.500 INFO log_util.__aenter__ adacbf79-27d1-40f5-a059-5bc5391f0e34 POST /v1/tool/deepsearch start...
2025-08-10 01:34:22.503 INFO middleware_util.custom_route_handler adacbf79-27d1-40f5-a059-5bc5391f0e34 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛最适合去的天气 2025年8月","request_id":"geniesession-1754760846817-8847:1754760846837-5145:aigzq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:22.507 INFO log_util.__enter__ adacbf79-27d1-40f5-a059-5bc5391f0e34  run start...
2025-08-10 01:34:22.507 INFO log_util.__exit__ adacbf79-27d1-40f5-a059-5bc5391f0e34  run cost=[0 ms]
2025-08-10 01:34:22.508 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:aigzq 第 1 轮深度搜索...
2025-08-10 01:34:22.509 INFO log_util.__aenter__ adacbf79-27d1-40f5-a059-5bc5391f0e34  query_decompose start...
2025-08-10 01:34:22.518 INFO log_util.__enter__ adacbf79-27d1-40f5-a059-5bc5391f0e34 enter ask_llm start...
2025-08-10 01:34:22.520 INFO log_util.__exit__ adacbf79-27d1-40f5-a059-5bc5391f0e34 enter ask_llm cost=[2 ms]
2025-08-10 01:34:22.537 ERROR log_util.__aexit__ adacbf79-27d1-40f5-a059-5bc5391f0e34  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:22.541 INFO log_util.__aexit__ adacbf79-27d1-40f5-a059-5bc5391f0e34 POST /v1/tool/deepsearch cost=[40 ms]
2025-08-10 01:34:30.934 INFO log_util.__aenter__ 94a849d7-7db5-494a-a21a-502af663be79 POST /v1/tool/deepsearch start...
2025-08-10 01:34:30.936 INFO middleware_util.custom_route_handler 94a849d7-7db5-494a-a21a-502af663be79 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛2025年8月天气预报","request_id":"geniesession-1754760846817-8847:1754760846837-5145:rpoho","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:30.938 INFO log_util.__enter__ 94a849d7-7db5-494a-a21a-502af663be79  run start...
2025-08-10 01:34:30.938 INFO log_util.__exit__ 94a849d7-7db5-494a-a21a-502af663be79  run cost=[0 ms]
2025-08-10 01:34:30.938 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:rpoho 第 1 轮深度搜索...
2025-08-10 01:34:30.939 INFO log_util.__aenter__ 94a849d7-7db5-494a-a21a-502af663be79  query_decompose start...
2025-08-10 01:34:30.949 INFO log_util.__enter__ 94a849d7-7db5-494a-a21a-502af663be79 enter ask_llm start...
2025-08-10 01:34:30.952 INFO log_util.__exit__ 94a849d7-7db5-494a-a21a-502af663be79 enter ask_llm cost=[3 ms]
2025-08-10 01:34:30.970 ERROR log_util.__aexit__ 94a849d7-7db5-494a-a21a-502af663be79  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:30.972 INFO log_util.__aexit__ 94a849d7-7db5-494a-a21a-502af663be79 POST /v1/tool/deepsearch cost=[37 ms]
2025-08-10 01:34:39.211 INFO log_util.__aenter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a POST /v1/tool/deepsearch start...
2025-08-10 01:34:39.212 INFO middleware_util.custom_route_handler 14ae021f-ed11-435b-bfe0-4bfb88f0778a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛最佳旅游季节和天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:nw24e","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:39.213 INFO log_util.__enter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  run start...
2025-08-10 01:34:39.213 INFO log_util.__exit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  run cost=[0 ms]
2025-08-10 01:34:39.213 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:nw24e 第 1 轮深度搜索...
2025-08-10 01:34:39.213 INFO log_util.__aenter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  query_decompose start...
2025-08-10 01:34:39.218 INFO log_util.__enter__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a enter ask_llm start...
2025-08-10 01:34:39.218 INFO log_util.__exit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a enter ask_llm cost=[0 ms]
2025-08-10 01:34:39.224 ERROR log_util.__aexit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:39.224 INFO log_util.__aexit__ 14ae021f-ed11-435b-bfe0-4bfb88f0778a POST /v1/tool/deepsearch cost=[12 ms]
2025-08-10 01:34:45.929 INFO log_util.__aenter__ 8f85da85-6461-4702-96a7-86847418e3ac POST /v1/tool/deepsearch start...
2025-08-10 01:34:45.930 INFO middleware_util.custom_route_handler 8f85da85-6461-4702-96a7-86847418e3ac POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳季节和天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:r90g2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:45.930 INFO log_util.__enter__ 8f85da85-6461-4702-96a7-86847418e3ac  run start...
2025-08-10 01:34:45.931 INFO log_util.__exit__ 8f85da85-6461-4702-96a7-86847418e3ac  run cost=[0 ms]
2025-08-10 01:34:45.931 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:r90g2 第 1 轮深度搜索...
2025-08-10 01:34:45.931 INFO log_util.__aenter__ 8f85da85-6461-4702-96a7-86847418e3ac  query_decompose start...
2025-08-10 01:34:45.933 INFO log_util.__enter__ 8f85da85-6461-4702-96a7-86847418e3ac enter ask_llm start...
2025-08-10 01:34:45.934 INFO log_util.__exit__ 8f85da85-6461-4702-96a7-86847418e3ac enter ask_llm cost=[1 ms]
2025-08-10 01:34:45.938 ERROR log_util.__aexit__ 8f85da85-6461-4702-96a7-86847418e3ac  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:45.939 INFO log_util.__aexit__ 8f85da85-6461-4702-96a7-86847418e3ac POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:34:53.624 INFO log_util.__aenter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e POST /v1/tool/deepsearch start...
2025-08-10 01:34:53.624 INFO middleware_util.custom_route_handler 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳月份和天气特点","request_id":"geniesession-1754760846817-8847:1754760846837-5145:vx6hi","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:34:53.625 INFO log_util.__enter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  run start...
2025-08-10 01:34:53.625 INFO log_util.__exit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  run cost=[0 ms]
2025-08-10 01:34:53.625 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:vx6hi 第 1 轮深度搜索...
2025-08-10 01:34:53.626 INFO log_util.__aenter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  query_decompose start...
2025-08-10 01:34:53.628 INFO log_util.__enter__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e enter ask_llm start...
2025-08-10 01:34:53.628 INFO log_util.__exit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e enter ask_llm cost=[0 ms]
2025-08-10 01:34:53.633 ERROR log_util.__aexit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:34:53.634 INFO log_util.__aexit__ 7dcc4464-d9ea-4f35-b8fd-4b2f3efa915e POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:00.497 INFO log_util.__aenter__ c327b3ad-b30c-4db2-a42b-4dced6712876 POST /v1/tool/deepsearch start...
2025-08-10 01:35:00.498 INFO middleware_util.custom_route_handler c327b3ad-b30c-4db2-a42b-4dced6712876 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛气候特点及最佳旅游时间","request_id":"geniesession-1754760846817-8847:1754760846837-5145:u5vr1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:00.499 INFO log_util.__enter__ c327b3ad-b30c-4db2-a42b-4dced6712876  run start...
2025-08-10 01:35:00.499 INFO log_util.__exit__ c327b3ad-b30c-4db2-a42b-4dced6712876  run cost=[0 ms]
2025-08-10 01:35:00.500 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:u5vr1 第 1 轮深度搜索...
2025-08-10 01:35:00.500 INFO log_util.__aenter__ c327b3ad-b30c-4db2-a42b-4dced6712876  query_decompose start...
2025-08-10 01:35:00.503 INFO log_util.__enter__ c327b3ad-b30c-4db2-a42b-4dced6712876 enter ask_llm start...
2025-08-10 01:35:00.504 INFO log_util.__exit__ c327b3ad-b30c-4db2-a42b-4dced6712876 enter ask_llm cost=[0 ms]
2025-08-10 01:35:00.510 ERROR log_util.__aexit__ c327b3ad-b30c-4db2-a42b-4dced6712876  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:00.511 INFO log_util.__aexit__ c327b3ad-b30c-4db2-a42b-4dced6712876 POST /v1/tool/deepsearch cost=[13 ms]
2025-08-10 01:35:08.013 INFO log_util.__aenter__ 86a278ae-3a47-401a-b139-0676fde3eeaf POST /v1/tool/deepsearch start...
2025-08-10 01:35:08.013 INFO middleware_util.custom_route_handler 86a278ae-3a47-401a-b139-0676fde3eeaf POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节推荐","request_id":"geniesession-1754760846817-8847:1754760846837-5145:su1pn","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:08.014 INFO log_util.__enter__ 86a278ae-3a47-401a-b139-0676fde3eeaf  run start...
2025-08-10 01:35:08.014 INFO log_util.__exit__ 86a278ae-3a47-401a-b139-0676fde3eeaf  run cost=[0 ms]
2025-08-10 01:35:08.014 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:su1pn 第 1 轮深度搜索...
2025-08-10 01:35:08.015 INFO log_util.__aenter__ 86a278ae-3a47-401a-b139-0676fde3eeaf  query_decompose start...
2025-08-10 01:35:08.017 INFO log_util.__enter__ 86a278ae-3a47-401a-b139-0676fde3eeaf enter ask_llm start...
2025-08-10 01:35:08.017 INFO log_util.__exit__ 86a278ae-3a47-401a-b139-0676fde3eeaf enter ask_llm cost=[0 ms]
2025-08-10 01:35:08.021 ERROR log_util.__aexit__ 86a278ae-3a47-401a-b139-0676fde3eeaf  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:08.022 INFO log_util.__aexit__ 86a278ae-3a47-401a-b139-0676fde3eeaf POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:13.146 INFO log_util.__aenter__ 0c338dcc-9dae-458b-aa90-533b05291612 POST /v1/tool/deepsearch start...
2025-08-10 01:35:13.147 INFO middleware_util.custom_route_handler 0c338dcc-9dae-458b-aa90-533b05291612 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间","request_id":"geniesession-1754760846817-8847:1754760846837-5145:39rm9","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:13.147 INFO log_util.__enter__ 0c338dcc-9dae-458b-aa90-533b05291612  run start...
2025-08-10 01:35:13.147 INFO log_util.__exit__ 0c338dcc-9dae-458b-aa90-533b05291612  run cost=[0 ms]
2025-08-10 01:35:13.148 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:39rm9 第 1 轮深度搜索...
2025-08-10 01:35:13.148 INFO log_util.__aenter__ 0c338dcc-9dae-458b-aa90-533b05291612  query_decompose start...
2025-08-10 01:35:13.150 INFO log_util.__enter__ 0c338dcc-9dae-458b-aa90-533b05291612 enter ask_llm start...
2025-08-10 01:35:13.151 INFO log_util.__exit__ 0c338dcc-9dae-458b-aa90-533b05291612 enter ask_llm cost=[1 ms]
2025-08-10 01:35:13.156 ERROR log_util.__aexit__ 0c338dcc-9dae-458b-aa90-533b05291612  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:13.157 INFO log_util.__aexit__ 0c338dcc-9dae-458b-aa90-533b05291612 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:19.067 INFO log_util.__aenter__ 47a200d9-11e0-45e7-b20f-792afb1abab1 POST /v1/tool/deepsearch start...
2025-08-10 01:35:19.068 INFO middleware_util.custom_route_handler 47a200d9-11e0-45e7-b20f-792afb1abab1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游攻略 天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:wplhi","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:19.069 INFO log_util.__enter__ 47a200d9-11e0-45e7-b20f-792afb1abab1  run start...
2025-08-10 01:35:19.069 INFO log_util.__exit__ 47a200d9-11e0-45e7-b20f-792afb1abab1  run cost=[0 ms]
2025-08-10 01:35:19.069 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:wplhi 第 1 轮深度搜索...
2025-08-10 01:35:19.069 INFO log_util.__aenter__ 47a200d9-11e0-45e7-b20f-792afb1abab1  query_decompose start...
2025-08-10 01:35:19.072 INFO log_util.__enter__ 47a200d9-11e0-45e7-b20f-792afb1abab1 enter ask_llm start...
2025-08-10 01:35:19.072 INFO log_util.__exit__ 47a200d9-11e0-45e7-b20f-792afb1abab1 enter ask_llm cost=[0 ms]
2025-08-10 01:35:19.077 ERROR log_util.__aexit__ 47a200d9-11e0-45e7-b20f-792afb1abab1  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:19.078 INFO log_util.__aexit__ 47a200d9-11e0-45e7-b20f-792afb1abab1 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 01:35:24.355 INFO log_util.__aenter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 POST /v1/tool/deepsearch start...
2025-08-10 01:35:24.357 INFO middleware_util.custom_route_handler 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳季节","request_id":"geniesession-1754760846817-8847:1754760846837-5145:xhno5","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:24.357 INFO log_util.__enter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  run start...
2025-08-10 01:35:24.357 INFO log_util.__exit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  run cost=[0 ms]
2025-08-10 01:35:24.358 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:xhno5 第 1 轮深度搜索...
2025-08-10 01:35:24.358 INFO log_util.__aenter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  query_decompose start...
2025-08-10 01:35:24.360 INFO log_util.__enter__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 enter ask_llm start...
2025-08-10 01:35:24.360 INFO log_util.__exit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 enter ask_llm cost=[0 ms]
2025-08-10 01:35:24.365 ERROR log_util.__aexit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:24.366 INFO log_util.__aexit__ 71ba3f92-c3c3-4ad6-bc77-adfa1d428df3 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-10 01:35:29.253 INFO log_util.__aenter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 POST /v1/tool/deepsearch start...
2025-08-10 01:35:29.254 INFO middleware_util.custom_route_handler 6cea7361-d3f5-49fb-ac2c-b802a1fde252 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛气候和旅游季节","request_id":"geniesession-1754760846817-8847:1754760846837-5145:hw1p1","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:29.255 INFO log_util.__enter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  run start...
2025-08-10 01:35:29.255 INFO log_util.__exit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  run cost=[0 ms]
2025-08-10 01:35:29.255 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:hw1p1 第 1 轮深度搜索...
2025-08-10 01:35:29.255 INFO log_util.__aenter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  query_decompose start...
2025-08-10 01:35:29.258 INFO log_util.__enter__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 enter ask_llm start...
2025-08-10 01:35:29.258 INFO log_util.__exit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 enter ask_llm cost=[0 ms]
2025-08-10 01:35:29.263 ERROR log_util.__aexit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:29.263 INFO log_util.__aexit__ 6cea7361-d3f5-49fb-ac2c-b802a1fde252 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:34.092 INFO log_util.__aenter__ a55c6741-5ebe-442c-8964-d34d4238c39d POST /v1/tool/deepsearch start...
2025-08-10 01:35:34.093 INFO middleware_util.custom_route_handler a55c6741-5ebe-442c-8964-d34d4238c39d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳时间及天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:6osa0","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:34.093 INFO log_util.__enter__ a55c6741-5ebe-442c-8964-d34d4238c39d  run start...
2025-08-10 01:35:34.093 INFO log_util.__exit__ a55c6741-5ebe-442c-8964-d34d4238c39d  run cost=[0 ms]
2025-08-10 01:35:34.094 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:6osa0 第 1 轮深度搜索...
2025-08-10 01:35:34.094 INFO log_util.__aenter__ a55c6741-5ebe-442c-8964-d34d4238c39d  query_decompose start...
2025-08-10 01:35:34.096 INFO log_util.__enter__ a55c6741-5ebe-442c-8964-d34d4238c39d enter ask_llm start...
2025-08-10 01:35:34.096 INFO log_util.__exit__ a55c6741-5ebe-442c-8964-d34d4238c39d enter ask_llm cost=[0 ms]
2025-08-10 01:35:34.101 ERROR log_util.__aexit__ a55c6741-5ebe-442c-8964-d34d4238c39d  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:34.102 INFO log_util.__aexit__ a55c6741-5ebe-442c-8964-d34d4238c39d POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:39.115 INFO log_util.__aenter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 POST /v1/tool/deepsearch start...
2025-08-10 01:35:39.116 INFO middleware_util.custom_route_handler cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节推荐及天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:4l80r","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:39.117 INFO log_util.__enter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  run start...
2025-08-10 01:35:39.117 INFO log_util.__exit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  run cost=[0 ms]
2025-08-10 01:35:39.118 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:4l80r 第 1 轮深度搜索...
2025-08-10 01:35:39.118 INFO log_util.__aenter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  query_decompose start...
2025-08-10 01:35:39.120 INFO log_util.__enter__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 enter ask_llm start...
2025-08-10 01:35:39.121 INFO log_util.__exit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 enter ask_llm cost=[0 ms]
2025-08-10 01:35:39.125 ERROR log_util.__aexit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:39.125 INFO log_util.__aexit__ cdcce4b6-2c8f-4ea0-8a0a-1136b2a4da12 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:44.327 INFO log_util.__aenter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f POST /v1/tool/deepsearch start...
2025-08-10 01:35:44.327 INFO middleware_util.custom_route_handler 778bb32c-f885-4452-9608-7b4d9a2cf63f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳月份","request_id":"geniesession-1754760846817-8847:1754760846837-5145:hmfmb","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:44.328 INFO log_util.__enter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  run start...
2025-08-10 01:35:44.328 INFO log_util.__exit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  run cost=[0 ms]
2025-08-10 01:35:44.328 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:hmfmb 第 1 轮深度搜索...
2025-08-10 01:35:44.328 INFO log_util.__aenter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  query_decompose start...
2025-08-10 01:35:44.331 INFO log_util.__enter__ 778bb32c-f885-4452-9608-7b4d9a2cf63f enter ask_llm start...
2025-08-10 01:35:44.331 INFO log_util.__exit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f enter ask_llm cost=[0 ms]
2025-08-10 01:35:44.335 ERROR log_util.__aexit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:44.336 INFO log_util.__aexit__ 778bb32c-f885-4452-9608-7b4d9a2cf63f POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:35:49.660 INFO log_util.__aenter__ a9e95911-5809-4d68-b6a2-98064e635d8b POST /v1/tool/deepsearch start...
2025-08-10 01:35:49.661 INFO middleware_util.custom_route_handler a9e95911-5809-4d68-b6a2-98064e635d8b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节和天气特点","request_id":"geniesession-1754760846817-8847:1754760846837-5145:f26ho","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:49.662 INFO log_util.__enter__ a9e95911-5809-4d68-b6a2-98064e635d8b  run start...
2025-08-10 01:35:49.662 INFO log_util.__exit__ a9e95911-5809-4d68-b6a2-98064e635d8b  run cost=[0 ms]
2025-08-10 01:35:49.662 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:f26ho 第 1 轮深度搜索...
2025-08-10 01:35:49.662 INFO log_util.__aenter__ a9e95911-5809-4d68-b6a2-98064e635d8b  query_decompose start...
2025-08-10 01:35:49.665 INFO log_util.__enter__ a9e95911-5809-4d68-b6a2-98064e635d8b enter ask_llm start...
2025-08-10 01:35:49.665 INFO log_util.__exit__ a9e95911-5809-4d68-b6a2-98064e635d8b enter ask_llm cost=[0 ms]
2025-08-10 01:35:49.669 ERROR log_util.__aexit__ a9e95911-5809-4d68-b6a2-98064e635d8b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:49.670 INFO log_util.__aexit__ a9e95911-5809-4d68-b6a2-98064e635d8b POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:35:55.297 INFO log_util.__aenter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f POST /v1/tool/deepsearch start...
2025-08-10 01:35:55.298 INFO middleware_util.custom_route_handler ea1b79d2-cc80-497f-8c4d-5081c3c2236f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳季节和天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:91una","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:35:55.299 INFO log_util.__enter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  run start...
2025-08-10 01:35:55.299 INFO log_util.__exit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  run cost=[0 ms]
2025-08-10 01:35:55.299 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:91una 第 1 轮深度搜索...
2025-08-10 01:35:55.300 INFO log_util.__aenter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  query_decompose start...
2025-08-10 01:35:55.302 INFO log_util.__enter__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f enter ask_llm start...
2025-08-10 01:35:55.302 INFO log_util.__exit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f enter ask_llm cost=[0 ms]
2025-08-10 01:35:55.307 ERROR log_util.__aexit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:35:55.308 INFO log_util.__aexit__ ea1b79d2-cc80-497f-8c4d-5081c3c2236f POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:36:00.481 INFO log_util.__aenter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 POST /v1/tool/deepsearch start...
2025-08-10 01:36:00.482 INFO middleware_util.custom_route_handler 3a9885fb-8800-4398-b217-ba95d7bc9c00 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游天气指南","request_id":"geniesession-1754760846817-8847:1754760846837-5145:4ml7p","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:36:00.482 INFO log_util.__enter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  run start...
2025-08-10 01:36:00.483 INFO log_util.__exit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  run cost=[0 ms]
2025-08-10 01:36:00.483 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:4ml7p 第 1 轮深度搜索...
2025-08-10 01:36:00.483 INFO log_util.__aenter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  query_decompose start...
2025-08-10 01:36:00.485 INFO log_util.__enter__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 enter ask_llm start...
2025-08-10 01:36:00.486 INFO log_util.__exit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 enter ask_llm cost=[1 ms]
2025-08-10 01:36:00.490 ERROR log_util.__aexit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:36:00.490 INFO log_util.__aexit__ 3a9885fb-8800-4398-b217-ba95d7bc9c00 POST /v1/tool/deepsearch cost=[9 ms]
2025-08-10 01:36:05.557 INFO log_util.__aenter__ e49bae8b-132a-4c32-bd00-b80a76e24297 POST /v1/tool/deepsearch start...
2025-08-10 01:36:05.558 INFO middleware_util.custom_route_handler e49bae8b-132a-4c32-bd00-b80a76e24297 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳天气","request_id":"geniesession-1754760846817-8847:1754760846837-5145:bv3hj","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:36:05.558 INFO log_util.__enter__ e49bae8b-132a-4c32-bd00-b80a76e24297  run start...
2025-08-10 01:36:05.559 INFO log_util.__exit__ e49bae8b-132a-4c32-bd00-b80a76e24297  run cost=[0 ms]
2025-08-10 01:36:05.559 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:bv3hj 第 1 轮深度搜索...
2025-08-10 01:36:05.559 INFO log_util.__aenter__ e49bae8b-132a-4c32-bd00-b80a76e24297  query_decompose start...
2025-08-10 01:36:05.562 INFO log_util.__enter__ e49bae8b-132a-4c32-bd00-b80a76e24297 enter ask_llm start...
2025-08-10 01:36:05.562 INFO log_util.__exit__ e49bae8b-132a-4c32-bd00-b80a76e24297 enter ask_llm cost=[0 ms]
2025-08-10 01:36:05.566 ERROR log_util.__aexit__ e49bae8b-132a-4c32-bd00-b80a76e24297  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model==deepseek/deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:36:05.567 INFO log_util.__aexit__ e49bae8b-132a-4c32-bd00-b80a76e24297 POST /v1/tool/deepsearch cost=[10 ms]
2025-08-10 01:37:05.313 INFO log_util.__aenter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 POST /v1/tool/deepsearch start...
2025-08-10 01:37:05.314 INFO middleware_util.custom_route_handler 2b90d797-42f5-4e71-9977-af6a44e93bd5 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游最佳天气月份","request_id":"geniesession-1754760846817-8847:1754760846837-5145:v4ke7","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:37:05.316 INFO log_util.__enter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  run start...
2025-08-10 01:37:05.316 INFO log_util.__exit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  run cost=[0 ms]
2025-08-10 01:37:05.316 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:v4ke7 第 1 轮深度搜索...
2025-08-10 01:37:05.316 INFO log_util.__aenter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  query_decompose start...
2025-08-10 01:37:05.320 INFO log_util.__enter__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 enter ask_llm start...
2025-08-10 01:37:05.320 INFO log_util.__exit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 enter ask_llm cost=[0 ms]
2025-08-10 01:37:05.329 ERROR log_util.__aexit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:37:05.330 INFO log_util.__aexit__ 2b90d797-42f5-4e71-9977-af6a44e93bd5 POST /v1/tool/deepsearch cost=[16 ms]
2025-08-10 01:37:10.527 INFO log_util.__aenter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b POST /v1/tool/deepsearch start...
2025-08-10 01:37:10.529 INFO middleware_util.custom_route_handler 6819e5b1-9c56-496a-b9f6-436c9cebc62b POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"巴厘岛旅游季节和天气特点","request_id":"geniesession-1754760846817-8847:1754760846837-5145:gecz4","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-10 01:37:10.530 INFO log_util.__enter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  run start...
2025-08-10 01:37:10.530 INFO log_util.__exit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  run cost=[0 ms]
2025-08-10 01:37:10.530 INFO deepsearch.run geniesession-1754760846817-8847:1754760846837-5145:gecz4 第 1 轮深度搜索...
2025-08-10 01:37:10.530 INFO log_util.__aenter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  query_decompose start...
2025-08-10 01:37:10.533 INFO log_util.__enter__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b enter ask_llm start...
2025-08-10 01:37:10.534 INFO log_util.__exit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b enter ask_llm cost=[1 ms]
2025-08-10 01:37:10.540 ERROR log_util.__aexit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b  query_decompose error=Traceback (most recent call last):
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 31, in query_decompose
    async for chunk in ask_llm(
  File "F:\tmp\joyagent-jdgenie\genie-tool\genie_tool\util\llm_util.py", line 41, in ask_llm
    response = await acompletion(
               ^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1552, in wrapper_async
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\utils.py", line 1410, in wrapper_async
    result = await original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\main.py", line 498, in acompletion
    _, custom_llm_provider, _, _ = get_llm_provider(
                                   ^^^^^^^^^^^^^^^^^
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "F:\tmp\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=deepseek-chat
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-10 01:37:10.541 INFO log_util.__aexit__ 6819e5b1-9c56-496a-b9f6-436c9cebc62b POST /v1/tool/deepsearch cost=[14 ms]
