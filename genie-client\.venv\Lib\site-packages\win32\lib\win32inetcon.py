# Generated by h2py from \mssdk\include\WinInet.h

INTERNET_INVALID_PORT_NUMBER = 0
INTERNET_DEFAULT_PORT = 0
INTERNET_DEFAULT_FTP_PORT = 21
INTERNET_DEFAULT_GOPHER_PORT = 70
INTERNET_DEFAULT_HTTP_PORT = 80
INTERNET_DEFAULT_HTTPS_PORT = 443
INTERNET_DEFAULT_SOCKS_PORT = 1080
INTERNET_MAX_HOST_NAME_LENGTH = 256
INTERNET_MAX_USER_NAME_LENGTH = 128
INTERNET_MAX_PASSWORD_LENGTH = 128
INTERNET_MAX_PORT_NUMBER_LENGTH = 5
INTERNET_MAX_PORT_NUMBER_VALUE = 65535
INTERNET_MAX_PATH_LENGTH = 2048
INTERNET_MAX_SCHEME_LENGTH = 32
INTERNET_KEEP_ALIVE_ENABLED = 1
INTERNET_KEEP_ALIVE_DISABLED = 0
INTERNET_REQFLAG_FROM_CACHE = 0x00000001
INTERNET_REQFLAG_ASYNC = 0x00000002
INTERNET_REQFLAG_VIA_PROXY = 0x00000004
INTERNET_REQFLAG_NO_HEADERS = 0x00000008
INTERNET_REQFLAG_PASSIVE = 0x00000010
INTERNET_REQFLAG_CACHE_WRITE_DISABLED = 0x00000040
INTERNET_REQFLAG_NET_TIMEOUT = 0x00000080
INTERNET_FLAG_RELOAD = -2147483648
INTERNET_FLAG_RAW_DATA = 0x40000000
INTERNET_FLAG_EXISTING_CONNECT = 0x20000000
INTERNET_FLAG_ASYNC = 0x10000000
INTERNET_FLAG_PASSIVE = 0x08000000
INTERNET_FLAG_NO_CACHE_WRITE = 0x04000000
INTERNET_FLAG_DONT_CACHE = INTERNET_FLAG_NO_CACHE_WRITE
INTERNET_FLAG_MAKE_PERSISTENT = 0x02000000
INTERNET_FLAG_FROM_CACHE = 0x01000000
INTERNET_FLAG_OFFLINE = INTERNET_FLAG_FROM_CACHE
INTERNET_FLAG_SECURE = 0x00800000
INTERNET_FLAG_KEEP_CONNECTION = 0x00400000
INTERNET_FLAG_NO_AUTO_REDIRECT = 0x00200000
INTERNET_FLAG_READ_PREFETCH = 0x00100000
INTERNET_FLAG_NO_COOKIES = 0x00080000
INTERNET_FLAG_NO_AUTH = 0x00040000
INTERNET_FLAG_RESTRICTED_ZONE = 0x00020000
INTERNET_FLAG_CACHE_IF_NET_FAIL = 0x00010000
INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP = 0x00008000
INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS = 0x00004000
INTERNET_FLAG_IGNORE_CERT_DATE_INVALID = 0x00002000
INTERNET_FLAG_IGNORE_CERT_CN_INVALID = 0x00001000
INTERNET_FLAG_RESYNCHRONIZE = 0x00000800
INTERNET_FLAG_HYPERLINK = 0x00000400
INTERNET_FLAG_NO_UI = 0x00000200
INTERNET_FLAG_PRAGMA_NOCACHE = 0x00000100
INTERNET_FLAG_CACHE_ASYNC = 0x00000080
INTERNET_FLAG_FORMS_SUBMIT = 0x00000040
INTERNET_FLAG_FWD_BACK = 0x00000020
INTERNET_FLAG_NEED_FILE = 0x00000010
INTERNET_FLAG_MUST_CACHE_REQUEST = INTERNET_FLAG_NEED_FILE
SECURITY_INTERNET_MASK = (
    INTERNET_FLAG_IGNORE_CERT_CN_INVALID
    | INTERNET_FLAG_IGNORE_CERT_DATE_INVALID
    | INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS
    | INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP
)
INTERNET_ERROR_MASK_INSERT_CDROM = 0x1
INTERNET_ERROR_MASK_COMBINED_SEC_CERT = 0x2
INTERNET_ERROR_MASK_NEED_MSN_SSPI_PKG = 0x4
INTERNET_ERROR_MASK_LOGIN_FAILURE_DISPLAY_ENTITY_BODY = 0x8
WININET_API_FLAG_ASYNC = 0x00000001
WININET_API_FLAG_SYNC = 0x00000004
WININET_API_FLAG_USE_CONTEXT = 0x00000008
INTERNET_NO_CALLBACK = 0
IDSI_FLAG_KEEP_ALIVE = 0x00000001
IDSI_FLAG_SECURE = 0x00000002
IDSI_FLAG_PROXY = 0x00000004
IDSI_FLAG_TUNNEL = 0x00000008
INTERNET_PER_CONN_FLAGS = 1
INTERNET_PER_CONN_PROXY_SERVER = 2
INTERNET_PER_CONN_PROXY_BYPASS = 3
INTERNET_PER_CONN_AUTOCONFIG_URL = 4
INTERNET_PER_CONN_AUTODISCOVERY_FLAGS = 5
INTERNET_PER_CONN_AUTOCONFIG_SECONDARY_URL = 6
INTERNET_PER_CONN_AUTOCONFIG_RELOAD_DELAY_MINS = 7
INTERNET_PER_CONN_AUTOCONFIG_LAST_DETECT_TIME = 8
INTERNET_PER_CONN_AUTOCONFIG_LAST_DETECT_URL = 9
PROXY_TYPE_DIRECT = 0x00000001
PROXY_TYPE_PROXY = 0x00000002
PROXY_TYPE_AUTO_PROXY_URL = 0x00000004
PROXY_TYPE_AUTO_DETECT = 0x00000008
AUTO_PROXY_FLAG_USER_SET = 0x00000001
AUTO_PROXY_FLAG_ALWAYS_DETECT = 0x00000002
AUTO_PROXY_FLAG_DETECTION_RUN = 0x00000004
AUTO_PROXY_FLAG_MIGRATED = 0x00000008
AUTO_PROXY_FLAG_DONT_CACHE_PROXY_RESULT = 0x00000010
AUTO_PROXY_FLAG_CACHE_INIT_RUN = 0x00000020
AUTO_PROXY_FLAG_DETECTION_SUSPECT = 0x00000040
ISO_FORCE_DISCONNECTED = 0x00000001
INTERNET_RFC1123_FORMAT = 0
INTERNET_RFC1123_BUFSIZE = 30
ICU_ESCAPE = -2147483648
ICU_ESCAPE_AUTHORITY = 0x00002000
ICU_REJECT_USERPWD = 0x00004000
ICU_USERNAME = 0x40000000
ICU_NO_ENCODE = 0x20000000
ICU_DECODE = 0x10000000
ICU_NO_META = 0x08000000
ICU_ENCODE_SPACES_ONLY = 0x04000000
ICU_BROWSER_MODE = 0x02000000
ICU_ENCODE_PERCENT = 0x00001000
INTERNET_OPEN_TYPE_PRECONFIG = 0
INTERNET_OPEN_TYPE_DIRECT = 1
INTERNET_OPEN_TYPE_PROXY = 3
INTERNET_OPEN_TYPE_PRECONFIG_WITH_NO_AUTOPROXY = 4
PRE_CONFIG_INTERNET_ACCESS = INTERNET_OPEN_TYPE_PRECONFIG
LOCAL_INTERNET_ACCESS = INTERNET_OPEN_TYPE_DIRECT
CERN_PROXY_INTERNET_ACCESS = INTERNET_OPEN_TYPE_PROXY
INTERNET_SERVICE_FTP = 1
INTERNET_SERVICE_GOPHER = 2
INTERNET_SERVICE_HTTP = 3
IRF_ASYNC = WININET_API_FLAG_ASYNC
IRF_SYNC = WININET_API_FLAG_SYNC
IRF_USE_CONTEXT = WININET_API_FLAG_USE_CONTEXT
IRF_NO_WAIT = 0x00000008
ISO_GLOBAL = 0x00000001
ISO_REGISTRY = 0x00000002
ISO_VALID_FLAGS = ISO_GLOBAL | ISO_REGISTRY
INTERNET_OPTION_CALLBACK = 1
INTERNET_OPTION_CONNECT_TIMEOUT = 2
INTERNET_OPTION_CONNECT_RETRIES = 3
INTERNET_OPTION_CONNECT_BACKOFF = 4
INTERNET_OPTION_SEND_TIMEOUT = 5
INTERNET_OPTION_CONTROL_SEND_TIMEOUT = INTERNET_OPTION_SEND_TIMEOUT
INTERNET_OPTION_RECEIVE_TIMEOUT = 6
INTERNET_OPTION_CONTROL_RECEIVE_TIMEOUT = INTERNET_OPTION_RECEIVE_TIMEOUT
INTERNET_OPTION_DATA_SEND_TIMEOUT = 7
INTERNET_OPTION_DATA_RECEIVE_TIMEOUT = 8
INTERNET_OPTION_HANDLE_TYPE = 9
INTERNET_OPTION_LISTEN_TIMEOUT = 11
INTERNET_OPTION_READ_BUFFER_SIZE = 12
INTERNET_OPTION_WRITE_BUFFER_SIZE = 13
INTERNET_OPTION_ASYNC_ID = 15
INTERNET_OPTION_ASYNC_PRIORITY = 16
INTERNET_OPTION_PARENT_HANDLE = 21
INTERNET_OPTION_KEEP_CONNECTION = 22
INTERNET_OPTION_REQUEST_FLAGS = 23
INTERNET_OPTION_EXTENDED_ERROR = 24
INTERNET_OPTION_OFFLINE_MODE = 26
INTERNET_OPTION_CACHE_STREAM_HANDLE = 27
INTERNET_OPTION_USERNAME = 28
INTERNET_OPTION_PASSWORD = 29
INTERNET_OPTION_ASYNC = 30
INTERNET_OPTION_SECURITY_FLAGS = 31
INTERNET_OPTION_SECURITY_CERTIFICATE_STRUCT = 32
INTERNET_OPTION_DATAFILE_NAME = 33
INTERNET_OPTION_URL = 34
INTERNET_OPTION_SECURITY_CERTIFICATE = 35
INTERNET_OPTION_SECURITY_KEY_BITNESS = 36
INTERNET_OPTION_REFRESH = 37
INTERNET_OPTION_PROXY = 38
INTERNET_OPTION_SETTINGS_CHANGED = 39
INTERNET_OPTION_VERSION = 40
INTERNET_OPTION_USER_AGENT = 41
INTERNET_OPTION_END_BROWSER_SESSION = 42
INTERNET_OPTION_PROXY_USERNAME = 43
INTERNET_OPTION_PROXY_PASSWORD = 44
INTERNET_OPTION_CONTEXT_VALUE = 45
INTERNET_OPTION_CONNECT_LIMIT = 46
INTERNET_OPTION_SECURITY_SELECT_CLIENT_CERT = 47
INTERNET_OPTION_POLICY = 48
INTERNET_OPTION_DISCONNECTED_TIMEOUT = 49
INTERNET_OPTION_CONNECTED_STATE = 50
INTERNET_OPTION_IDLE_STATE = 51
INTERNET_OPTION_OFFLINE_SEMANTICS = 52
INTERNET_OPTION_SECONDARY_CACHE_KEY = 53
INTERNET_OPTION_CALLBACK_FILTER = 54
INTERNET_OPTION_CONNECT_TIME = 55
INTERNET_OPTION_SEND_THROUGHPUT = 56
INTERNET_OPTION_RECEIVE_THROUGHPUT = 57
INTERNET_OPTION_REQUEST_PRIORITY = 58
INTERNET_OPTION_HTTP_VERSION = 59
INTERNET_OPTION_RESET_URLCACHE_SESSION = 60
INTERNET_OPTION_ERROR_MASK = 62
INTERNET_OPTION_FROM_CACHE_TIMEOUT = 63
INTERNET_OPTION_BYPASS_EDITED_ENTRY = 64
INTERNET_OPTION_DIAGNOSTIC_SOCKET_INFO = 67
INTERNET_OPTION_CODEPAGE = 68
INTERNET_OPTION_CACHE_TIMESTAMPS = 69
INTERNET_OPTION_DISABLE_AUTODIAL = 70
INTERNET_OPTION_MAX_CONNS_PER_SERVER = 73
INTERNET_OPTION_MAX_CONNS_PER_1_0_SERVER = 74
INTERNET_OPTION_PER_CONNECTION_OPTION = 75
INTERNET_OPTION_DIGEST_AUTH_UNLOAD = 76
INTERNET_OPTION_IGNORE_OFFLINE = 77
INTERNET_OPTION_IDENTITY = 78
INTERNET_OPTION_REMOVE_IDENTITY = 79
INTERNET_OPTION_ALTER_IDENTITY = 80
INTERNET_OPTION_SUPPRESS_BEHAVIOR = 81
INTERNET_OPTION_AUTODIAL_MODE = 82
INTERNET_OPTION_AUTODIAL_CONNECTION = 83
INTERNET_OPTION_CLIENT_CERT_CONTEXT = 84
INTERNET_OPTION_AUTH_FLAGS = 85
INTERNET_OPTION_COOKIES_3RD_PARTY = 86
INTERNET_OPTION_DISABLE_PASSPORT_AUTH = 87
INTERNET_OPTION_SEND_UTF8_SERVERNAME_TO_PROXY = 88
INTERNET_OPTION_EXEMPT_CONNECTION_LIMIT = 89
INTERNET_OPTION_ENABLE_PASSPORT_AUTH = 90
INTERNET_OPTION_HIBERNATE_INACTIVE_WORKER_THREADS = 91
INTERNET_OPTION_ACTIVATE_WORKER_THREADS = 92
INTERNET_OPTION_RESTORE_WORKER_THREAD_DEFAULTS = 93
INTERNET_OPTION_SOCKET_SEND_BUFFER_LENGTH = 94
INTERNET_OPTION_PROXY_SETTINGS_CHANGED = 95
INTERNET_FIRST_OPTION = INTERNET_OPTION_CALLBACK
INTERNET_LAST_OPTION = INTERNET_OPTION_PROXY_SETTINGS_CHANGED
INTERNET_PRIORITY_FOREGROUND = 1000
INTERNET_HANDLE_TYPE_INTERNET = 1
INTERNET_HANDLE_TYPE_CONNECT_FTP = 2
INTERNET_HANDLE_TYPE_CONNECT_GOPHER = 3
INTERNET_HANDLE_TYPE_CONNECT_HTTP = 4
INTERNET_HANDLE_TYPE_FTP_FIND = 5
INTERNET_HANDLE_TYPE_FTP_FIND_HTML = 6
INTERNET_HANDLE_TYPE_FTP_FILE = 7
INTERNET_HANDLE_TYPE_FTP_FILE_HTML = 8
INTERNET_HANDLE_TYPE_GOPHER_FIND = 9
INTERNET_HANDLE_TYPE_GOPHER_FIND_HTML = 10
INTERNET_HANDLE_TYPE_GOPHER_FILE = 11
INTERNET_HANDLE_TYPE_GOPHER_FILE_HTML = 12
INTERNET_HANDLE_TYPE_HTTP_REQUEST = 13
INTERNET_HANDLE_TYPE_FILE_REQUEST = 14
AUTH_FLAG_DISABLE_NEGOTIATE = 0x00000001
AUTH_FLAG_ENABLE_NEGOTIATE = 0x00000002
SECURITY_FLAG_SECURE = 0x00000001
SECURITY_FLAG_STRENGTH_WEAK = 0x10000000
SECURITY_FLAG_STRENGTH_MEDIUM = 0x40000000
SECURITY_FLAG_STRENGTH_STRONG = 0x20000000
SECURITY_FLAG_UNKNOWNBIT = -2147483648
SECURITY_FLAG_FORTEZZA = 0x08000000
SECURITY_FLAG_NORMALBITNESS = SECURITY_FLAG_STRENGTH_WEAK
SECURITY_FLAG_SSL = 0x00000002
SECURITY_FLAG_SSL3 = 0x00000004
SECURITY_FLAG_PCT = 0x00000008
SECURITY_FLAG_PCT4 = 0x00000010
SECURITY_FLAG_IETFSSL4 = 0x00000020
SECURITY_FLAG_40BIT = SECURITY_FLAG_STRENGTH_WEAK
SECURITY_FLAG_128BIT = SECURITY_FLAG_STRENGTH_STRONG
SECURITY_FLAG_56BIT = SECURITY_FLAG_STRENGTH_MEDIUM
SECURITY_FLAG_IGNORE_REVOCATION = 0x00000080
SECURITY_FLAG_IGNORE_UNKNOWN_CA = 0x00000100
SECURITY_FLAG_IGNORE_WRONG_USAGE = 0x00000200
SECURITY_FLAG_IGNORE_CERT_CN_INVALID = INTERNET_FLAG_IGNORE_CERT_CN_INVALID
SECURITY_FLAG_IGNORE_CERT_DATE_INVALID = INTERNET_FLAG_IGNORE_CERT_DATE_INVALID
SECURITY_FLAG_IGNORE_CERT_WRONG_USAGE = 0x00000200
SECURITY_FLAG_IGNORE_REDIRECT_TO_HTTPS = INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS
SECURITY_FLAG_IGNORE_REDIRECT_TO_HTTP = INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP
SECURITY_SET_MASK = (
    SECURITY_FLAG_IGNORE_REVOCATION
    | SECURITY_FLAG_IGNORE_UNKNOWN_CA
    | SECURITY_FLAG_IGNORE_CERT_CN_INVALID
    | SECURITY_FLAG_IGNORE_CERT_DATE_INVALID
    | SECURITY_FLAG_IGNORE_WRONG_USAGE
)
AUTODIAL_MODE_NEVER = 1
AUTODIAL_MODE_ALWAYS = 2
AUTODIAL_MODE_NO_NETWORK_PRESENT = 4
INTERNET_STATUS_RESOLVING_NAME = 10
INTERNET_STATUS_NAME_RESOLVED = 11
INTERNET_STATUS_CONNECTING_TO_SERVER = 20
INTERNET_STATUS_CONNECTED_TO_SERVER = 21
INTERNET_STATUS_SENDING_REQUEST = 30
INTERNET_STATUS_REQUEST_SENT = 31
INTERNET_STATUS_RECEIVING_RESPONSE = 40
INTERNET_STATUS_RESPONSE_RECEIVED = 41
INTERNET_STATUS_CTL_RESPONSE_RECEIVED = 42
INTERNET_STATUS_PREFETCH = 43
INTERNET_STATUS_CLOSING_CONNECTION = 50
INTERNET_STATUS_CONNECTION_CLOSED = 51
INTERNET_STATUS_HANDLE_CREATED = 60
INTERNET_STATUS_HANDLE_CLOSING = 70
INTERNET_STATUS_DETECTING_PROXY = 80
INTERNET_STATUS_REQUEST_COMPLETE = 100
INTERNET_STATUS_REDIRECT = 110
INTERNET_STATUS_INTERMEDIATE_RESPONSE = 120
INTERNET_STATUS_USER_INPUT_REQUIRED = 140
INTERNET_STATUS_STATE_CHANGE = 200
INTERNET_STATUS_COOKIE_SENT = 320
INTERNET_STATUS_COOKIE_RECEIVED = 321
INTERNET_STATUS_PRIVACY_IMPACTED = 324
INTERNET_STATUS_P3P_HEADER = 325
INTERNET_STATUS_P3P_POLICYREF = 326
INTERNET_STATUS_COOKIE_HISTORY = 327
INTERNET_STATE_CONNECTED = 0x00000001
INTERNET_STATE_DISCONNECTED = 0x00000002
INTERNET_STATE_DISCONNECTED_BY_USER = 0x00000010
INTERNET_STATE_IDLE = 0x00000100
INTERNET_STATE_BUSY = 0x00000200
FTP_TRANSFER_TYPE_UNKNOWN = 0x00000000
FTP_TRANSFER_TYPE_ASCII = 0x00000001
FTP_TRANSFER_TYPE_BINARY = 0x00000002
FTP_TRANSFER_TYPE_MASK = FTP_TRANSFER_TYPE_ASCII | FTP_TRANSFER_TYPE_BINARY
MAX_GOPHER_DISPLAY_TEXT = 128
MAX_GOPHER_SELECTOR_TEXT = 256
MAX_GOPHER_HOST_NAME = INTERNET_MAX_HOST_NAME_LENGTH
MAX_GOPHER_LOCATOR_LENGTH = (
    1
    + MAX_GOPHER_DISPLAY_TEXT
    + 1
    + MAX_GOPHER_SELECTOR_TEXT
    + 1
    + MAX_GOPHER_HOST_NAME
    + 1
    + INTERNET_MAX_PORT_NUMBER_LENGTH
    + 1
    + 1
    + 2
)
GOPHER_TYPE_TEXT_FILE = 0x00000001
GOPHER_TYPE_DIRECTORY = 0x00000002
GOPHER_TYPE_CSO = 0x00000004
GOPHER_TYPE_ERROR = 0x00000008
GOPHER_TYPE_MAC_BINHEX = 0x00000010
GOPHER_TYPE_DOS_ARCHIVE = 0x00000020
GOPHER_TYPE_UNIX_UUENCODED = 0x00000040
GOPHER_TYPE_INDEX_SERVER = 0x00000080
GOPHER_TYPE_TELNET = 0x00000100
GOPHER_TYPE_BINARY = 0x00000200
GOPHER_TYPE_REDUNDANT = 0x00000400
GOPHER_TYPE_TN3270 = 0x00000800
GOPHER_TYPE_GIF = 0x00001000
GOPHER_TYPE_IMAGE = 0x00002000
GOPHER_TYPE_BITMAP = 0x00004000
GOPHER_TYPE_MOVIE = 0x00008000
GOPHER_TYPE_SOUND = 0x00010000
GOPHER_TYPE_HTML = 0x00020000
GOPHER_TYPE_PDF = 0x00040000
GOPHER_TYPE_CALENDAR = 0x00080000
GOPHER_TYPE_INLINE = 0x00100000
GOPHER_TYPE_UNKNOWN = 0x20000000
GOPHER_TYPE_ASK = 0x40000000
GOPHER_TYPE_GOPHER_PLUS = -2147483648
GOPHER_TYPE_FILE_MASK = (
    GOPHER_TYPE_TEXT_FILE
    | GOPHER_TYPE_MAC_BINHEX
    | GOPHER_TYPE_DOS_ARCHIVE
    | GOPHER_TYPE_UNIX_UUENCODED
    | GOPHER_TYPE_BINARY
    | GOPHER_TYPE_GIF
    | GOPHER_TYPE_IMAGE
    | GOPHER_TYPE_BITMAP
    | GOPHER_TYPE_MOVIE
    | GOPHER_TYPE_SOUND
    | GOPHER_TYPE_HTML
    | GOPHER_TYPE_PDF
    | GOPHER_TYPE_CALENDAR
    | GOPHER_TYPE_INLINE
)
MAX_GOPHER_CATEGORY_NAME = 128
MAX_GOPHER_ATTRIBUTE_NAME = 128
MIN_GOPHER_ATTRIBUTE_LENGTH = 256
GOPHER_ATTRIBUTE_ID_BASE = -1412641792
GOPHER_CATEGORY_ID_ALL = GOPHER_ATTRIBUTE_ID_BASE + 1
GOPHER_CATEGORY_ID_INFO = GOPHER_ATTRIBUTE_ID_BASE + 2
GOPHER_CATEGORY_ID_ADMIN = GOPHER_ATTRIBUTE_ID_BASE + 3
GOPHER_CATEGORY_ID_VIEWS = GOPHER_ATTRIBUTE_ID_BASE + 4
GOPHER_CATEGORY_ID_ABSTRACT = GOPHER_ATTRIBUTE_ID_BASE + 5
GOPHER_CATEGORY_ID_VERONICA = GOPHER_ATTRIBUTE_ID_BASE + 6
GOPHER_CATEGORY_ID_ASK = GOPHER_ATTRIBUTE_ID_BASE + 7
GOPHER_CATEGORY_ID_UNKNOWN = GOPHER_ATTRIBUTE_ID_BASE + 8
GOPHER_ATTRIBUTE_ID_ALL = GOPHER_ATTRIBUTE_ID_BASE + 9
GOPHER_ATTRIBUTE_ID_ADMIN = GOPHER_ATTRIBUTE_ID_BASE + 10
GOPHER_ATTRIBUTE_ID_MOD_DATE = GOPHER_ATTRIBUTE_ID_BASE + 11
GOPHER_ATTRIBUTE_ID_TTL = GOPHER_ATTRIBUTE_ID_BASE + 12
GOPHER_ATTRIBUTE_ID_SCORE = GOPHER_ATTRIBUTE_ID_BASE + 13
GOPHER_ATTRIBUTE_ID_RANGE = GOPHER_ATTRIBUTE_ID_BASE + 14
GOPHER_ATTRIBUTE_ID_SITE = GOPHER_ATTRIBUTE_ID_BASE + 15
GOPHER_ATTRIBUTE_ID_ORG = GOPHER_ATTRIBUTE_ID_BASE + 16
GOPHER_ATTRIBUTE_ID_LOCATION = GOPHER_ATTRIBUTE_ID_BASE + 17
GOPHER_ATTRIBUTE_ID_GEOG = GOPHER_ATTRIBUTE_ID_BASE + 18
GOPHER_ATTRIBUTE_ID_TIMEZONE = GOPHER_ATTRIBUTE_ID_BASE + 19
GOPHER_ATTRIBUTE_ID_PROVIDER = GOPHER_ATTRIBUTE_ID_BASE + 20
GOPHER_ATTRIBUTE_ID_VERSION = GOPHER_ATTRIBUTE_ID_BASE + 21
GOPHER_ATTRIBUTE_ID_ABSTRACT = GOPHER_ATTRIBUTE_ID_BASE + 22
GOPHER_ATTRIBUTE_ID_VIEW = GOPHER_ATTRIBUTE_ID_BASE + 23
GOPHER_ATTRIBUTE_ID_TREEWALK = GOPHER_ATTRIBUTE_ID_BASE + 24
GOPHER_ATTRIBUTE_ID_UNKNOWN = GOPHER_ATTRIBUTE_ID_BASE + 25
HTTP_MAJOR_VERSION = 1
HTTP_MINOR_VERSION = 0
HTTP_VERSIONA = "HTTP/1.0"
HTTP_VERSION = HTTP_VERSIONA
HTTP_QUERY_MIME_VERSION = 0
HTTP_QUERY_CONTENT_TYPE = 1
HTTP_QUERY_CONTENT_TRANSFER_ENCODING = 2
HTTP_QUERY_CONTENT_ID = 3
HTTP_QUERY_CONTENT_DESCRIPTION = 4
HTTP_QUERY_CONTENT_LENGTH = 5
HTTP_QUERY_CONTENT_LANGUAGE = 6
HTTP_QUERY_ALLOW = 7
HTTP_QUERY_PUBLIC = 8
HTTP_QUERY_DATE = 9
HTTP_QUERY_EXPIRES = 10
HTTP_QUERY_LAST_MODIFIED = 11
HTTP_QUERY_MESSAGE_ID = 12
HTTP_QUERY_URI = 13
HTTP_QUERY_DERIVED_FROM = 14
HTTP_QUERY_COST = 15
HTTP_QUERY_LINK = 16
HTTP_QUERY_PRAGMA = 17
HTTP_QUERY_VERSION = 18
HTTP_QUERY_STATUS_CODE = 19
HTTP_QUERY_STATUS_TEXT = 20
HTTP_QUERY_RAW_HEADERS = 21
HTTP_QUERY_RAW_HEADERS_CRLF = 22
HTTP_QUERY_CONNECTION = 23
HTTP_QUERY_ACCEPT = 24
HTTP_QUERY_ACCEPT_CHARSET = 25
HTTP_QUERY_ACCEPT_ENCODING = 26
HTTP_QUERY_ACCEPT_LANGUAGE = 27
HTTP_QUERY_AUTHORIZATION = 28
HTTP_QUERY_CONTENT_ENCODING = 29
HTTP_QUERY_FORWARDED = 30
HTTP_QUERY_FROM = 31
HTTP_QUERY_IF_MODIFIED_SINCE = 32
HTTP_QUERY_LOCATION = 33
HTTP_QUERY_ORIG_URI = 34
HTTP_QUERY_REFERER = 35
HTTP_QUERY_RETRY_AFTER = 36
HTTP_QUERY_SERVER = 37
HTTP_QUERY_TITLE = 38
HTTP_QUERY_USER_AGENT = 39
HTTP_QUERY_WWW_AUTHENTICATE = 40
HTTP_QUERY_PROXY_AUTHENTICATE = 41
HTTP_QUERY_ACCEPT_RANGES = 42
HTTP_QUERY_SET_COOKIE = 43
HTTP_QUERY_COOKIE = 44
HTTP_QUERY_REQUEST_METHOD = 45
HTTP_QUERY_REFRESH = 46
HTTP_QUERY_CONTENT_DISPOSITION = 47
HTTP_QUERY_AGE = 48
HTTP_QUERY_CACHE_CONTROL = 49
HTTP_QUERY_CONTENT_BASE = 50
HTTP_QUERY_CONTENT_LOCATION = 51
HTTP_QUERY_CONTENT_MD5 = 52
HTTP_QUERY_CONTENT_RANGE = 53
HTTP_QUERY_ETAG = 54
HTTP_QUERY_HOST = 55
HTTP_QUERY_IF_MATCH = 56
HTTP_QUERY_IF_NONE_MATCH = 57
HTTP_QUERY_IF_RANGE = 58
HTTP_QUERY_IF_UNMODIFIED_SINCE = 59
HTTP_QUERY_MAX_FORWARDS = 60
HTTP_QUERY_PROXY_AUTHORIZATION = 61
HTTP_QUERY_RANGE = 62
HTTP_QUERY_TRANSFER_ENCODING = 63
HTTP_QUERY_UPGRADE = 64
HTTP_QUERY_VARY = 65
HTTP_QUERY_VIA = 66
HTTP_QUERY_WARNING = 67
HTTP_QUERY_EXPECT = 68
HTTP_QUERY_PROXY_CONNECTION = 69
HTTP_QUERY_UNLESS_MODIFIED_SINCE = 70
HTTP_QUERY_ECHO_REQUEST = 71
HTTP_QUERY_ECHO_REPLY = 72
HTTP_QUERY_ECHO_HEADERS = 73
HTTP_QUERY_ECHO_HEADERS_CRLF = 74
HTTP_QUERY_PROXY_SUPPORT = 75
HTTP_QUERY_AUTHENTICATION_INFO = 76
HTTP_QUERY_PASSPORT_URLS = 77
HTTP_QUERY_PASSPORT_CONFIG = 78
HTTP_QUERY_MAX = 78
HTTP_QUERY_CUSTOM = 65535
HTTP_QUERY_FLAG_REQUEST_HEADERS = -2147483648
HTTP_QUERY_FLAG_SYSTEMTIME = 0x40000000
HTTP_QUERY_FLAG_NUMBER = 0x20000000
HTTP_QUERY_FLAG_COALESCE = 0x10000000
HTTP_QUERY_MODIFIER_FLAGS_MASK = (
    HTTP_QUERY_FLAG_REQUEST_HEADERS
    | HTTP_QUERY_FLAG_SYSTEMTIME
    | HTTP_QUERY_FLAG_NUMBER
    | HTTP_QUERY_FLAG_COALESCE
)
HTTP_QUERY_HEADER_MASK = ~HTTP_QUERY_MODIFIER_FLAGS_MASK
HTTP_STATUS_CONTINUE = 100
HTTP_STATUS_SWITCH_PROTOCOLS = 101
HTTP_STATUS_OK = 200
HTTP_STATUS_CREATED = 201
HTTP_STATUS_ACCEPTED = 202
HTTP_STATUS_PARTIAL = 203
HTTP_STATUS_NO_CONTENT = 204
HTTP_STATUS_RESET_CONTENT = 205
HTTP_STATUS_PARTIAL_CONTENT = 206
HTTP_STATUS_WEBDAV_MULTI_STATUS = 207
HTTP_STATUS_AMBIGUOUS = 300
HTTP_STATUS_MOVED = 301
HTTP_STATUS_REDIRECT = 302
HTTP_STATUS_REDIRECT_METHOD = 303
HTTP_STATUS_NOT_MODIFIED = 304
HTTP_STATUS_USE_PROXY = 305
HTTP_STATUS_REDIRECT_KEEP_VERB = 307
HTTP_STATUS_BAD_REQUEST = 400
HTTP_STATUS_DENIED = 401
HTTP_STATUS_PAYMENT_REQ = 402
HTTP_STATUS_FORBIDDEN = 403
HTTP_STATUS_NOT_FOUND = 404
HTTP_STATUS_BAD_METHOD = 405
HTTP_STATUS_NONE_ACCEPTABLE = 406
HTTP_STATUS_PROXY_AUTH_REQ = 407
HTTP_STATUS_REQUEST_TIMEOUT = 408
HTTP_STATUS_CONFLICT = 409
HTTP_STATUS_GONE = 410
HTTP_STATUS_LENGTH_REQUIRED = 411
HTTP_STATUS_PRECOND_FAILED = 412
HTTP_STATUS_REQUEST_TOO_LARGE = 413
HTTP_STATUS_URI_TOO_LONG = 414
HTTP_STATUS_UNSUPPORTED_MEDIA = 415
HTTP_STATUS_RETRY_WITH = 449
HTTP_STATUS_SERVER_ERROR = 500
HTTP_STATUS_NOT_SUPPORTED = 501
HTTP_STATUS_BAD_GATEWAY = 502
HTTP_STATUS_SERVICE_UNAVAIL = 503
HTTP_STATUS_GATEWAY_TIMEOUT = 504
HTTP_STATUS_VERSION_NOT_SUP = 505
HTTP_STATUS_FIRST = HTTP_STATUS_CONTINUE
HTTP_STATUS_LAST = HTTP_STATUS_VERSION_NOT_SUP
HTTP_ADDREQ_INDEX_MASK = 0x0000FFFF
HTTP_ADDREQ_FLAGS_MASK = -65536
HTTP_ADDREQ_FLAG_ADD_IF_NEW = 0x10000000
HTTP_ADDREQ_FLAG_ADD = 0x20000000
HTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA = 0x40000000
HTTP_ADDREQ_FLAG_COALESCE_WITH_SEMICOLON = 0x01000000
HTTP_ADDREQ_FLAG_COALESCE = HTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA
HTTP_ADDREQ_FLAG_REPLACE = -2147483648
HSR_ASYNC = WININET_API_FLAG_ASYNC
HSR_SYNC = WININET_API_FLAG_SYNC
HSR_USE_CONTEXT = WININET_API_FLAG_USE_CONTEXT
HSR_INITIATE = 0x00000008
HSR_DOWNLOAD = 0x00000010
HSR_CHUNKED = 0x00000020
INTERNET_COOKIE_IS_SECURE = 0x01
INTERNET_COOKIE_IS_SESSION = 0x02
INTERNET_COOKIE_THIRD_PARTY = 0x10
INTERNET_COOKIE_PROMPT_REQUIRED = 0x20
INTERNET_COOKIE_EVALUATE_P3P = 0x40
INTERNET_COOKIE_APPLY_P3P = 0x80
INTERNET_COOKIE_P3P_ENABLED = 0x100
INTERNET_COOKIE_IS_RESTRICTED = 0x200
INTERNET_COOKIE_IE6 = 0x400
INTERNET_COOKIE_IS_LEGACY = 0x800
FLAG_ICC_FORCE_CONNECTION = 0x00000001
FLAGS_ERROR_UI_FILTER_FOR_ERRORS = 0x01
FLAGS_ERROR_UI_FLAGS_CHANGE_OPTIONS = 0x02
FLAGS_ERROR_UI_FLAGS_GENERATE_DATA = 0x04
FLAGS_ERROR_UI_FLAGS_NO_UI = 0x08
FLAGS_ERROR_UI_SERIALIZE_DIALOGS = 0x10
INTERNET_ERROR_BASE = 12000
ERROR_INTERNET_OUT_OF_HANDLES = INTERNET_ERROR_BASE + 1
ERROR_INTERNET_TIMEOUT = INTERNET_ERROR_BASE + 2
ERROR_INTERNET_EXTENDED_ERROR = INTERNET_ERROR_BASE + 3
ERROR_INTERNET_INTERNAL_ERROR = INTERNET_ERROR_BASE + 4
ERROR_INTERNET_INVALID_URL = INTERNET_ERROR_BASE + 5
ERROR_INTERNET_UNRECOGNIZED_SCHEME = INTERNET_ERROR_BASE + 6
ERROR_INTERNET_NAME_NOT_RESOLVED = INTERNET_ERROR_BASE + 7
ERROR_INTERNET_PROTOCOL_NOT_FOUND = INTERNET_ERROR_BASE + 8
ERROR_INTERNET_INVALID_OPTION = INTERNET_ERROR_BASE + 9
ERROR_INTERNET_BAD_OPTION_LENGTH = INTERNET_ERROR_BASE + 10
ERROR_INTERNET_OPTION_NOT_SETTABLE = INTERNET_ERROR_BASE + 11
ERROR_INTERNET_SHUTDOWN = INTERNET_ERROR_BASE + 12
ERROR_INTERNET_INCORRECT_USER_NAME = INTERNET_ERROR_BASE + 13
ERROR_INTERNET_INCORRECT_PASSWORD = INTERNET_ERROR_BASE + 14
ERROR_INTERNET_LOGIN_FAILURE = INTERNET_ERROR_BASE + 15
ERROR_INTERNET_INVALID_OPERATION = INTERNET_ERROR_BASE + 16
ERROR_INTERNET_OPERATION_CANCELLED = INTERNET_ERROR_BASE + 17
ERROR_INTERNET_INCORRECT_HANDLE_TYPE = INTERNET_ERROR_BASE + 18
ERROR_INTERNET_INCORRECT_HANDLE_STATE = INTERNET_ERROR_BASE + 19
ERROR_INTERNET_NOT_PROXY_REQUEST = INTERNET_ERROR_BASE + 20
ERROR_INTERNET_REGISTRY_VALUE_NOT_FOUND = INTERNET_ERROR_BASE + 21
ERROR_INTERNET_BAD_REGISTRY_PARAMETER = INTERNET_ERROR_BASE + 22
ERROR_INTERNET_NO_DIRECT_ACCESS = INTERNET_ERROR_BASE + 23
ERROR_INTERNET_NO_CONTEXT = INTERNET_ERROR_BASE + 24
ERROR_INTERNET_NO_CALLBACK = INTERNET_ERROR_BASE + 25
ERROR_INTERNET_REQUEST_PENDING = INTERNET_ERROR_BASE + 26
ERROR_INTERNET_INCORRECT_FORMAT = INTERNET_ERROR_BASE + 27
ERROR_INTERNET_ITEM_NOT_FOUND = INTERNET_ERROR_BASE + 28
ERROR_INTERNET_CANNOT_CONNECT = INTERNET_ERROR_BASE + 29
ERROR_INTERNET_CONNECTION_ABORTED = INTERNET_ERROR_BASE + 30
ERROR_INTERNET_CONNECTION_RESET = INTERNET_ERROR_BASE + 31
ERROR_INTERNET_FORCE_RETRY = INTERNET_ERROR_BASE + 32
ERROR_INTERNET_INVALID_PROXY_REQUEST = INTERNET_ERROR_BASE + 33
ERROR_INTERNET_NEED_UI = INTERNET_ERROR_BASE + 34
ERROR_INTERNET_HANDLE_EXISTS = INTERNET_ERROR_BASE + 36
ERROR_INTERNET_SEC_CERT_DATE_INVALID = INTERNET_ERROR_BASE + 37
ERROR_INTERNET_SEC_CERT_CN_INVALID = INTERNET_ERROR_BASE + 38
ERROR_INTERNET_HTTP_TO_HTTPS_ON_REDIR = INTERNET_ERROR_BASE + 39
ERROR_INTERNET_HTTPS_TO_HTTP_ON_REDIR = INTERNET_ERROR_BASE + 40
ERROR_INTERNET_MIXED_SECURITY = INTERNET_ERROR_BASE + 41
ERROR_INTERNET_CHG_POST_IS_NON_SECURE = INTERNET_ERROR_BASE + 42
ERROR_INTERNET_POST_IS_NON_SECURE = INTERNET_ERROR_BASE + 43
ERROR_INTERNET_CLIENT_AUTH_CERT_NEEDED = INTERNET_ERROR_BASE + 44
ERROR_INTERNET_INVALID_CA = INTERNET_ERROR_BASE + 45
ERROR_INTERNET_CLIENT_AUTH_NOT_SETUP = INTERNET_ERROR_BASE + 46
ERROR_INTERNET_ASYNC_THREAD_FAILED = INTERNET_ERROR_BASE + 47
ERROR_INTERNET_REDIRECT_SCHEME_CHANGE = INTERNET_ERROR_BASE + 48
ERROR_INTERNET_DIALOG_PENDING = INTERNET_ERROR_BASE + 49
ERROR_INTERNET_RETRY_DIALOG = INTERNET_ERROR_BASE + 50
ERROR_INTERNET_HTTPS_HTTP_SUBMIT_REDIR = INTERNET_ERROR_BASE + 52
ERROR_INTERNET_INSERT_CDROM = INTERNET_ERROR_BASE + 53
ERROR_INTERNET_FORTEZZA_LOGIN_NEEDED = INTERNET_ERROR_BASE + 54
ERROR_INTERNET_SEC_CERT_ERRORS = INTERNET_ERROR_BASE + 55
ERROR_INTERNET_SEC_CERT_NO_REV = INTERNET_ERROR_BASE + 56
ERROR_INTERNET_SEC_CERT_REV_FAILED = INTERNET_ERROR_BASE + 57
ERROR_FTP_TRANSFER_IN_PROGRESS = INTERNET_ERROR_BASE + 110
ERROR_FTP_DROPPED = INTERNET_ERROR_BASE + 111
ERROR_FTP_NO_PASSIVE_MODE = INTERNET_ERROR_BASE + 112
ERROR_GOPHER_PROTOCOL_ERROR = INTERNET_ERROR_BASE + 130
ERROR_GOPHER_NOT_FILE = INTERNET_ERROR_BASE + 131
ERROR_GOPHER_DATA_ERROR = INTERNET_ERROR_BASE + 132
ERROR_GOPHER_END_OF_DATA = INTERNET_ERROR_BASE + 133
ERROR_GOPHER_INVALID_LOCATOR = INTERNET_ERROR_BASE + 134
ERROR_GOPHER_INCORRECT_LOCATOR_TYPE = INTERNET_ERROR_BASE + 135
ERROR_GOPHER_NOT_GOPHER_PLUS = INTERNET_ERROR_BASE + 136
ERROR_GOPHER_ATTRIBUTE_NOT_FOUND = INTERNET_ERROR_BASE + 137
ERROR_GOPHER_UNKNOWN_LOCATOR = INTERNET_ERROR_BASE + 138
ERROR_HTTP_HEADER_NOT_FOUND = INTERNET_ERROR_BASE + 150
ERROR_HTTP_DOWNLEVEL_SERVER = INTERNET_ERROR_BASE + 151
ERROR_HTTP_INVALID_SERVER_RESPONSE = INTERNET_ERROR_BASE + 152
ERROR_HTTP_INVALID_HEADER = INTERNET_ERROR_BASE + 153
ERROR_HTTP_INVALID_QUERY_REQUEST = INTERNET_ERROR_BASE + 154
ERROR_HTTP_HEADER_ALREADY_EXISTS = INTERNET_ERROR_BASE + 155
ERROR_HTTP_REDIRECT_FAILED = INTERNET_ERROR_BASE + 156
ERROR_HTTP_NOT_REDIRECTED = INTERNET_ERROR_BASE + 160
ERROR_HTTP_COOKIE_NEEDS_CONFIRMATION = INTERNET_ERROR_BASE + 161
ERROR_HTTP_COOKIE_DECLINED = INTERNET_ERROR_BASE + 162
ERROR_HTTP_REDIRECT_NEEDS_CONFIRMATION = INTERNET_ERROR_BASE + 168
ERROR_INTERNET_SECURITY_CHANNEL_ERROR = INTERNET_ERROR_BASE + 157
ERROR_INTERNET_UNABLE_TO_CACHE_FILE = INTERNET_ERROR_BASE + 158
ERROR_INTERNET_TCPIP_NOT_INSTALLED = INTERNET_ERROR_BASE + 159
ERROR_INTERNET_DISCONNECTED = INTERNET_ERROR_BASE + 163
ERROR_INTERNET_SERVER_UNREACHABLE = INTERNET_ERROR_BASE + 164
ERROR_INTERNET_PROXY_SERVER_UNREACHABLE = INTERNET_ERROR_BASE + 165
ERROR_INTERNET_BAD_AUTO_PROXY_SCRIPT = INTERNET_ERROR_BASE + 166
ERROR_INTERNET_UNABLE_TO_DOWNLOAD_SCRIPT = INTERNET_ERROR_BASE + 167
ERROR_INTERNET_SEC_INVALID_CERT = INTERNET_ERROR_BASE + 169
ERROR_INTERNET_SEC_CERT_REVOKED = INTERNET_ERROR_BASE + 170
ERROR_INTERNET_FAILED_DUETOSECURITYCHECK = INTERNET_ERROR_BASE + 171
ERROR_INTERNET_NOT_INITIALIZED = INTERNET_ERROR_BASE + 172
ERROR_INTERNET_NEED_MSN_SSPI_PKG = INTERNET_ERROR_BASE + 173
ERROR_INTERNET_LOGIN_FAILURE_DISPLAY_ENTITY_BODY = INTERNET_ERROR_BASE + 174
INTERNET_ERROR_LAST = ERROR_INTERNET_LOGIN_FAILURE_DISPLAY_ENTITY_BODY
NORMAL_CACHE_ENTRY = 0x00000001
STICKY_CACHE_ENTRY = 0x00000004
EDITED_CACHE_ENTRY = 0x00000008
TRACK_OFFLINE_CACHE_ENTRY = 0x00000010
TRACK_ONLINE_CACHE_ENTRY = 0x00000020
SPARSE_CACHE_ENTRY = 0x00010000
COOKIE_CACHE_ENTRY = 0x00100000
URLHISTORY_CACHE_ENTRY = 0x00200000
URLCACHE_FIND_DEFAULT_FILTER = (
    NORMAL_CACHE_ENTRY
    | COOKIE_CACHE_ENTRY
    | URLHISTORY_CACHE_ENTRY
    | TRACK_OFFLINE_CACHE_ENTRY
    | TRACK_ONLINE_CACHE_ENTRY
    | STICKY_CACHE_ENTRY
)
CACHEGROUP_ATTRIBUTE_GET_ALL = -1
CACHEGROUP_ATTRIBUTE_BASIC = 0x00000001
CACHEGROUP_ATTRIBUTE_FLAG = 0x00000002
CACHEGROUP_ATTRIBUTE_TYPE = 0x00000004
CACHEGROUP_ATTRIBUTE_QUOTA = 0x00000008
CACHEGROUP_ATTRIBUTE_GROUPNAME = 0x00000010
CACHEGROUP_ATTRIBUTE_STORAGE = 0x00000020
CACHEGROUP_FLAG_NONPURGEABLE = 0x00000001
CACHEGROUP_FLAG_GIDONLY = 0x00000004
CACHEGROUP_FLAG_FLUSHURL_ONDELETE = 0x00000002
CACHEGROUP_SEARCH_ALL = 0x00000000
CACHEGROUP_SEARCH_BYURL = 0x00000001
CACHEGROUP_TYPE_INVALID = 0x00000001
CACHEGROUP_READWRITE_MASK = (
    CACHEGROUP_ATTRIBUTE_TYPE
    | CACHEGROUP_ATTRIBUTE_QUOTA
    | CACHEGROUP_ATTRIBUTE_GROUPNAME
    | CACHEGROUP_ATTRIBUTE_STORAGE
)
GROUPNAME_MAX_LENGTH = 120
GROUP_OWNER_STORAGE_SIZE = 4
CACHE_ENTRY_ATTRIBUTE_FC = 0x00000004
CACHE_ENTRY_HITRATE_FC = 0x00000010
CACHE_ENTRY_MODTIME_FC = 0x00000040
CACHE_ENTRY_EXPTIME_FC = 0x00000080
CACHE_ENTRY_ACCTIME_FC = 0x00000100
CACHE_ENTRY_SYNCTIME_FC = 0x00000200
CACHE_ENTRY_HEADERINFO_FC = 0x00000400
CACHE_ENTRY_EXEMPT_DELTA_FC = 0x00000800
INTERNET_CACHE_GROUP_ADD = 0
INTERNET_CACHE_GROUP_REMOVE = 1
INTERNET_DIAL_FORCE_PROMPT = 0x2000
INTERNET_DIAL_SHOW_OFFLINE = 0x4000
INTERNET_DIAL_UNATTENDED = 0x8000
INTERENT_GOONLINE_REFRESH = 0x00000001
INTERENT_GOONLINE_MASK = 0x00000001
INTERNET_AUTODIAL_FORCE_ONLINE = 1
INTERNET_AUTODIAL_FORCE_UNATTENDED = 2
INTERNET_AUTODIAL_FAILIFSECURITYCHECK = 4
INTERNET_AUTODIAL_OVERRIDE_NET_PRESENT = 8
INTERNET_AUTODIAL_FLAGS_MASK = (
    INTERNET_AUTODIAL_FORCE_ONLINE
    | INTERNET_AUTODIAL_FORCE_UNATTENDED
    | INTERNET_AUTODIAL_FAILIFSECURITYCHECK
    | INTERNET_AUTODIAL_OVERRIDE_NET_PRESENT
)
PROXY_AUTO_DETECT_TYPE_DHCP = 1
PROXY_AUTO_DETECT_TYPE_DNS_A = 2
INTERNET_CONNECTION_MODEM = 0x01
INTERNET_CONNECTION_LAN = 0x02
INTERNET_CONNECTION_PROXY = 0x04
INTERNET_CONNECTION_MODEM_BUSY = 0x08
INTERNET_RAS_INSTALLED = 0x10
INTERNET_CONNECTION_OFFLINE = 0x20
INTERNET_CONNECTION_CONFIGURED = 0x40
INTERNET_CUSTOMDIAL_CONNECT = 0
INTERNET_CUSTOMDIAL_UNATTENDED = 1
INTERNET_CUSTOMDIAL_DISCONNECT = 2
INTERNET_CUSTOMDIAL_SHOWOFFLINE = 4
INTERNET_CUSTOMDIAL_SAFE_FOR_UNATTENDED = 1
INTERNET_CUSTOMDIAL_WILL_SUPPLY_STATE = 2
INTERNET_CUSTOMDIAL_CAN_HANGUP = 4
INTERNET_DIALSTATE_DISCONNECTED = 1
INTERNET_IDENTITY_FLAG_PRIVATE_CACHE = 0x01
INTERNET_IDENTITY_FLAG_SHARED_CACHE = 0x02
INTERNET_IDENTITY_FLAG_CLEAR_DATA = 0x04
INTERNET_IDENTITY_FLAG_CLEAR_COOKIES = 0x08
INTERNET_IDENTITY_FLAG_CLEAR_HISTORY = 0x10
INTERNET_IDENTITY_FLAG_CLEAR_CONTENT = 0x20
INTERNET_SUPPRESS_RESET_ALL = 0x00
INTERNET_SUPPRESS_COOKIE_POLICY = 0x01
INTERNET_SUPPRESS_COOKIE_POLICY_RESET = 0x02
PRIVACY_TEMPLATE_NO_COOKIES = 0
PRIVACY_TEMPLATE_HIGH = 1
PRIVACY_TEMPLATE_MEDIUM_HIGH = 2
PRIVACY_TEMPLATE_MEDIUM = 3
PRIVACY_TEMPLATE_MEDIUM_LOW = 4
PRIVACY_TEMPLATE_LOW = 5
PRIVACY_TEMPLATE_CUSTOM = 100
PRIVACY_TEMPLATE_ADVANCED = 101
PRIVACY_TEMPLATE_MAX = PRIVACY_TEMPLATE_LOW
PRIVACY_TYPE_FIRST_PARTY = 0
PRIVACY_TYPE_THIRD_PARTY = 1

# Generated by h2py from winhttp.h
WINHTTP_FLAG_ASYNC = 0x10000000
WINHTTP_FLAG_SECURE = 0x00800000
WINHTTP_FLAG_ESCAPE_PERCENT = 0x00000004
WINHTTP_FLAG_NULL_CODEPAGE = 0x00000008
WINHTTP_FLAG_BYPASS_PROXY_CACHE = 0x00000100
WINHTTP_FLAG_REFRESH = WINHTTP_FLAG_BYPASS_PROXY_CACHE
WINHTTP_FLAG_ESCAPE_DISABLE = 0x00000040
WINHTTP_FLAG_ESCAPE_DISABLE_QUERY = 0x00000080
INTERNET_SCHEME_HTTP = 1
INTERNET_SCHEME_HTTPS = 2
WINHTTP_AUTOPROXY_AUTO_DETECT = 0x00000001
WINHTTP_AUTOPROXY_CONFIG_URL = 0x00000002
WINHTTP_AUTOPROXY_RUN_INPROCESS = 0x00010000
WINHTTP_AUTOPROXY_RUN_OUTPROCESS_ONLY = 0x00020000
WINHTTP_AUTO_DETECT_TYPE_DHCP = 0x00000001
WINHTTP_AUTO_DETECT_TYPE_DNS_A = 0x00000002
WINHTTP_TIME_FORMAT_BUFSIZE = 62
WINHTTP_ACCESS_TYPE_DEFAULT_PROXY = 0
WINHTTP_ACCESS_TYPE_NO_PROXY = 1
WINHTTP_ACCESS_TYPE_NAMED_PROXY = 3
WINHTTP_OPTION_CALLBACK = 1
WINHTTP_OPTION_RESOLVE_TIMEOUT = 2
WINHTTP_OPTION_CONNECT_TIMEOUT = 3
WINHTTP_OPTION_CONNECT_RETRIES = 4
WINHTTP_OPTION_SEND_TIMEOUT = 5
WINHTTP_OPTION_RECEIVE_TIMEOUT = 6
WINHTTP_OPTION_RECEIVE_RESPONSE_TIMEOUT = 7
WINHTTP_OPTION_HANDLE_TYPE = 9
WINHTTP_OPTION_READ_BUFFER_SIZE = 12
WINHTTP_OPTION_WRITE_BUFFER_SIZE = 13
WINHTTP_OPTION_PARENT_HANDLE = 21
WINHTTP_OPTION_EXTENDED_ERROR = 24
WINHTTP_OPTION_SECURITY_FLAGS = 31
WINHTTP_OPTION_SECURITY_CERTIFICATE_STRUCT = 32
WINHTTP_OPTION_URL = 34
WINHTTP_OPTION_SECURITY_KEY_BITNESS = 36
WINHTTP_OPTION_PROXY = 38
WINHTTP_OPTION_USER_AGENT = 41
WINHTTP_OPTION_CONTEXT_VALUE = 45
WINHTTP_OPTION_CLIENT_CERT_CONTEXT = 47
WINHTTP_OPTION_REQUEST_PRIORITY = 58
WINHTTP_OPTION_HTTP_VERSION = 59
WINHTTP_OPTION_DISABLE_FEATURE = 63
WINHTTP_OPTION_CODEPAGE = 68
WINHTTP_OPTION_MAX_CONNS_PER_SERVER = 73
WINHTTP_OPTION_MAX_CONNS_PER_1_0_SERVER = 74
WINHTTP_OPTION_AUTOLOGON_POLICY = 77
WINHTTP_OPTION_SERVER_CERT_CONTEXT = 78
WINHTTP_OPTION_ENABLE_FEATURE = 79
WINHTTP_OPTION_WORKER_THREAD_COUNT = 80
WINHTTP_OPTION_PASSPORT_COBRANDING_TEXT = 81
WINHTTP_OPTION_PASSPORT_COBRANDING_URL = 82
WINHTTP_OPTION_CONFIGURE_PASSPORT_AUTH = 83
WINHTTP_OPTION_SECURE_PROTOCOLS = 84
WINHTTP_OPTION_ENABLETRACING = 85
WINHTTP_OPTION_PASSPORT_SIGN_OUT = 86
WINHTTP_OPTION_PASSPORT_RETURN_URL = 87
WINHTTP_OPTION_REDIRECT_POLICY = 88
WINHTTP_OPTION_MAX_HTTP_AUTOMATIC_REDIRECTS = 89
WINHTTP_OPTION_MAX_HTTP_STATUS_CONTINUE = 90
WINHTTP_OPTION_MAX_RESPONSE_HEADER_SIZE = 91
WINHTTP_OPTION_MAX_RESPONSE_DRAIN_SIZE = 92
WINHTTP_OPTION_CONNECTION_INFO = 93
WINHTTP_OPTION_CLIENT_CERT_ISSUER_LIST = 94
WINHTTP_OPTION_SPN = 96
WINHTTP_OPTION_GLOBAL_PROXY_CREDS = 97
WINHTTP_OPTION_GLOBAL_SERVER_CREDS = 98
WINHTTP_OPTION_UNLOAD_NOTIFY_EVENT = 99
WINHTTP_OPTION_REJECT_USERPWD_IN_URL = 100
WINHTTP_OPTION_USE_GLOBAL_SERVER_CREDENTIALS = 101
WINHTTP_LAST_OPTION = WINHTTP_OPTION_USE_GLOBAL_SERVER_CREDENTIALS
WINHTTP_OPTION_USERNAME = 0x1000
WINHTTP_OPTION_PASSWORD = 0x1001
WINHTTP_OPTION_PROXY_USERNAME = 0x1002
WINHTTP_OPTION_PROXY_PASSWORD = 0x1003
WINHTTP_CONNS_PER_SERVER_UNLIMITED = -1
WINHTTP_AUTOLOGON_SECURITY_LEVEL_MEDIUM = 0
WINHTTP_AUTOLOGON_SECURITY_LEVEL_LOW = 1
WINHTTP_AUTOLOGON_SECURITY_LEVEL_HIGH = 2
WINHTTP_AUTOLOGON_SECURITY_LEVEL_DEFAULT = WINHTTP_AUTOLOGON_SECURITY_LEVEL_MEDIUM
WINHTTP_OPTION_REDIRECT_POLICY_NEVER = 0
WINHTTP_OPTION_REDIRECT_POLICY_DISALLOW_HTTPS_TO_HTTP = 1
WINHTTP_OPTION_REDIRECT_POLICY_ALWAYS = 2
WINHTTP_OPTION_REDIRECT_POLICY_LAST = WINHTTP_OPTION_REDIRECT_POLICY_ALWAYS
WINHTTP_OPTION_REDIRECT_POLICY_DEFAULT = (
    WINHTTP_OPTION_REDIRECT_POLICY_DISALLOW_HTTPS_TO_HTTP
)
WINHTTP_DISABLE_PASSPORT_AUTH = 0x00000000
WINHTTP_ENABLE_PASSPORT_AUTH = 0x10000000
WINHTTP_DISABLE_PASSPORT_KEYRING = 0x20000000
WINHTTP_ENABLE_PASSPORT_KEYRING = 0x40000000
WINHTTP_DISABLE_COOKIES = 0x00000001
WINHTTP_DISABLE_REDIRECTS = 0x00000002
WINHTTP_DISABLE_AUTHENTICATION = 0x00000004
WINHTTP_DISABLE_KEEP_ALIVE = 0x00000008
WINHTTP_ENABLE_SSL_REVOCATION = 0x00000001
WINHTTP_ENABLE_SSL_REVERT_IMPERSONATION = 0x00000002
WINHTTP_DISABLE_SPN_SERVER_PORT = 0x00000000
WINHTTP_ENABLE_SPN_SERVER_PORT = 0x00000001
WINHTTP_OPTION_SPN_MASK = WINHTTP_ENABLE_SPN_SERVER_PORT
WINHTTP_HANDLE_TYPE_SESSION = 1
WINHTTP_HANDLE_TYPE_CONNECT = 2
WINHTTP_HANDLE_TYPE_REQUEST = 3
WINHTTP_AUTH_SCHEME_BASIC = 0x00000001
WINHTTP_AUTH_SCHEME_NTLM = 0x00000002
WINHTTP_AUTH_SCHEME_PASSPORT = 0x00000004
WINHTTP_AUTH_SCHEME_DIGEST = 0x00000008
WINHTTP_AUTH_SCHEME_NEGOTIATE = 0x00000010
WINHTTP_AUTH_TARGET_SERVER = 0x00000000
WINHTTP_AUTH_TARGET_PROXY = 0x00000001
WINHTTP_CALLBACK_STATUS_FLAG_CERT_REV_FAILED = 0x00000001
WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CERT = 0x00000002
WINHTTP_CALLBACK_STATUS_FLAG_CERT_REVOKED = 0x00000004
WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CA = 0x00000008
WINHTTP_CALLBACK_STATUS_FLAG_CERT_CN_INVALID = 0x00000010
WINHTTP_CALLBACK_STATUS_FLAG_CERT_DATE_INVALID = 0x00000020
WINHTTP_CALLBACK_STATUS_FLAG_CERT_WRONG_USAGE = 0x00000040
WINHTTP_CALLBACK_STATUS_FLAG_SECURITY_CHANNEL_ERROR = -2147483648
WINHTTP_FLAG_SECURE_PROTOCOL_SSL2 = 0x00000008
WINHTTP_FLAG_SECURE_PROTOCOL_SSL3 = 0x00000020
WINHTTP_FLAG_SECURE_PROTOCOL_TLS1 = 0x00000080
WINHTTP_FLAG_SECURE_PROTOCOL_ALL = (
    WINHTTP_FLAG_SECURE_PROTOCOL_SSL2
    | WINHTTP_FLAG_SECURE_PROTOCOL_SSL3
    | WINHTTP_FLAG_SECURE_PROTOCOL_TLS1
)
WINHTTP_CALLBACK_STATUS_RESOLVING_NAME = 0x00000001
WINHTTP_CALLBACK_STATUS_NAME_RESOLVED = 0x00000002
WINHTTP_CALLBACK_STATUS_CONNECTING_TO_SERVER = 0x00000004
WINHTTP_CALLBACK_STATUS_CONNECTED_TO_SERVER = 0x00000008
WINHTTP_CALLBACK_STATUS_SENDING_REQUEST = 0x00000010
WINHTTP_CALLBACK_STATUS_REQUEST_SENT = 0x00000020
WINHTTP_CALLBACK_STATUS_RECEIVING_RESPONSE = 0x00000040
WINHTTP_CALLBACK_STATUS_RESPONSE_RECEIVED = 0x00000080
WINHTTP_CALLBACK_STATUS_CLOSING_CONNECTION = 0x00000100
WINHTTP_CALLBACK_STATUS_CONNECTION_CLOSED = 0x00000200
WINHTTP_CALLBACK_STATUS_HANDLE_CREATED = 0x00000400
WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING = 0x00000800
WINHTTP_CALLBACK_STATUS_DETECTING_PROXY = 0x00001000
WINHTTP_CALLBACK_STATUS_REDIRECT = 0x00004000
WINHTTP_CALLBACK_STATUS_INTERMEDIATE_RESPONSE = 0x00008000
WINHTTP_CALLBACK_STATUS_SECURE_FAILURE = 0x00010000
WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE = 0x00020000
WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE = 0x00040000
WINHTTP_CALLBACK_STATUS_READ_COMPLETE = 0x00080000
WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE = 0x00100000
WINHTTP_CALLBACK_STATUS_REQUEST_ERROR = 0x00200000
WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE = 0x00400000
API_RECEIVE_RESPONSE = 1
API_QUERY_DATA_AVAILABLE = 2
API_READ_DATA = 3
API_WRITE_DATA = 4
API_SEND_REQUEST = 5
WINHTTP_CALLBACK_FLAG_RESOLVE_NAME = (
    WINHTTP_CALLBACK_STATUS_RESOLVING_NAME | WINHTTP_CALLBACK_STATUS_NAME_RESOLVED
)
WINHTTP_CALLBACK_FLAG_CONNECT_TO_SERVER = (
    WINHTTP_CALLBACK_STATUS_CONNECTING_TO_SERVER
    | WINHTTP_CALLBACK_STATUS_CONNECTED_TO_SERVER
)
WINHTTP_CALLBACK_FLAG_SEND_REQUEST = (
    WINHTTP_CALLBACK_STATUS_SENDING_REQUEST | WINHTTP_CALLBACK_STATUS_REQUEST_SENT
)
WINHTTP_CALLBACK_FLAG_RECEIVE_RESPONSE = (
    WINHTTP_CALLBACK_STATUS_RECEIVING_RESPONSE
    | WINHTTP_CALLBACK_STATUS_RESPONSE_RECEIVED
)
WINHTTP_CALLBACK_FLAG_CLOSE_CONNECTION = (
    WINHTTP_CALLBACK_STATUS_CLOSING_CONNECTION
    | WINHTTP_CALLBACK_STATUS_CONNECTION_CLOSED
)
WINHTTP_CALLBACK_FLAG_HANDLES = (
    WINHTTP_CALLBACK_STATUS_HANDLE_CREATED | WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING
)
WINHTTP_CALLBACK_FLAG_DETECTING_PROXY = WINHTTP_CALLBACK_STATUS_DETECTING_PROXY
WINHTTP_CALLBACK_FLAG_REDIRECT = WINHTTP_CALLBACK_STATUS_REDIRECT
WINHTTP_CALLBACK_FLAG_INTERMEDIATE_RESPONSE = (
    WINHTTP_CALLBACK_STATUS_INTERMEDIATE_RESPONSE
)
WINHTTP_CALLBACK_FLAG_SECURE_FAILURE = WINHTTP_CALLBACK_STATUS_SECURE_FAILURE
WINHTTP_CALLBACK_FLAG_SENDREQUEST_COMPLETE = (
    WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE
)
WINHTTP_CALLBACK_FLAG_HEADERS_AVAILABLE = WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE
WINHTTP_CALLBACK_FLAG_DATA_AVAILABLE = WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE
WINHTTP_CALLBACK_FLAG_READ_COMPLETE = WINHTTP_CALLBACK_STATUS_READ_COMPLETE
WINHTTP_CALLBACK_FLAG_WRITE_COMPLETE = WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE
WINHTTP_CALLBACK_FLAG_REQUEST_ERROR = WINHTTP_CALLBACK_STATUS_REQUEST_ERROR
WINHTTP_CALLBACK_FLAG_ALL_COMPLETIONS = (
    WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE
    | WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE
    | WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE
    | WINHTTP_CALLBACK_STATUS_READ_COMPLETE
    | WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE
    | WINHTTP_CALLBACK_STATUS_REQUEST_ERROR
)
WINHTTP_CALLBACK_FLAG_ALL_NOTIFICATIONS = -1
WINHTTP_QUERY_MIME_VERSION = 0
WINHTTP_QUERY_CONTENT_TYPE = 1
WINHTTP_QUERY_CONTENT_TRANSFER_ENCODING = 2
WINHTTP_QUERY_CONTENT_ID = 3
WINHTTP_QUERY_CONTENT_DESCRIPTION = 4
WINHTTP_QUERY_CONTENT_LENGTH = 5
WINHTTP_QUERY_CONTENT_LANGUAGE = 6
WINHTTP_QUERY_ALLOW = 7
WINHTTP_QUERY_PUBLIC = 8
WINHTTP_QUERY_DATE = 9
WINHTTP_QUERY_EXPIRES = 10
WINHTTP_QUERY_LAST_MODIFIED = 11
WINHTTP_QUERY_MESSAGE_ID = 12
WINHTTP_QUERY_URI = 13
WINHTTP_QUERY_DERIVED_FROM = 14
WINHTTP_QUERY_COST = 15
WINHTTP_QUERY_LINK = 16
WINHTTP_QUERY_PRAGMA = 17
WINHTTP_QUERY_VERSION = 18
WINHTTP_QUERY_STATUS_CODE = 19
WINHTTP_QUERY_STATUS_TEXT = 20
WINHTTP_QUERY_RAW_HEADERS = 21
WINHTTP_QUERY_RAW_HEADERS_CRLF = 22
WINHTTP_QUERY_CONNECTION = 23
WINHTTP_QUERY_ACCEPT = 24
WINHTTP_QUERY_ACCEPT_CHARSET = 25
WINHTTP_QUERY_ACCEPT_ENCODING = 26
WINHTTP_QUERY_ACCEPT_LANGUAGE = 27
WINHTTP_QUERY_AUTHORIZATION = 28
WINHTTP_QUERY_CONTENT_ENCODING = 29
WINHTTP_QUERY_FORWARDED = 30
WINHTTP_QUERY_FROM = 31
WINHTTP_QUERY_IF_MODIFIED_SINCE = 32
WINHTTP_QUERY_LOCATION = 33
WINHTTP_QUERY_ORIG_URI = 34
WINHTTP_QUERY_REFERER = 35
WINHTTP_QUERY_RETRY_AFTER = 36
WINHTTP_QUERY_SERVER = 37
WINHTTP_QUERY_TITLE = 38
WINHTTP_QUERY_USER_AGENT = 39
WINHTTP_QUERY_WWW_AUTHENTICATE = 40
WINHTTP_QUERY_PROXY_AUTHENTICATE = 41
WINHTTP_QUERY_ACCEPT_RANGES = 42
WINHTTP_QUERY_SET_COOKIE = 43
WINHTTP_QUERY_COOKIE = 44
WINHTTP_QUERY_REQUEST_METHOD = 45
WINHTTP_QUERY_REFRESH = 46
WINHTTP_QUERY_CONTENT_DISPOSITION = 47
WINHTTP_QUERY_AGE = 48
WINHTTP_QUERY_CACHE_CONTROL = 49
WINHTTP_QUERY_CONTENT_BASE = 50
WINHTTP_QUERY_CONTENT_LOCATION = 51
WINHTTP_QUERY_CONTENT_MD5 = 52
WINHTTP_QUERY_CONTENT_RANGE = 53
WINHTTP_QUERY_ETAG = 54
WINHTTP_QUERY_HOST = 55
WINHTTP_QUERY_IF_MATCH = 56
WINHTTP_QUERY_IF_NONE_MATCH = 57
WINHTTP_QUERY_IF_RANGE = 58
WINHTTP_QUERY_IF_UNMODIFIED_SINCE = 59
WINHTTP_QUERY_MAX_FORWARDS = 60
WINHTTP_QUERY_PROXY_AUTHORIZATION = 61
WINHTTP_QUERY_RANGE = 62
WINHTTP_QUERY_TRANSFER_ENCODING = 63
WINHTTP_QUERY_UPGRADE = 64
WINHTTP_QUERY_VARY = 65
WINHTTP_QUERY_VIA = 66
WINHTTP_QUERY_WARNING = 67
WINHTTP_QUERY_EXPECT = 68
WINHTTP_QUERY_PROXY_CONNECTION = 69
WINHTTP_QUERY_UNLESS_MODIFIED_SINCE = 70
WINHTTP_QUERY_PROXY_SUPPORT = 75
WINHTTP_QUERY_AUTHENTICATION_INFO = 76
WINHTTP_QUERY_PASSPORT_URLS = 77
WINHTTP_QUERY_PASSPORT_CONFIG = 78
WINHTTP_QUERY_MAX = 78
WINHTTP_QUERY_CUSTOM = 65535
WINHTTP_QUERY_FLAG_REQUEST_HEADERS = -2147483648
WINHTTP_QUERY_FLAG_SYSTEMTIME = 0x40000000
WINHTTP_QUERY_FLAG_NUMBER = 0x20000000
WINHTTP_ADDREQ_INDEX_MASK = 0x0000FFFF
WINHTTP_ADDREQ_FLAGS_MASK = -65536
WINHTTP_ADDREQ_FLAG_ADD_IF_NEW = 0x10000000
WINHTTP_ADDREQ_FLAG_ADD = 0x20000000
WINHTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA = 0x40000000
WINHTTP_ADDREQ_FLAG_COALESCE_WITH_SEMICOLON = 0x01000000
WINHTTP_ADDREQ_FLAG_COALESCE = WINHTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA
WINHTTP_ADDREQ_FLAG_REPLACE = -2147483648
WINHTTP_IGNORE_REQUEST_TOTAL_LENGTH = 0
WINHTTP_ERROR_BASE = 12000
ERROR_WINHTTP_OUT_OF_HANDLES = WINHTTP_ERROR_BASE + 1
ERROR_WINHTTP_TIMEOUT = WINHTTP_ERROR_BASE + 2
ERROR_WINHTTP_INTERNAL_ERROR = WINHTTP_ERROR_BASE + 4
ERROR_WINHTTP_INVALID_URL = WINHTTP_ERROR_BASE + 5
ERROR_WINHTTP_UNRECOGNIZED_SCHEME = WINHTTP_ERROR_BASE + 6
ERROR_WINHTTP_NAME_NOT_RESOLVED = WINHTTP_ERROR_BASE + 7
ERROR_WINHTTP_INVALID_OPTION = WINHTTP_ERROR_BASE + 9
ERROR_WINHTTP_OPTION_NOT_SETTABLE = WINHTTP_ERROR_BASE + 11
ERROR_WINHTTP_SHUTDOWN = WINHTTP_ERROR_BASE + 12
ERROR_WINHTTP_LOGIN_FAILURE = WINHTTP_ERROR_BASE + 15
ERROR_WINHTTP_OPERATION_CANCELLED = WINHTTP_ERROR_BASE + 17
ERROR_WINHTTP_INCORRECT_HANDLE_TYPE = WINHTTP_ERROR_BASE + 18
ERROR_WINHTTP_INCORRECT_HANDLE_STATE = WINHTTP_ERROR_BASE + 19
ERROR_WINHTTP_CANNOT_CONNECT = WINHTTP_ERROR_BASE + 29
ERROR_WINHTTP_CONNECTION_ERROR = WINHTTP_ERROR_BASE + 30
ERROR_WINHTTP_RESEND_REQUEST = WINHTTP_ERROR_BASE + 32
ERROR_WINHTTP_CLIENT_AUTH_CERT_NEEDED = WINHTTP_ERROR_BASE + 44
ERROR_WINHTTP_CANNOT_CALL_BEFORE_OPEN = WINHTTP_ERROR_BASE + 100
ERROR_WINHTTP_CANNOT_CALL_BEFORE_SEND = WINHTTP_ERROR_BASE + 101
ERROR_WINHTTP_CANNOT_CALL_AFTER_SEND = WINHTTP_ERROR_BASE + 102
ERROR_WINHTTP_CANNOT_CALL_AFTER_OPEN = WINHTTP_ERROR_BASE + 103
ERROR_WINHTTP_HEADER_NOT_FOUND = WINHTTP_ERROR_BASE + 150
ERROR_WINHTTP_INVALID_SERVER_RESPONSE = WINHTTP_ERROR_BASE + 152
ERROR_WINHTTP_INVALID_HEADER = WINHTTP_ERROR_BASE + 153
ERROR_WINHTTP_INVALID_QUERY_REQUEST = WINHTTP_ERROR_BASE + 154
ERROR_WINHTTP_HEADER_ALREADY_EXISTS = WINHTTP_ERROR_BASE + 155
ERROR_WINHTTP_REDIRECT_FAILED = WINHTTP_ERROR_BASE + 156
ERROR_WINHTTP_AUTO_PROXY_SERVICE_ERROR = WINHTTP_ERROR_BASE + 178
ERROR_WINHTTP_BAD_AUTO_PROXY_SCRIPT = WINHTTP_ERROR_BASE + 166
ERROR_WINHTTP_UNABLE_TO_DOWNLOAD_SCRIPT = WINHTTP_ERROR_BASE + 167
ERROR_WINHTTP_NOT_INITIALIZED = WINHTTP_ERROR_BASE + 172
ERROR_WINHTTP_SECURE_FAILURE = WINHTTP_ERROR_BASE + 175
ERROR_WINHTTP_SECURE_CERT_DATE_INVALID = WINHTTP_ERROR_BASE + 37
ERROR_WINHTTP_SECURE_CERT_CN_INVALID = WINHTTP_ERROR_BASE + 38
ERROR_WINHTTP_SECURE_INVALID_CA = WINHTTP_ERROR_BASE + 45
ERROR_WINHTTP_SECURE_CERT_REV_FAILED = WINHTTP_ERROR_BASE + 57
ERROR_WINHTTP_SECURE_CHANNEL_ERROR = WINHTTP_ERROR_BASE + 157
ERROR_WINHTTP_SECURE_INVALID_CERT = WINHTTP_ERROR_BASE + 169
ERROR_WINHTTP_SECURE_CERT_REVOKED = WINHTTP_ERROR_BASE + 170
ERROR_WINHTTP_SECURE_CERT_WRONG_USAGE = WINHTTP_ERROR_BASE + 179
ERROR_WINHTTP_AUTODETECTION_FAILED = WINHTTP_ERROR_BASE + 180
ERROR_WINHTTP_HEADER_COUNT_EXCEEDED = WINHTTP_ERROR_BASE + 181
ERROR_WINHTTP_HEADER_SIZE_OVERFLOW = WINHTTP_ERROR_BASE + 182
ERROR_WINHTTP_CHUNKED_ENCODING_HEADER_SIZE_OVERFLOW = WINHTTP_ERROR_BASE + 183
ERROR_WINHTTP_RESPONSE_DRAIN_OVERFLOW = WINHTTP_ERROR_BASE + 184
ERROR_WINHTTP_CLIENT_CERT_NO_PRIVATE_KEY = WINHTTP_ERROR_BASE + 185
ERROR_WINHTTP_CLIENT_CERT_NO_ACCESS_PRIVATE_KEY = WINHTTP_ERROR_BASE + 186
WINHTTP_ERROR_LAST = WINHTTP_ERROR_BASE + 186

WINHTTP_NO_PROXY_NAME = None
WINHTTP_NO_PROXY_BYPASS = None
WINHTTP_NO_REFERER = None
WINHTTP_DEFAULT_ACCEPT_TYPES = None
WINHTTP_NO_ADDITIONAL_HEADERS = None
WINHTTP_NO_REQUEST_DATA = None
