# Generated by h2py from WinCrypt.h
def GET_ALG_CLASS(x):
    return x & (7 << 13)


def GET_ALG_TYPE(x):
    return x & (15 << 9)


def GET_ALG_SID(x):
    return x & (511)


ALG_CLASS_ANY = 0
ALG_CLASS_SIGNATURE = 1 << 13
ALG_CLASS_MSG_ENCRYPT = 2 << 13
ALG_CLASS_DATA_ENCRYPT = 3 << 13
ALG_CLASS_HASH = 4 << 13
ALG_CLASS_KEY_EXCHANGE = 5 << 13
ALG_CLASS_ALL = 7 << 13
ALG_TYPE_ANY = 0
ALG_TYPE_DSS = 1 << 9
ALG_TYPE_RSA = 2 << 9
ALG_TYPE_BLOCK = 3 << 9
ALG_TYPE_STREAM = 4 << 9
ALG_TYPE_DH = 5 << 9
ALG_TYPE_SECURECHANNEL = 6 << 9
ALG_SID_ANY = 0
ALG_SID_RSA_ANY = 0
ALG_SID_RSA_PKCS = 1
ALG_SID_RSA_MSATWORK = 2
ALG_SID_RSA_ENTRUST = 3
ALG_SID_RSA_PGP = 4
ALG_SID_DSS_ANY = 0
ALG_SID_DSS_PKCS = 1
ALG_SID_DSS_DMS = 2
ALG_SID_DES = 1
ALG_SID_3DES = 3
ALG_SID_DESX = 4
ALG_SID_IDEA = 5
ALG_SID_CAST = 6
ALG_SID_SAFERSK64 = 7
ALG_SID_SAFERSK128 = 8
ALG_SID_3DES_112 = 9
ALG_SID_CYLINK_MEK = 12
ALG_SID_RC5 = 13
ALG_SID_AES_128 = 14
ALG_SID_AES_192 = 15
ALG_SID_AES_256 = 16
ALG_SID_AES = 17
ALG_SID_SKIPJACK = 10
ALG_SID_TEK = 11
CRYPT_MODE_CBCI = 6
CRYPT_MODE_CFBP = 7
CRYPT_MODE_OFBP = 8
CRYPT_MODE_CBCOFM = 9
CRYPT_MODE_CBCOFMI = 10
ALG_SID_RC2 = 2
ALG_SID_RC4 = 1
ALG_SID_SEAL = 2
ALG_SID_DH_SANDF = 1
ALG_SID_DH_EPHEM = 2
ALG_SID_AGREED_KEY_ANY = 3
ALG_SID_KEA = 4
ALG_SID_MD2 = 1
ALG_SID_MD4 = 2
ALG_SID_MD5 = 3
ALG_SID_SHA = 4
ALG_SID_SHA1 = 4
ALG_SID_MAC = 5
ALG_SID_RIPEMD = 6
ALG_SID_RIPEMD160 = 7
ALG_SID_SSL3SHAMD5 = 8
ALG_SID_HMAC = 9
ALG_SID_TLS1PRF = 10
ALG_SID_HASH_REPLACE_OWF = 11
ALG_SID_SHA_256 = 12
ALG_SID_SHA_384 = 13
ALG_SID_SHA_512 = 14
ALG_SID_SSL3_MASTER = 1
ALG_SID_SCHANNEL_MASTER_HASH = 2
ALG_SID_SCHANNEL_MAC_KEY = 3
ALG_SID_PCT1_MASTER = 4
ALG_SID_SSL2_MASTER = 5
ALG_SID_TLS1_MASTER = 6
ALG_SID_SCHANNEL_ENC_KEY = 7
ALG_SID_EXAMPLE = 80
CALG_MD2 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_MD2
CALG_MD4 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_MD4
CALG_MD5 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_MD5
CALG_SHA = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_SHA
CALG_SHA1 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_SHA1
CALG_MAC = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_MAC
CALG_RSA_SIGN = ALG_CLASS_SIGNATURE | ALG_TYPE_RSA | ALG_SID_RSA_ANY
CALG_DSS_SIGN = ALG_CLASS_SIGNATURE | ALG_TYPE_DSS | ALG_SID_DSS_ANY
CALG_NO_SIGN = ALG_CLASS_SIGNATURE | ALG_TYPE_ANY | ALG_SID_ANY
CALG_RSA_KEYX = ALG_CLASS_KEY_EXCHANGE | ALG_TYPE_RSA | ALG_SID_RSA_ANY
CALG_DES = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_DES
CALG_3DES_112 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_3DES_112
CALG_3DES = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_3DES
CALG_DESX = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_DESX
CALG_RC2 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_RC2
CALG_RC4 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_STREAM | ALG_SID_RC4
CALG_SEAL = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_STREAM | ALG_SID_SEAL
CALG_DH_SF = ALG_CLASS_KEY_EXCHANGE | ALG_TYPE_DH | ALG_SID_DH_SANDF
CALG_DH_EPHEM = ALG_CLASS_KEY_EXCHANGE | ALG_TYPE_DH | ALG_SID_DH_EPHEM
CALG_AGREEDKEY_ANY = ALG_CLASS_KEY_EXCHANGE | ALG_TYPE_DH | ALG_SID_AGREED_KEY_ANY
CALG_KEA_KEYX = ALG_CLASS_KEY_EXCHANGE | ALG_TYPE_DH | ALG_SID_KEA
CALG_HUGHES_MD5 = ALG_CLASS_KEY_EXCHANGE | ALG_TYPE_ANY | ALG_SID_MD5
CALG_SKIPJACK = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_SKIPJACK
CALG_TEK = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_TEK
CALG_CYLINK_MEK = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_CYLINK_MEK
CALG_SSL3_SHAMD5 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_SSL3SHAMD5
CALG_SSL3_MASTER = ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_SSL3_MASTER
CALG_SCHANNEL_MASTER_HASH = (
    ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_SCHANNEL_MASTER_HASH
)
CALG_SCHANNEL_MAC_KEY = (
    ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_SCHANNEL_MAC_KEY
)
CALG_SCHANNEL_ENC_KEY = (
    ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_SCHANNEL_ENC_KEY
)
CALG_PCT1_MASTER = ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_PCT1_MASTER
CALG_SSL2_MASTER = ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_SSL2_MASTER
CALG_TLS1_MASTER = ALG_CLASS_MSG_ENCRYPT | ALG_TYPE_SECURECHANNEL | ALG_SID_TLS1_MASTER
CALG_RC5 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_RC5
CALG_HMAC = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_HMAC
CALG_TLS1PRF = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_TLS1PRF
CALG_HASH_REPLACE_OWF = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_HASH_REPLACE_OWF
CALG_AES_128 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_AES_128
CALG_AES_192 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_AES_192
CALG_AES_256 = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_AES_256
CALG_AES = ALG_CLASS_DATA_ENCRYPT | ALG_TYPE_BLOCK | ALG_SID_AES
CALG_SHA_256 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_SHA_256
CALG_SHA_384 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_SHA_384
CALG_SHA_512 = ALG_CLASS_HASH | ALG_TYPE_ANY | ALG_SID_SHA_512
CRYPT_VERIFYCONTEXT = -268435456
CRYPT_NEWKEYSET = 0x00000008
CRYPT_DELETEKEYSET = 0x00000010
CRYPT_MACHINE_KEYSET = 0x00000020
CRYPT_SILENT = 0x00000040
CRYPT_EXPORTABLE = 0x00000001
CRYPT_USER_PROTECTED = 0x00000002
CRYPT_CREATE_SALT = 0x00000004
CRYPT_UPDATE_KEY = 0x00000008
CRYPT_NO_SALT = 0x00000010
CRYPT_PREGEN = 0x00000040
CRYPT_RECIPIENT = 0x00000010
CRYPT_INITIATOR = 0x00000040
CRYPT_ONLINE = 0x00000080
CRYPT_SF = 0x00000100
CRYPT_CREATE_IV = 0x00000200
CRYPT_KEK = 0x00000400
CRYPT_DATA_KEY = 0x00000800
CRYPT_VOLATILE = 0x00001000
CRYPT_SGCKEY = 0x00002000
CRYPT_ARCHIVABLE = 0x00004000
RSA1024BIT_KEY = 0x04000000
CRYPT_SERVER = 0x00000400
KEY_LENGTH_MASK = -65536
CRYPT_Y_ONLY = 0x00000001
CRYPT_SSL2_FALLBACK = 0x00000002
CRYPT_DESTROYKEY = 0x00000004
CRYPT_OAEP = 0x00000040
CRYPT_BLOB_VER3 = 0x00000080
CRYPT_IPSEC_HMAC_KEY = 0x00000100
CRYPT_DECRYPT_RSA_NO_PADDING_CHECK = 0x00000020
CRYPT_SECRETDIGEST = 0x00000001
CRYPT_OWF_REPL_LM_HASH = 0x00000001
CRYPT_LITTLE_ENDIAN = 0x00000001
CRYPT_NOHASHOID = 0x00000001
CRYPT_TYPE2_FORMAT = 0x00000002
CRYPT_X931_FORMAT = 0x00000004
CRYPT_MACHINE_DEFAULT = 0x00000001
CRYPT_USER_DEFAULT = 0x00000002
CRYPT_DELETE_DEFAULT = 0x00000004
SIMPLEBLOB = 0x1
PUBLICKEYBLOB = 0x6
PRIVATEKEYBLOB = 0x7
PLAINTEXTKEYBLOB = 0x8
OPAQUEKEYBLOB = 0x9
PUBLICKEYBLOBEX = 0xA
SYMMETRICWRAPKEYBLOB = 0xB
AT_KEYEXCHANGE = 1
AT_SIGNATURE = 2
CRYPT_USERDATA = 1
KP_IV = 1
KP_SALT = 2
KP_PADDING = 3
KP_MODE = 4
KP_MODE_BITS = 5
KP_PERMISSIONS = 6
KP_ALGID = 7
KP_BLOCKLEN = 8
KP_KEYLEN = 9
KP_SALT_EX = 10
KP_P = 11
KP_G = 12
KP_Q = 13
KP_X = 14
KP_Y = 15
KP_RA = 16
KP_RB = 17
KP_INFO = 18
KP_EFFECTIVE_KEYLEN = 19
KP_SCHANNEL_ALG = 20
KP_CLIENT_RANDOM = 21
KP_SERVER_RANDOM = 22
KP_RP = 23
KP_PRECOMP_MD5 = 24
KP_PRECOMP_SHA = 25
KP_CERTIFICATE = 26
KP_CLEAR_KEY = 27
KP_PUB_EX_LEN = 28
KP_PUB_EX_VAL = 29
KP_KEYVAL = 30
KP_ADMIN_PIN = 31
KP_KEYEXCHANGE_PIN = 32
KP_SIGNATURE_PIN = 33
KP_PREHASH = 34
KP_ROUNDS = 35
KP_OAEP_PARAMS = 36
KP_CMS_KEY_INFO = 37
KP_CMS_DH_KEY_INFO = 38
KP_PUB_PARAMS = 39
KP_VERIFY_PARAMS = 40
KP_HIGHEST_VERSION = 41
KP_GET_USE_COUNT = 42
PKCS5_PADDING = 1
RANDOM_PADDING = 2
ZERO_PADDING = 3
CRYPT_MODE_CBC = 1
CRYPT_MODE_ECB = 2
CRYPT_MODE_OFB = 3
CRYPT_MODE_CFB = 4
CRYPT_MODE_CTS = 5
CRYPT_ENCRYPT = 0x0001
CRYPT_DECRYPT = 0x0002
CRYPT_EXPORT = 0x0004
CRYPT_READ = 0x0008
CRYPT_WRITE = 0x0010
CRYPT_MAC = 0x0020
CRYPT_EXPORT_KEY = 0x0040
CRYPT_IMPORT_KEY = 0x0080
CRYPT_ARCHIVE = 0x0100
HP_ALGID = 0x0001
HP_HASHVAL = 0x0002
HP_HASHSIZE = 0x0004
HP_HMAC_INFO = 0x0005
HP_TLS1PRF_LABEL = 0x0006
HP_TLS1PRF_SEED = 0x0007

CRYPT_FAILED = 0
CRYPT_SUCCEED = 1


def RCRYPT_SUCCEEDED(rt):
    return (rt) == CRYPT_SUCCEED


def RCRYPT_FAILED(rt):
    return (rt) == CRYPT_FAILED


PP_ENUMALGS = 1
PP_ENUMCONTAINERS = 2
PP_IMPTYPE = 3
PP_NAME = 4
PP_VERSION = 5
PP_CONTAINER = 6
PP_CHANGE_PASSWORD = 7
PP_KEYSET_SEC_DESCR = 8
PP_CERTCHAIN = 9
PP_KEY_TYPE_SUBTYPE = 10
PP_PROVTYPE = 16
PP_KEYSTORAGE = 17
PP_APPLI_CERT = 18
PP_SYM_KEYSIZE = 19
PP_SESSION_KEYSIZE = 20
PP_UI_PROMPT = 21
PP_ENUMALGS_EX = 22
PP_ENUMMANDROOTS = 25
PP_ENUMELECTROOTS = 26
PP_KEYSET_TYPE = 27
PP_ADMIN_PIN = 31
PP_KEYEXCHANGE_PIN = 32
PP_SIGNATURE_PIN = 33
PP_SIG_KEYSIZE_INC = 34
PP_KEYX_KEYSIZE_INC = 35
PP_UNIQUE_CONTAINER = 36
PP_SGC_INFO = 37
PP_USE_HARDWARE_RNG = 38
PP_KEYSPEC = 39
PP_ENUMEX_SIGNING_PROT = 40
PP_CRYPT_COUNT_KEY_USE = 41
CRYPT_FIRST = 1
CRYPT_NEXT = 2
CRYPT_SGC_ENUM = 4
CRYPT_IMPL_HARDWARE = 1
CRYPT_IMPL_SOFTWARE = 2
CRYPT_IMPL_MIXED = 3
CRYPT_IMPL_UNKNOWN = 4
CRYPT_IMPL_REMOVABLE = 8
CRYPT_SEC_DESCR = 0x00000001
CRYPT_PSTORE = 0x00000002
CRYPT_UI_PROMPT = 0x00000004
CRYPT_FLAG_PCT1 = 0x0001
CRYPT_FLAG_SSL2 = 0x0002
CRYPT_FLAG_SSL3 = 0x0004
CRYPT_FLAG_TLS1 = 0x0008
CRYPT_FLAG_IPSEC = 0x0010
CRYPT_FLAG_SIGNING = 0x0020
CRYPT_SGC = 0x0001
CRYPT_FASTSGC = 0x0002
PP_CLIENT_HWND = 1
PP_CONTEXT_INFO = 11
PP_KEYEXCHANGE_KEYSIZE = 12
PP_SIGNATURE_KEYSIZE = 13
PP_KEYEXCHANGE_ALG = 14
PP_SIGNATURE_ALG = 15
PP_DELETEKEY = 24
PROV_RSA_FULL = 1
PROV_RSA_SIG = 2
PROV_DSS = 3
PROV_FORTEZZA = 4
PROV_MS_EXCHANGE = 5
PROV_SSL = 6
PROV_RSA_SCHANNEL = 12
PROV_DSS_DH = 13
PROV_EC_ECDSA_SIG = 14
PROV_EC_ECNRA_SIG = 15
PROV_EC_ECDSA_FULL = 16
PROV_EC_ECNRA_FULL = 17
PROV_DH_SCHANNEL = 18
PROV_SPYRUS_LYNKS = 20
PROV_RNG = 21
PROV_INTEL_SEC = 22
PROV_REPLACE_OWF = 23
PROV_RSA_AES = 24
MS_DEF_PROV_A = "Microsoft Base Cryptographic Provider v1.0"
MS_DEF_PROV = MS_DEF_PROV_A
MS_ENHANCED_PROV_A = "Microsoft Enhanced Cryptographic Provider v1.0"
MS_ENHANCED_PROV = MS_ENHANCED_PROV_A
MS_STRONG_PROV_A = "Microsoft Strong Cryptographic Provider"
MS_STRONG_PROV = MS_STRONG_PROV_A
MS_DEF_RSA_SIG_PROV_A = "Microsoft RSA Signature Cryptographic Provider"
MS_DEF_RSA_SIG_PROV = MS_DEF_RSA_SIG_PROV_A
MS_DEF_RSA_SCHANNEL_PROV_A = "Microsoft RSA SChannel Cryptographic Provider"
MS_DEF_RSA_SCHANNEL_PROV = MS_DEF_RSA_SCHANNEL_PROV_A
MS_DEF_DSS_PROV_A = "Microsoft Base DSS Cryptographic Provider"
MS_DEF_DSS_PROV = MS_DEF_DSS_PROV_A
MS_DEF_DSS_DH_PROV_A = "Microsoft Base DSS and Diffie-Hellman Cryptographic Provider"
MS_DEF_DSS_DH_PROV = MS_DEF_DSS_DH_PROV_A
MS_ENH_DSS_DH_PROV_A = (
    "Microsoft Enhanced DSS and Diffie-Hellman Cryptographic Provider"
)
MS_ENH_DSS_DH_PROV = MS_ENH_DSS_DH_PROV_A
MS_DEF_DH_SCHANNEL_PROV_A = "Microsoft DH SChannel Cryptographic Provider"
MS_DEF_DH_SCHANNEL_PROV = MS_DEF_DH_SCHANNEL_PROV_A
MS_SCARD_PROV_A = "Microsoft Base Smart Card Crypto Provider"
MS_SCARD_PROV = MS_SCARD_PROV_A
MS_ENH_RSA_AES_PROV_A = "Microsoft Enhanced RSA and AES Cryptographic Provider"
MS_ENH_RSA_AES_PROV = MS_ENH_RSA_AES_PROV_A
MAXUIDLEN = 64
EXPO_OFFLOAD_REG_VALUE = "ExpoOffload"
EXPO_OFFLOAD_FUNC_NAME = "OffloadModExpo"
szKEY_CRYPTOAPI_PRIVATE_KEY_OPTIONS = "Software\\Policies\\Microsoft\\Cryptography"
szFORCE_KEY_PROTECTION = "ForceKeyProtection"
dwFORCE_KEY_PROTECTION_DISABLED = 0x0
dwFORCE_KEY_PROTECTION_USER_SELECT = 0x1
dwFORCE_KEY_PROTECTION_HIGH = 0x2
szKEY_CACHE_ENABLED = "CachePrivateKeys"
szKEY_CACHE_SECONDS = "PrivateKeyLifetimeSeconds"
CUR_BLOB_VERSION = 2
SCHANNEL_MAC_KEY = 0x00000000
SCHANNEL_ENC_KEY = 0x00000001
INTERNATIONAL_USAGE = 0x00000001
szOID_RSA = "1.2.840.113549"
szOID_PKCS = "1.2.840.113549.1"
szOID_RSA_HASH = "1.2.840.113549.2"
szOID_RSA_ENCRYPT = "1.2.840.113549.3"
szOID_PKCS_1 = "1.2.840.113549.1.1"
szOID_PKCS_2 = "1.2.840.113549.1.2"
szOID_PKCS_3 = "1.2.840.113549.1.3"
szOID_PKCS_4 = "1.2.840.113549.1.4"
szOID_PKCS_5 = "1.2.840.113549.1.5"
szOID_PKCS_6 = "1.2.840.113549.1.6"
szOID_PKCS_7 = "1.2.840.113549.1.7"
szOID_PKCS_8 = "1.2.840.113549.1.8"
szOID_PKCS_9 = "1.2.840.113549.1.9"
szOID_PKCS_10 = "1.2.840.113549.1.10"
szOID_PKCS_12 = "1.2.840.113549.1.12"
szOID_RSA_RSA = "1.2.840.113549.1.1.1"
szOID_RSA_MD2RSA = "1.2.840.113549.1.1.2"
szOID_RSA_MD4RSA = "1.2.840.113549.1.1.3"
szOID_RSA_MD5RSA = "1.2.840.113549.1.1.4"
szOID_RSA_SHA1RSA = "1.2.840.113549.1.1.5"
szOID_RSA_SETOAEP_RSA = "1.2.840.113549.1.1.6"
szOID_RSA_DH = "1.2.840.113549.1.3.1"
szOID_RSA_data = "1.2.840.113549.1.7.1"
szOID_RSA_signedData = "1.2.840.113549.1.7.2"
szOID_RSA_envelopedData = "1.2.840.113549.1.7.3"
szOID_RSA_signEnvData = "1.2.840.113549.1.7.4"
szOID_RSA_digestedData = "1.2.840.113549.1.7.5"
szOID_RSA_hashedData = "1.2.840.113549.1.7.5"
szOID_RSA_encryptedData = "1.2.840.113549.1.7.6"
szOID_RSA_emailAddr = "1.2.840.113549.1.9.1"
szOID_RSA_unstructName = "1.2.840.113549.1.9.2"
szOID_RSA_contentType = "1.2.840.113549.1.9.3"
szOID_RSA_messageDigest = "1.2.840.113549.1.9.4"
szOID_RSA_signingTime = "1.2.840.113549.1.9.5"
szOID_RSA_counterSign = "1.2.840.113549.1.9.6"
szOID_RSA_challengePwd = "1.2.840.113549.1.9.7"
szOID_RSA_unstructAddr = "1.2.840.113549.1.9.8"
szOID_RSA_extCertAttrs = "1.2.840.113549.1.9.9"
szOID_RSA_certExtensions = "1.2.840.113549.1.9.14"
szOID_RSA_SMIMECapabilities = "1.2.840.113549.1.9.15"
szOID_RSA_preferSignedData = "1.2.840.113549.1.9.15.1"
szOID_RSA_SMIMEalg = "1.2.840.113549.1.9.16.3"
szOID_RSA_SMIMEalgESDH = "1.2.840.113549.1.9.16.3.5"
szOID_RSA_SMIMEalgCMS3DESwrap = "1.2.840.113549.1.9.16.3.6"
szOID_RSA_SMIMEalgCMSRC2wrap = "1.2.840.113549.1.9.16.3.7"
szOID_RSA_MD2 = "1.2.840.113549.2.2"
szOID_RSA_MD4 = "1.2.840.113549.2.4"
szOID_RSA_MD5 = "1.2.840.113549.2.5"
szOID_RSA_RC2CBC = "1.2.840.113549.3.2"
szOID_RSA_RC4 = "1.2.840.113549.3.4"
szOID_RSA_DES_EDE3_CBC = "1.2.840.113549.3.7"
szOID_RSA_RC5_CBCPad = "1.2.840.113549.3.9"
szOID_ANSI_X942 = "1.2.840.10046"
szOID_ANSI_X942_DH = "1.2.840.10046.2.1"
szOID_X957 = "1.2.840.10040"
szOID_X957_DSA = "1.2.840.10040.4.1"
szOID_X957_SHA1DSA = "1.2.840.10040.4.3"
szOID_DS = "2.5"
szOID_DSALG = "2.5.8"
szOID_DSALG_CRPT = "2.5.8.1"
szOID_DSALG_HASH = "2.5.8.2"
szOID_DSALG_SIGN = "2.5.8.3"
szOID_DSALG_RSA = "2.5.8.1.1"
szOID_OIW = "1.3.14"
szOID_OIWSEC = "1.3.14.3.2"
szOID_OIWSEC_md4RSA = "1.3.14.3.2.2"
szOID_OIWSEC_md5RSA = "1.3.14.3.2.3"
szOID_OIWSEC_md4RSA2 = "1.3.14.3.2.4"
szOID_OIWSEC_desECB = "1.3.14.3.2.6"
szOID_OIWSEC_desCBC = "1.3.14.3.2.7"
szOID_OIWSEC_desOFB = "1.3.14.3.2.8"
szOID_OIWSEC_desCFB = "1.3.14.3.2.9"
szOID_OIWSEC_desMAC = "1.3.14.3.2.10"
szOID_OIWSEC_rsaSign = "1.3.14.3.2.11"
szOID_OIWSEC_dsa = "1.3.14.3.2.12"
szOID_OIWSEC_shaDSA = "1.3.14.3.2.13"
szOID_OIWSEC_mdc2RSA = "1.3.14.3.2.14"
szOID_OIWSEC_shaRSA = "1.3.14.3.2.15"
szOID_OIWSEC_dhCommMod = "1.3.14.3.2.16"
szOID_OIWSEC_desEDE = "1.3.14.3.2.17"
szOID_OIWSEC_sha = "1.3.14.3.2.18"
szOID_OIWSEC_mdc2 = "1.3.14.3.2.19"
szOID_OIWSEC_dsaComm = "1.3.14.3.2.20"
szOID_OIWSEC_dsaCommSHA = "1.3.14.3.2.21"
szOID_OIWSEC_rsaXchg = "1.3.14.3.2.22"
szOID_OIWSEC_keyHashSeal = "1.3.14.3.2.23"
szOID_OIWSEC_md2RSASign = "1.3.14.3.2.24"
szOID_OIWSEC_md5RSASign = "1.3.14.3.2.25"
szOID_OIWSEC_sha1 = "1.3.14.3.2.26"
szOID_OIWSEC_dsaSHA1 = "1.3.14.3.2.27"
szOID_OIWSEC_dsaCommSHA1 = "1.3.14.3.2.28"
szOID_OIWSEC_sha1RSASign = "1.3.14.3.2.29"
szOID_OIWDIR = "1.3.14.7.2"
szOID_OIWDIR_CRPT = "1.3.14.7.2.1"
szOID_OIWDIR_HASH = "1.3.14.7.2.2"
szOID_OIWDIR_SIGN = "1.3.14.7.2.3"
szOID_OIWDIR_md2 = "1.3.14.7.2.2.1"
szOID_OIWDIR_md2RSA = "1.3.14.7.2.3.1"
szOID_INFOSEC = "2.16.840.*********"
szOID_INFOSEC_sdnsSignature = "2.16.840.*********.1.1"
szOID_INFOSEC_mosaicSignature = "2.16.840.*********.1.2"
szOID_INFOSEC_sdnsConfidentiality = "2.16.840.*********.1.3"
szOID_INFOSEC_mosaicConfidentiality = "2.16.840.*********.1.4"
szOID_INFOSEC_sdnsIntegrity = "2.16.840.*********.1.5"
szOID_INFOSEC_mosaicIntegrity = "2.16.840.*********.1.6"
szOID_INFOSEC_sdnsTokenProtection = "2.16.840.*********.1.7"
szOID_INFOSEC_mosaicTokenProtection = "2.16.840.*********.1.8"
szOID_INFOSEC_sdnsKeyManagement = "2.16.840.*********.1.9"
szOID_INFOSEC_mosaicKeyManagement = "2.16.840.*********.1.10"
szOID_INFOSEC_sdnsKMandSig = "2.16.840.*********.1.11"
szOID_INFOSEC_mosaicKMandSig = "2.16.840.*********.1.12"
szOID_INFOSEC_SuiteASignature = "2.16.840.*********.1.13"
szOID_INFOSEC_SuiteAConfidentiality = "2.16.840.*********.1.14"
szOID_INFOSEC_SuiteAIntegrity = "2.16.840.*********.1.15"
szOID_INFOSEC_SuiteATokenProtection = "2.16.840.*********.1.16"
szOID_INFOSEC_SuiteAKeyManagement = "2.16.840.*********.1.17"
szOID_INFOSEC_SuiteAKMandSig = "2.16.840.*********.1.18"
szOID_INFOSEC_mosaicUpdatedSig = "2.16.840.*********.1.19"
szOID_INFOSEC_mosaicKMandUpdSig = "2.16.840.*********.1.20"
szOID_INFOSEC_mosaicUpdatedInteg = "2.16.840.*********.1.21"
szOID_COMMON_NAME = "*******"
szOID_SUR_NAME = "*******"
szOID_DEVICE_SERIAL_NUMBER = "*******"
szOID_COUNTRY_NAME = "*******"
szOID_LOCALITY_NAME = "*******"
szOID_STATE_OR_PROVINCE_NAME = "*******"
szOID_STREET_ADDRESS = "*******"
szOID_ORGANIZATION_NAME = "********"
szOID_ORGANIZATIONAL_UNIT_NAME = "********"
szOID_TITLE = "********"
szOID_DESCRIPTION = "********"
szOID_SEARCH_GUIDE = "********"
szOID_BUSINESS_CATEGORY = "********"
szOID_POSTAL_ADDRESS = "********"
szOID_POSTAL_CODE = "********"
szOID_POST_OFFICE_BOX = "********"
szOID_PHYSICAL_DELIVERY_OFFICE_NAME = "********"
szOID_TELEPHONE_NUMBER = "********"
szOID_TELEX_NUMBER = "********"
szOID_TELETEXT_TERMINAL_IDENTIFIER = "********"
szOID_FACSIMILE_TELEPHONE_NUMBER = "********"
szOID_X21_ADDRESS = "********"
szOID_INTERNATIONAL_ISDN_NUMBER = "********"
szOID_REGISTERED_ADDRESS = "********"
szOID_DESTINATION_INDICATOR = "********"
szOID_PREFERRED_DELIVERY_METHOD = "********"
szOID_PRESENTATION_ADDRESS = "********"
szOID_SUPPORTED_APPLICATION_CONTEXT = "*******0"
szOID_MEMBER = "*******1"
szOID_OWNER = "*******2"
szOID_ROLE_OCCUPANT = "*******3"
szOID_SEE_ALSO = "*******4"
szOID_USER_PASSWORD = "*******5"
szOID_USER_CERTIFICATE = "*******6"
szOID_CA_CERTIFICATE = "*******7"
szOID_AUTHORITY_REVOCATION_LIST = "*******8"
szOID_CERTIFICATE_REVOCATION_LIST = "*******9"
szOID_CROSS_CERTIFICATE_PAIR = "*******0"
szOID_GIVEN_NAME = "*******2"
szOID_INITIALS = "*******3"
szOID_DN_QUALIFIER = "*******6"
szOID_DOMAIN_COMPONENT = "0.9.2342.19200300.100.1.25"
szOID_PKCS_12_FRIENDLY_NAME_ATTR = "1.2.840.113549.1.9.20"
szOID_PKCS_12_LOCAL_KEY_ID = "1.2.840.113549.1.9.21"
szOID_PKCS_12_KEY_PROVIDER_NAME_ATTR = "*******.4.1.311.17.1"
szOID_LOCAL_MACHINE_KEYSET = "*******.4.1.311.17.2"
szOID_KEYID_RDN = "*******.4.1.311.10.7.1"
CERT_RDN_ANY_TYPE = 0
CERT_RDN_ENCODED_BLOB = 1
CERT_RDN_OCTET_STRING = 2
CERT_RDN_NUMERIC_STRING = 3
CERT_RDN_PRINTABLE_STRING = 4
CERT_RDN_TELETEX_STRING = 5
CERT_RDN_T61_STRING = 5
CERT_RDN_VIDEOTEX_STRING = 6
CERT_RDN_IA5_STRING = 7
CERT_RDN_GRAPHIC_STRING = 8
CERT_RDN_VISIBLE_STRING = 9
CERT_RDN_ISO646_STRING = 9
CERT_RDN_GENERAL_STRING = 10
CERT_RDN_UNIVERSAL_STRING = 11
CERT_RDN_INT4_STRING = 11
CERT_RDN_BMP_STRING = 12
CERT_RDN_UNICODE_STRING = 12
CERT_RDN_UTF8_STRING = 13
CERT_RDN_TYPE_MASK = 0x000000FF
CERT_RDN_FLAGS_MASK = -16777216
CERT_RDN_ENABLE_T61_UNICODE_FLAG = -**********
CERT_RDN_ENABLE_UTF8_UNICODE_FLAG = 0x20000000
CERT_RDN_DISABLE_CHECK_TYPE_FLAG = 0x40000000
CERT_RDN_DISABLE_IE4_UTF8_FLAG = 0x01000000
CERT_RSA_PUBLIC_KEY_OBJID = szOID_RSA_RSA
CERT_DEFAULT_OID_PUBLIC_KEY_SIGN = szOID_RSA_RSA
CERT_DEFAULT_OID_PUBLIC_KEY_XCHG = szOID_RSA_RSA
CERT_V1 = 0
CERT_V2 = 1
CERT_V3 = 2
CERT_INFO_VERSION_FLAG = 1
CERT_INFO_SERIAL_NUMBER_FLAG = 2
CERT_INFO_SIGNATURE_ALGORITHM_FLAG = 3
CERT_INFO_ISSUER_FLAG = 4
CERT_INFO_NOT_BEFORE_FLAG = 5
CERT_INFO_NOT_AFTER_FLAG = 6
CERT_INFO_SUBJECT_FLAG = 7
CERT_INFO_SUBJECT_PUBLIC_KEY_INFO_FLAG = 8
CERT_INFO_ISSUER_UNIQUE_ID_FLAG = 9
CERT_INFO_SUBJECT_UNIQUE_ID_FLAG = 10
CERT_INFO_EXTENSION_FLAG = 11
CRL_V1 = 0
CRL_V2 = 1
CERT_REQUEST_V1 = 0
CERT_KEYGEN_REQUEST_V1 = 0
CTL_V1 = 0
CERT_ENCODING_TYPE_MASK = 0x0000FFFF
CMSG_ENCODING_TYPE_MASK = -65536


def GET_CERT_ENCODING_TYPE(X):
    return X & CERT_ENCODING_TYPE_MASK


def GET_CMSG_ENCODING_TYPE(X):
    return X & CMSG_ENCODING_TYPE_MASK


CRYPT_ASN_ENCODING = 0x00000001
CRYPT_NDR_ENCODING = 0x00000002
X509_ASN_ENCODING = 0x00000001
X509_NDR_ENCODING = 0x00000002
PKCS_7_ASN_ENCODING = 0x00010000
PKCS_7_NDR_ENCODING = 0x00020000
CRYPT_FORMAT_STR_MULTI_LINE = 0x0001
CRYPT_FORMAT_STR_NO_HEX = 0x0010
CRYPT_FORMAT_SIMPLE = 0x0001
CRYPT_FORMAT_X509 = 0x0002
CRYPT_FORMAT_OID = 0x0004
CRYPT_FORMAT_RDN_SEMICOLON = 0x0100
CRYPT_FORMAT_RDN_CRLF = 0x0200
CRYPT_FORMAT_RDN_UNQUOTE = 0x0400
CRYPT_FORMAT_RDN_REVERSE = 0x0800
CRYPT_FORMAT_COMMA = 0x1000
CRYPT_FORMAT_SEMICOLON = CRYPT_FORMAT_RDN_SEMICOLON
CRYPT_FORMAT_CRLF = CRYPT_FORMAT_RDN_CRLF
CRYPT_ENCODE_NO_SIGNATURE_BYTE_REVERSAL_FLAG = 0x8
CRYPT_ENCODE_ALLOC_FLAG = 0x8000
CRYPT_UNICODE_NAME_ENCODE_ENABLE_T61_UNICODE_FLAG = CERT_RDN_ENABLE_T61_UNICODE_FLAG
CRYPT_UNICODE_NAME_ENCODE_ENABLE_UTF8_UNICODE_FLAG = CERT_RDN_ENABLE_UTF8_UNICODE_FLAG
CRYPT_UNICODE_NAME_ENCODE_DISABLE_CHECK_TYPE_FLAG = CERT_RDN_DISABLE_CHECK_TYPE_FLAG
CRYPT_SORTED_CTL_ENCODE_HASHED_SUBJECT_IDENTIFIER_FLAG = 0x10000
CRYPT_DECODE_NOCOPY_FLAG = 0x1
CRYPT_DECODE_TO_BE_SIGNED_FLAG = 0x2
CRYPT_DECODE_SHARE_OID_STRING_FLAG = 0x4
CRYPT_DECODE_NO_SIGNATURE_BYTE_REVERSAL_FLAG = 0x8
CRYPT_DECODE_ALLOC_FLAG = 0x8000
CRYPT_UNICODE_NAME_DECODE_DISABLE_IE4_UTF8_FLAG = CERT_RDN_DISABLE_IE4_UTF8_FLAG

CRYPT_ENCODE_DECODE_NONE = 0
X509_CERT = 1
X509_CERT_TO_BE_SIGNED = 2
X509_CERT_CRL_TO_BE_SIGNED = 3
X509_CERT_REQUEST_TO_BE_SIGNED = 4
X509_EXTENSIONS = 5
X509_NAME_VALUE = 6
X509_NAME = 7
X509_PUBLIC_KEY_INFO = 8
X509_AUTHORITY_KEY_ID = 9
X509_KEY_ATTRIBUTES = 10
X509_KEY_USAGE_RESTRICTION = 11
X509_ALTERNATE_NAME = 12
X509_BASIC_CONSTRAINTS = 13
X509_KEY_USAGE = 14
X509_BASIC_CONSTRAINTS2 = 15
X509_CERT_POLICIES = 16
PKCS_UTC_TIME = 17
PKCS_TIME_REQUEST = 18
RSA_CSP_PUBLICKEYBLOB = 19
X509_UNICODE_NAME = 20
X509_KEYGEN_REQUEST_TO_BE_SIGNED = 21
PKCS_ATTRIBUTE = 22
PKCS_CONTENT_INFO_SEQUENCE_OF_ANY = 23
X509_UNICODE_NAME_VALUE = 24
X509_ANY_STRING = X509_NAME_VALUE
X509_UNICODE_ANY_STRING = X509_UNICODE_NAME_VALUE
X509_OCTET_STRING = 25
X509_BITS = 26
X509_INTEGER = 27
X509_MULTI_BYTE_INTEGER = 28
X509_ENUMERATED = 29
X509_CHOICE_OF_TIME = 30
X509_AUTHORITY_KEY_ID2 = 31
X509_AUTHORITY_INFO_ACCESS = 32
X509_SUBJECT_INFO_ACCESS = X509_AUTHORITY_INFO_ACCESS
X509_CRL_REASON_CODE = X509_ENUMERATED
PKCS_CONTENT_INFO = 33
X509_SEQUENCE_OF_ANY = 34
X509_CRL_DIST_POINTS = 35
X509_ENHANCED_KEY_USAGE = 36
PKCS_CTL = 37
X509_MULTI_BYTE_UINT = 38
X509_DSS_PUBLICKEY = X509_MULTI_BYTE_UINT
X509_DSS_PARAMETERS = 39
X509_DSS_SIGNATURE = 40
PKCS_RC2_CBC_PARAMETERS = 41
PKCS_SMIME_CAPABILITIES = 42
X509_QC_STATEMENTS_EXT = 42
PKCS_RSA_PRIVATE_KEY = 43
PKCS_PRIVATE_KEY_INFO = 44
PKCS_ENCRYPTED_PRIVATE_KEY_INFO = 45
X509_PKIX_POLICY_QUALIFIER_USERNOTICE = 46
X509_DH_PUBLICKEY = X509_MULTI_BYTE_UINT
X509_DH_PARAMETERS = 47
PKCS_ATTRIBUTES = 48
PKCS_SORTED_CTL = 49
X509_ECC_SIGNATURE = 47
X942_DH_PARAMETERS = 50
X509_BITS_WITHOUT_TRAILING_ZEROES = 51
X942_OTHER_INFO = 52
X509_CERT_PAIR = 53
X509_ISSUING_DIST_POINT = 54
X509_NAME_CONSTRAINTS = 55
X509_POLICY_MAPPINGS = 56
X509_POLICY_CONSTRAINTS = 57
X509_CROSS_CERT_DIST_POINTS = 58
CMC_DATA = 59
CMC_RESPONSE = 60
CMC_STATUS = 61
CMC_ADD_EXTENSIONS = 62
CMC_ADD_ATTRIBUTES = 63
X509_CERTIFICATE_TEMPLATE = 64
OCSP_SIGNED_REQUEST = 65
OCSP_REQUEST = 66
OCSP_RESPONSE = 67
OCSP_BASIC_SIGNED_RESPONSE = 68
OCSP_BASIC_RESPONSE = 69
X509_LOGOTYPE_EXT = 70
X509_BIOMETRIC_EXT = 71
CNG_RSA_PUBLIC_KEY_BLOB = 72
X509_OBJECT_IDENTIFIER = 73
X509_ALGORITHM_IDENTIFIER = 74
PKCS_RSA_SSA_PSS_PARAMETERS = 75
PKCS_RSAES_OAEP_PARAMETERS = 76
ECC_CMS_SHARED_INFO = 77
TIMESTAMP_REQUEST = 78
TIMESTAMP_RESPONSE = 79
TIMESTAMP_INFO = 80
X509_CERT_BUNDLE = 81
PKCS7_SIGNER_INFO = 500
CMS_SIGNER_INFO = 501

szOID_AUTHORITY_KEY_IDENTIFIER = "2.5.29.1"
szOID_KEY_ATTRIBUTES = "2.5.29.2"
szOID_CERT_POLICIES_95 = "2.5.29.3"
szOID_KEY_USAGE_RESTRICTION = "2.5.29.4"
szOID_SUBJECT_ALT_NAME = "2.5.29.7"
szOID_ISSUER_ALT_NAME = "2.5.29.8"
szOID_BASIC_CONSTRAINTS = "2.5.29.10"
szOID_KEY_USAGE = "2.5.29.15"
szOID_PRIVATEKEY_USAGE_PERIOD = "2.5.29.16"
szOID_BASIC_CONSTRAINTS2 = "2.5.29.19"
szOID_CERT_POLICIES = "2.5.29.32"
szOID_ANY_CERT_POLICY = "2.5.29.32.0"
szOID_AUTHORITY_KEY_IDENTIFIER2 = "2.5.29.35"
szOID_SUBJECT_KEY_IDENTIFIER = "2.5.29.14"
szOID_SUBJECT_ALT_NAME2 = "2.5.29.17"
szOID_ISSUER_ALT_NAME2 = "2.5.29.18"
szOID_CRL_REASON_CODE = "2.5.29.21"
szOID_REASON_CODE_HOLD = "2.5.29.23"
szOID_CRL_DIST_POINTS = "2.5.29.31"
szOID_ENHANCED_KEY_USAGE = "2.5.29.37"
szOID_CRL_NUMBER = "2.5.29.20"
szOID_DELTA_CRL_INDICATOR = "2.5.29.27"
szOID_ISSUING_DIST_POINT = "2.5.29.28"
szOID_FRESHEST_CRL = "2.5.29.46"
szOID_NAME_CONSTRAINTS = "2.5.29.30"
szOID_POLICY_MAPPINGS = "2.5.29.33"
szOID_LEGACY_POLICY_MAPPINGS = "2.5.29.5"
szOID_POLICY_CONSTRAINTS = "2.5.29.36"
szOID_RENEWAL_CERTIFICATE = "*******.4.1.311.13.1"
szOID_ENROLLMENT_NAME_VALUE_PAIR = "*******.4.1.311.13.2.1"
szOID_ENROLLMENT_CSP_PROVIDER = "*******.4.1.311.13.2.2"
szOID_OS_VERSION = "*******.4.1.311.13.2.3"
szOID_ENROLLMENT_AGENT = "*******.4.1.311.20.2.1"
szOID_PKIX = "*******.5.5.7"
szOID_PKIX_PE = "*******.*******"
szOID_AUTHORITY_INFO_ACCESS = "*******.*******.1"
szOID_CERT_EXTENSIONS = "*******.4.1.311.2.1.14"
szOID_NEXT_UPDATE_LOCATION = "*******.4.1.311.10.2"
szOID_REMOVE_CERTIFICATE = "*******.4.1.311.10.8.1"
szOID_CROSS_CERT_DIST_POINTS = "*******.4.1.311.10.9.1"
szOID_CTL = "*******.4.1.311.10.1"
szOID_SORTED_CTL = "*******.4.1.311.10.1.1"
szOID_SERIALIZED = "*******.4.1.311.********"
szOID_NT_PRINCIPAL_NAME = "*******.4.1.311.20.2.3"
szOID_PRODUCT_UPDATE = "*******.4.1.311.31.1"
szOID_ANY_APPLICATION_POLICY = "*******.4.1.311.10.12.1"
szOID_AUTO_ENROLL_CTL_USAGE = "*******.4.1.311.20.1"
szOID_ENROLL_CERTTYPE_EXTENSION = "*******.4.1.311.20.2"
szOID_CERT_MANIFOLD = "*******.4.1.311.20.3"
szOID_CERTSRV_CA_VERSION = "*******.4.1.311.21.1"
szOID_CERTSRV_PREVIOUS_CERT_HASH = "*******.4.1.311.21.2"
szOID_CRL_VIRTUAL_BASE = "*******.4.1.311.21.3"
szOID_CRL_NEXT_PUBLISH = "*******.4.1.311.21.4"
szOID_KP_CA_EXCHANGE = "*******.4.1.311.21.5"
szOID_KP_KEY_RECOVERY_AGENT = "*******.4.1.311.21.6"
szOID_CERTIFICATE_TEMPLATE = "*******.4.1.311.21.7"
szOID_ENTERPRISE_OID_ROOT = "*******.4.1.311.21.8"
szOID_RDN_DUMMY_SIGNER = "*******.4.1.311.21.9"
szOID_APPLICATION_CERT_POLICIES = "*******.4.1.311.21.10"
szOID_APPLICATION_POLICY_MAPPINGS = "*******.4.1.311.21.11"
szOID_APPLICATION_POLICY_CONSTRAINTS = "*******.4.1.311.21.12"
szOID_ARCHIVED_KEY_ATTR = "*******.4.1.311.21.13"
szOID_CRL_SELF_CDP = "*******.4.1.311.21.14"
szOID_REQUIRE_CERT_CHAIN_POLICY = "*******.4.1.311.21.15"
szOID_ARCHIVED_KEY_CERT_HASH = "*******.4.1.311.21.16"
szOID_ISSUED_CERT_HASH = "*******.4.1.311.21.17"
szOID_DS_EMAIL_REPLICATION = "*******.4.1.311.21.19"
szOID_REQUEST_CLIENT_INFO = "*******.4.1.311.21.20"
szOID_ENCRYPTED_KEY_HASH = "*******.4.1.311.21.21"
szOID_CERTSRV_CROSSCA_VERSION = "*******.4.1.311.21.22"
szOID_NTDS_REPLICATION = "*******.4.1.311.25.1"
szOID_SUBJECT_DIR_ATTRS = "********"
szOID_PKIX_KP = "*******.*******"
szOID_PKIX_KP_SERVER_AUTH = "*******.*******.1"
szOID_PKIX_KP_CLIENT_AUTH = "*******.*******.2"
szOID_PKIX_KP_CODE_SIGNING = "*******.*******.3"
szOID_PKIX_KP_EMAIL_PROTECTION = "*******.*******.4"
szOID_PKIX_KP_IPSEC_END_SYSTEM = "*******.*******.5"
szOID_PKIX_KP_IPSEC_TUNNEL = "*******.*******.6"
szOID_PKIX_KP_IPSEC_USER = "*******.*******.7"
szOID_PKIX_KP_TIMESTAMP_SIGNING = "*******.*******.8"
szOID_IPSEC_KP_IKE_INTERMEDIATE = "*******.*******.2"
szOID_KP_CTL_USAGE_SIGNING = "*******.4.1.311.10.3.1"
szOID_KP_TIME_STAMP_SIGNING = "*******.4.1.311.10.3.2"
szOID_SERVER_GATED_CRYPTO = "*******.4.1.311.10.3.3"
szOID_SGC_NETSCAPE = "2.16.840.1.113730.4.1"
szOID_KP_EFS = "*******.4.1.311.10.3.4"
szOID_EFS_RECOVERY = "*******.4.1.311.********"
szOID_WHQL_CRYPTO = "*******.4.1.311.10.3.5"
szOID_NT5_CRYPTO = "*******.4.1.311.10.3.6"
szOID_OEM_WHQL_CRYPTO = "*******.4.1.311.10.3.7"
szOID_EMBEDDED_NT_CRYPTO = "*******.4.1.311.10.3.8"
szOID_ROOT_LIST_SIGNER = "*******.4.1.311.10.3.9"
szOID_KP_QUALIFIED_SUBORDINATION = "*******.4.1.311.10.3.10"
szOID_KP_KEY_RECOVERY = "*******.4.1.311.10.3.11"
szOID_KP_DOCUMENT_SIGNING = "*******.4.1.311.10.3.12"
szOID_KP_LIFETIME_SIGNING = "*******.4.1.311.10.3.13"
szOID_KP_MOBILE_DEVICE_SOFTWARE = "*******.4.1.311.10.3.14"
szOID_DRM = "*******.4.1.311.10.5.1"
szOID_DRM_INDIVIDUALIZATION = "*******.4.1.311.10.5.2"
szOID_LICENSES = "*******.4.1.311.10.6.1"
szOID_LICENSE_SERVER = "*******.4.1.311.10.6.2"
szOID_KP_SMARTCARD_LOGON = "*******.4.1.311.20.2.2"
szOID_YESNO_TRUST_ATTR = "*******.4.1.311.10.4.1"
szOID_PKIX_POLICY_QUALIFIER_CPS = "*******.*******.1"
szOID_PKIX_POLICY_QUALIFIER_USERNOTICE = "*******.*******.2"
szOID_CERT_POLICIES_95_QUALIFIER1 = "2.16.840.1.113733.*******"
CERT_UNICODE_RDN_ERR_INDEX_MASK = 0x3FF
CERT_UNICODE_RDN_ERR_INDEX_SHIFT = 22
CERT_UNICODE_ATTR_ERR_INDEX_MASK = 0x003F
CERT_UNICODE_ATTR_ERR_INDEX_SHIFT = 16
CERT_UNICODE_VALUE_ERR_INDEX_MASK = 0x0000FFFF
CERT_UNICODE_VALUE_ERR_INDEX_SHIFT = 0
CERT_DIGITAL_SIGNATURE_KEY_USAGE = 0x80
CERT_NON_REPUDIATION_KEY_USAGE = 0x40
CERT_KEY_ENCIPHERMENT_KEY_USAGE = 0x20
CERT_DATA_ENCIPHERMENT_KEY_USAGE = 0x10
CERT_KEY_AGREEMENT_KEY_USAGE = 0x08
CERT_KEY_CERT_SIGN_KEY_USAGE = 0x04
CERT_OFFLINE_CRL_SIGN_KEY_USAGE = 0x02
CERT_CRL_SIGN_KEY_USAGE = 0x02
CERT_ENCIPHER_ONLY_KEY_USAGE = 0x01
CERT_DECIPHER_ONLY_KEY_USAGE = 0x80
CERT_ALT_NAME_OTHER_NAME = 1
CERT_ALT_NAME_RFC822_NAME = 2
CERT_ALT_NAME_DNS_NAME = 3
CERT_ALT_NAME_X400_ADDRESS = 4
CERT_ALT_NAME_DIRECTORY_NAME = 5
CERT_ALT_NAME_EDI_PARTY_NAME = 6
CERT_ALT_NAME_URL = 7
CERT_ALT_NAME_IP_ADDRESS = 8
CERT_ALT_NAME_REGISTERED_ID = 9
CERT_ALT_NAME_ENTRY_ERR_INDEX_MASK = 0xFF
CERT_ALT_NAME_ENTRY_ERR_INDEX_SHIFT = 16
CERT_ALT_NAME_VALUE_ERR_INDEX_MASK = 0x0000FFFF
CERT_ALT_NAME_VALUE_ERR_INDEX_SHIFT = 0
CERT_CA_SUBJECT_FLAG = 0x80
CERT_END_ENTITY_SUBJECT_FLAG = 0x40
szOID_PKIX_ACC_DESCR = "*******.********"
szOID_PKIX_OCSP = "*******.********.1"
szOID_PKIX_CA_ISSUERS = "*******.********.2"
CRL_REASON_UNSPECIFIED = 0
CRL_REASON_KEY_COMPROMISE = 1
CRL_REASON_CA_COMPROMISE = 2
CRL_REASON_AFFILIATION_CHANGED = 3
CRL_REASON_SUPERSEDED = 4
CRL_REASON_CESSATION_OF_OPERATION = 5
CRL_REASON_CERTIFICATE_HOLD = 6
CRL_REASON_REMOVE_FROM_CRL = 8
CRL_DIST_POINT_NO_NAME = 0
CRL_DIST_POINT_FULL_NAME = 1
CRL_DIST_POINT_ISSUER_RDN_NAME = 2
CRL_REASON_UNUSED_FLAG = 0x80
CRL_REASON_KEY_COMPROMISE_FLAG = 0x40
CRL_REASON_CA_COMPROMISE_FLAG = 0x20
CRL_REASON_AFFILIATION_CHANGED_FLAG = 0x10
CRL_REASON_SUPERSEDED_FLAG = 0x08
CRL_REASON_CESSATION_OF_OPERATION_FLAG = 0x04
CRL_REASON_CERTIFICATE_HOLD_FLAG = 0x02
CRL_DIST_POINT_ERR_INDEX_MASK = 0x7F
CRL_DIST_POINT_ERR_INDEX_SHIFT = 24

CRL_DIST_POINT_ERR_CRL_ISSUER_BIT = -**********

CROSS_CERT_DIST_POINT_ERR_INDEX_MASK = 0xFF
CROSS_CERT_DIST_POINT_ERR_INDEX_SHIFT = 24

CERT_EXCLUDED_SUBTREE_BIT = -**********

SORTED_CTL_EXT_FLAGS_OFFSET = 0 * 4
SORTED_CTL_EXT_COUNT_OFFSET = 1 * 4
SORTED_CTL_EXT_MAX_COLLISION_OFFSET = 2 * 4
SORTED_CTL_EXT_HASH_BUCKET_OFFSET = 3 * 4
SORTED_CTL_EXT_HASHED_SUBJECT_IDENTIFIER_FLAG = 0x1
CERT_DSS_R_LEN = 20
CERT_DSS_S_LEN = 20
CERT_DSS_SIGNATURE_LEN = CERT_DSS_R_LEN + CERT_DSS_S_LEN
CERT_MAX_ASN_ENCODED_DSS_SIGNATURE_LEN = 2 + 2 * (2 + 20 + 1)
CRYPT_X942_COUNTER_BYTE_LENGTH = 4
CRYPT_X942_KEY_LENGTH_BYTE_LENGTH = 4
CRYPT_X942_PUB_INFO_BYTE_LENGTH = 512 / 8
CRYPT_RC2_40BIT_VERSION = 160
CRYPT_RC2_56BIT_VERSION = 52
CRYPT_RC2_64BIT_VERSION = 120
CRYPT_RC2_128BIT_VERSION = 58
szOID_VERISIGN_PRIVATE_6_9 = "2.16.840.1.113733.1.6.9"
szOID_VERISIGN_ONSITE_JURISDICTION_HASH = "2.16.840.1.113733.1.6.11"
szOID_VERISIGN_BITSTRING_6_13 = "2.16.840.1.113733.1.6.13"
szOID_VERISIGN_ISS_STRONG_CRYPTO = "2.16.840.1.113733.1.8.1"
szOID_NETSCAPE = "2.16.840.1.113730"
szOID_NETSCAPE_CERT_EXTENSION = "2.16.840.1.113730.1"
szOID_NETSCAPE_CERT_TYPE = "2.16.840.1.113730.1.1"
szOID_NETSCAPE_BASE_URL = "2.16.840.1.113730.1.2"
szOID_NETSCAPE_REVOCATION_URL = "2.16.840.1.113730.1.3"
szOID_NETSCAPE_CA_REVOCATION_URL = "2.16.840.1.113730.1.4"
szOID_NETSCAPE_CERT_RENEWAL_URL = "2.16.840.1.113730.1.7"
szOID_NETSCAPE_CA_POLICY_URL = "2.16.840.1.113730.1.8"
szOID_NETSCAPE_SSL_SERVER_NAME = "2.16.840.1.113730.1.12"
szOID_NETSCAPE_COMMENT = "2.16.840.1.113730.1.13"
szOID_NETSCAPE_DATA_TYPE = "2.16.840.1.113730.2"
szOID_NETSCAPE_CERT_SEQUENCE = "2.16.840.1.113730.2.5"
NETSCAPE_SSL_CLIENT_AUTH_CERT_TYPE = 0x80
NETSCAPE_SSL_SERVER_AUTH_CERT_TYPE = 0x40
NETSCAPE_SMIME_CERT_TYPE = 0x20
NETSCAPE_SIGN_CERT_TYPE = 0x10
NETSCAPE_SSL_CA_CERT_TYPE = 0x04
NETSCAPE_SMIME_CA_CERT_TYPE = 0x02
NETSCAPE_SIGN_CA_CERT_TYPE = 0x01
szOID_CT_PKI_DATA = "*******.*******2.2"
szOID_CT_PKI_RESPONSE = "*******.*******2.3"
szOID_PKIX_NO_SIGNATURE = "*******.*******.2"
szOID_CMC = "*******.*******"
szOID_CMC_STATUS_INFO = "*******.*******.1"
szOID_CMC_IDENTIFICATION = "*******.*******.2"
szOID_CMC_IDENTITY_PROOF = "*******.*******.3"
szOID_CMC_DATA_RETURN = "*******.*******.4"
szOID_CMC_TRANSACTION_ID = "*******.*******.5"
szOID_CMC_SENDER_NONCE = "*******.*******.6"
szOID_CMC_RECIPIENT_NONCE = "*******.*******.7"
szOID_CMC_ADD_EXTENSIONS = "*******.*******.8"
szOID_CMC_ENCRYPTED_POP = "*******.*******.9"
szOID_CMC_DECRYPTED_POP = "*******.*******.10"
szOID_CMC_LRA_POP_WITNESS = "*******.*******.11"
szOID_CMC_GET_CERT = "*******.*******.15"
szOID_CMC_GET_CRL = "*******.*******.16"
szOID_CMC_REVOKE_REQUEST = "*******.*******.17"
szOID_CMC_REG_INFO = "*******.*******.18"
szOID_CMC_RESPONSE_INFO = "*******.*******.19"
szOID_CMC_QUERY_PENDING = "*******.*******.21"
szOID_CMC_ID_POP_LINK_RANDOM = "*******.*******.22"
szOID_CMC_ID_POP_LINK_WITNESS = "*******.*******.23"
szOID_CMC_ID_CONFIRM_CERT_ACCEPTANCE = "*******.*******.24"
szOID_CMC_ADD_ATTRIBUTES = "*******.4.1.311.10.10.1"
CMC_TAGGED_CERT_REQUEST_CHOICE = 1
CMC_OTHER_INFO_NO_CHOICE = 0
CMC_OTHER_INFO_FAIL_CHOICE = 1
CMC_OTHER_INFO_PEND_CHOICE = 2
CMC_STATUS_SUCCESS = 0
CMC_STATUS_FAILED = 2
CMC_STATUS_PENDING = 3
CMC_STATUS_NO_SUPPORT = 4
CMC_STATUS_CONFIRM_REQUIRED = 5
CMC_FAIL_BAD_ALG = 0
CMC_FAIL_BAD_MESSAGE_CHECK = 1
CMC_FAIL_BAD_REQUEST = 2
CMC_FAIL_BAD_TIME = 3
CMC_FAIL_BAD_CERT_ID = 4
CMC_FAIL_UNSUPORTED_EXT = 5  # Yes Microsoft made a typo in "UNSUPPORTED"
CMC_FAIL_MUST_ARCHIVE_KEYS = 6
CMC_FAIL_BAD_IDENTITY = 7
CMC_FAIL_POP_REQUIRED = 8
CMC_FAIL_POP_FAILED = 9
CMC_FAIL_NO_KEY_REUSE = 10
CMC_FAIL_INTERNAL_CA_ERROR = 11
CMC_FAIL_TRY_LATER = 12
CRYPT_OID_ENCODE_OBJECT_FUNC = "CryptDllEncodeObject"
CRYPT_OID_DECODE_OBJECT_FUNC = "CryptDllDecodeObject"
CRYPT_OID_ENCODE_OBJECT_EX_FUNC = "CryptDllEncodeObjectEx"
CRYPT_OID_DECODE_OBJECT_EX_FUNC = "CryptDllDecodeObjectEx"
CRYPT_OID_CREATE_COM_OBJECT_FUNC = "CryptDllCreateCOMObject"
CRYPT_OID_VERIFY_REVOCATION_FUNC = "CertDllVerifyRevocation"
CRYPT_OID_VERIFY_CTL_USAGE_FUNC = "CertDllVerifyCTLUsage"
CRYPT_OID_FORMAT_OBJECT_FUNC = "CryptDllFormatObject"
CRYPT_OID_FIND_OID_INFO_FUNC = "CryptDllFindOIDInfo"
CRYPT_OID_FIND_LOCALIZED_NAME_FUNC = "CryptDllFindLocalizedName"

CRYPT_OID_REGPATH = "Software\\Microsoft\\Cryptography\\OID"
CRYPT_OID_REG_ENCODING_TYPE_PREFIX = "EncodingType "
CRYPT_OID_REG_DLL_VALUE_NAME = "Dll"
CRYPT_OID_REG_FUNC_NAME_VALUE_NAME = "FuncName"
CRYPT_OID_REG_FUNC_NAME_VALUE_NAME_A = "FuncName"
CRYPT_OID_REG_FLAGS_VALUE_NAME = "CryptFlags"
CRYPT_DEFAULT_OID = "DEFAULT"
CRYPT_INSTALL_OID_FUNC_BEFORE_FLAG = 1
CRYPT_GET_INSTALLED_OID_FUNC_FLAG = 0x1
CRYPT_REGISTER_FIRST_INDEX = 0
CRYPT_REGISTER_LAST_INDEX = -1
CRYPT_MATCH_ANY_ENCODING_TYPE = -1
CRYPT_HASH_ALG_OID_GROUP_ID = 1
CRYPT_ENCRYPT_ALG_OID_GROUP_ID = 2
CRYPT_PUBKEY_ALG_OID_GROUP_ID = 3
CRYPT_SIGN_ALG_OID_GROUP_ID = 4
CRYPT_RDN_ATTR_OID_GROUP_ID = 5
CRYPT_EXT_OR_ATTR_OID_GROUP_ID = 6
CRYPT_ENHKEY_USAGE_OID_GROUP_ID = 7
CRYPT_POLICY_OID_GROUP_ID = 8
CRYPT_TEMPLATE_OID_GROUP_ID = 9
CRYPT_LAST_OID_GROUP_ID = 9
CRYPT_FIRST_ALG_OID_GROUP_ID = CRYPT_HASH_ALG_OID_GROUP_ID
CRYPT_LAST_ALG_OID_GROUP_ID = CRYPT_SIGN_ALG_OID_GROUP_ID
CRYPT_OID_INHIBIT_SIGNATURE_FORMAT_FLAG = 0x1
CRYPT_OID_USE_PUBKEY_PARA_FOR_PKCS7_FLAG = 0x2
CRYPT_OID_NO_NULL_ALGORITHM_PARA_FLAG = 0x4
CRYPT_OID_INFO_OID_KEY = 1
CRYPT_OID_INFO_NAME_KEY = 2
CRYPT_OID_INFO_ALGID_KEY = 3
CRYPT_OID_INFO_SIGN_KEY = 4
CRYPT_INSTALL_OID_INFO_BEFORE_FLAG = 1
CRYPT_LOCALIZED_NAME_ENCODING_TYPE = 0
CRYPT_LOCALIZED_NAME_OID = "LocalizedNames"
szOID_PKCS_7_DATA = "1.2.840.113549.1.7.1"
szOID_PKCS_7_SIGNED = "1.2.840.113549.1.7.2"
szOID_PKCS_7_ENVELOPED = "1.2.840.113549.1.7.3"
szOID_PKCS_7_SIGNEDANDENVELOPED = "1.2.840.113549.1.7.4"
szOID_PKCS_7_DIGESTED = "1.2.840.113549.1.7.5"
szOID_PKCS_7_ENCRYPTED = "1.2.840.113549.1.7.6"
szOID_PKCS_9_CONTENT_TYPE = "1.2.840.113549.1.9.3"
szOID_PKCS_9_MESSAGE_DIGEST = "1.2.840.113549.1.9.4"
CMSG_DATA = 1
CMSG_SIGNED = 2
CMSG_ENVELOPED = 3
CMSG_SIGNED_AND_ENVELOPED = 4
CMSG_HASHED = 5
CMSG_ENCRYPTED = 6

CMSG_ALL_FLAGS = -1
CMSG_DATA_FLAG = 1 << CMSG_DATA
CMSG_SIGNED_FLAG = 1 << CMSG_SIGNED
CMSG_ENVELOPED_FLAG = 1 << CMSG_ENVELOPED
CMSG_SIGNED_AND_ENVELOPED_FLAG = 1 << CMSG_SIGNED_AND_ENVELOPED
CMSG_HASHED_FLAG = 1 << CMSG_HASHED
CMSG_ENCRYPTED_FLAG = 1 << CMSG_ENCRYPTED
CERT_ID_ISSUER_SERIAL_NUMBER = 1
CERT_ID_KEY_IDENTIFIER = 2
CERT_ID_SHA1_HASH = 3
CMSG_KEY_AGREE_EPHEMERAL_KEY_CHOICE = 1
CMSG_KEY_AGREE_STATIC_KEY_CHOICE = 2
CMSG_MAIL_LIST_HANDLE_KEY_CHOICE = 1
CMSG_KEY_TRANS_RECIPIENT = 1
CMSG_KEY_AGREE_RECIPIENT = 2
CMSG_MAIL_LIST_RECIPIENT = 3
CMSG_SP3_COMPATIBLE_ENCRYPT_FLAG = -**********
CMSG_RC4_NO_SALT_FLAG = 0x40000000
CMSG_INDEFINITE_LENGTH = -1
CMSG_BARE_CONTENT_FLAG = 0x00000001
CMSG_LENGTH_ONLY_FLAG = 0x00000002
CMSG_DETACHED_FLAG = 0x00000004
CMSG_AUTHENTICATED_ATTRIBUTES_FLAG = 0x00000008
CMSG_CONTENTS_OCTETS_FLAG = 0x00000010
CMSG_MAX_LENGTH_FLAG = 0x00000020
CMSG_CMS_ENCAPSULATED_CONTENT_FLAG = 0x00000040
CMSG_CRYPT_RELEASE_CONTEXT_FLAG = 0x00008000
CMSG_TYPE_PARAM = 1
CMSG_CONTENT_PARAM = 2
CMSG_BARE_CONTENT_PARAM = 3
CMSG_INNER_CONTENT_TYPE_PARAM = 4
CMSG_SIGNER_COUNT_PARAM = 5
CMSG_SIGNER_INFO_PARAM = 6
CMSG_SIGNER_CERT_INFO_PARAM = 7
CMSG_SIGNER_HASH_ALGORITHM_PARAM = 8
CMSG_SIGNER_AUTH_ATTR_PARAM = 9
CMSG_SIGNER_UNAUTH_ATTR_PARAM = 10
CMSG_CERT_COUNT_PARAM = 11
CMSG_CERT_PARAM = 12
CMSG_CRL_COUNT_PARAM = 13
CMSG_CRL_PARAM = 14
CMSG_ENVELOPE_ALGORITHM_PARAM = 15
CMSG_RECIPIENT_COUNT_PARAM = 17
CMSG_RECIPIENT_INDEX_PARAM = 18
CMSG_RECIPIENT_INFO_PARAM = 19
CMSG_HASH_ALGORITHM_PARAM = 20
CMSG_HASH_DATA_PARAM = 21
CMSG_COMPUTED_HASH_PARAM = 22
CMSG_ENCRYPT_PARAM = 26
CMSG_ENCRYPTED_DIGEST = 27
CMSG_ENCODED_SIGNER = 28
CMSG_ENCODED_MESSAGE = 29
CMSG_VERSION_PARAM = 30
CMSG_ATTR_CERT_COUNT_PARAM = 31
CMSG_ATTR_CERT_PARAM = 32
CMSG_CMS_RECIPIENT_COUNT_PARAM = 33
CMSG_CMS_RECIPIENT_INDEX_PARAM = 34
CMSG_CMS_RECIPIENT_ENCRYPTED_KEY_INDEX_PARAM = 35
CMSG_CMS_RECIPIENT_INFO_PARAM = 36
CMSG_UNPROTECTED_ATTR_PARAM = 37
CMSG_SIGNER_CERT_ID_PARAM = 38
CMSG_CMS_SIGNER_INFO_PARAM = 39
CMSG_SIGNED_DATA_V1 = 1
CMSG_SIGNED_DATA_V3 = 3
CMSG_SIGNED_DATA_PKCS_1_5_VERSION = CMSG_SIGNED_DATA_V1
CMSG_SIGNED_DATA_CMS_VERSION = CMSG_SIGNED_DATA_V3
CMSG_SIGNER_INFO_V1 = 1
CMSG_SIGNER_INFO_V3 = 3
CMSG_SIGNER_INFO_PKCS_1_5_VERSION = CMSG_SIGNER_INFO_V1
CMSG_SIGNER_INFO_CMS_VERSION = CMSG_SIGNER_INFO_V3
CMSG_HASHED_DATA_V0 = 0
CMSG_HASHED_DATA_V2 = 2
CMSG_HASHED_DATA_PKCS_1_5_VERSION = CMSG_HASHED_DATA_V0
CMSG_HASHED_DATA_CMS_VERSION = CMSG_HASHED_DATA_V2
CMSG_ENVELOPED_DATA_V0 = 0
CMSG_ENVELOPED_DATA_V2 = 2
CMSG_ENVELOPED_DATA_PKCS_1_5_VERSION = CMSG_ENVELOPED_DATA_V0
CMSG_ENVELOPED_DATA_CMS_VERSION = CMSG_ENVELOPED_DATA_V2
CMSG_KEY_AGREE_ORIGINATOR_CERT = 1
CMSG_KEY_AGREE_ORIGINATOR_PUBLIC_KEY = 2
CMSG_ENVELOPED_RECIPIENT_V0 = 0
CMSG_ENVELOPED_RECIPIENT_V2 = 2
CMSG_ENVELOPED_RECIPIENT_V3 = 3
CMSG_ENVELOPED_RECIPIENT_V4 = 4
CMSG_KEY_TRANS_PKCS_1_5_VERSION = CMSG_ENVELOPED_RECIPIENT_V0
CMSG_KEY_TRANS_CMS_VERSION = CMSG_ENVELOPED_RECIPIENT_V2
CMSG_KEY_AGREE_VERSION = CMSG_ENVELOPED_RECIPIENT_V3
CMSG_MAIL_LIST_VERSION = CMSG_ENVELOPED_RECIPIENT_V4
CMSG_CTRL_VERIFY_SIGNATURE = 1
CMSG_CTRL_DECRYPT = 2
CMSG_CTRL_VERIFY_HASH = 5
CMSG_CTRL_ADD_SIGNER = 6
CMSG_CTRL_DEL_SIGNER = 7
CMSG_CTRL_ADD_SIGNER_UNAUTH_ATTR = 8
CMSG_CTRL_DEL_SIGNER_UNAUTH_ATTR = 9
CMSG_CTRL_ADD_CERT = 10
CMSG_CTRL_DEL_CERT = 11
CMSG_CTRL_ADD_CRL = 12
CMSG_CTRL_DEL_CRL = 13
CMSG_CTRL_ADD_ATTR_CERT = 14
CMSG_CTRL_DEL_ATTR_CERT = 15
CMSG_CTRL_KEY_TRANS_DECRYPT = 16
CMSG_CTRL_KEY_AGREE_DECRYPT = 17
CMSG_CTRL_MAIL_LIST_DECRYPT = 18
CMSG_CTRL_VERIFY_SIGNATURE_EX = 19
CMSG_CTRL_ADD_CMS_SIGNER_INFO = 20
CMSG_VERIFY_SIGNER_PUBKEY = 1
CMSG_VERIFY_SIGNER_CERT = 2
CMSG_VERIFY_SIGNER_CHAIN = 3
CMSG_VERIFY_SIGNER_NULL = 4
CMSG_OID_GEN_ENCRYPT_KEY_FUNC = "CryptMsgDllGenEncryptKey"
CMSG_OID_EXPORT_ENCRYPT_KEY_FUNC = "CryptMsgDllExportEncryptKey"
CMSG_OID_IMPORT_ENCRYPT_KEY_FUNC = "CryptMsgDllImportEncryptKey"
CMSG_CONTENT_ENCRYPT_PAD_ENCODED_LEN_FLAG = 0x00000001
CMSG_DEFAULT_INSTALLABLE_FUNC_OID = 1
CMSG_CONTENT_ENCRYPT_FREE_PARA_FLAG = 0x00000001
CMSG_CONTENT_ENCRYPT_RELEASE_CONTEXT_FLAG = 0x00008000
CMSG_OID_GEN_CONTENT_ENCRYPT_KEY_FUNC = "CryptMsgDllGenContentEncryptKey"
CMSG_KEY_TRANS_ENCRYPT_FREE_PARA_FLAG = 0x00000001
CMSG_OID_EXPORT_KEY_TRANS_FUNC = "CryptMsgDllExportKeyTrans"
CMSG_KEY_AGREE_ENCRYPT_FREE_PARA_FLAG = 0x00000001
CMSG_KEY_AGREE_ENCRYPT_FREE_MATERIAL_FLAG = 0x00000002
CMSG_KEY_AGREE_ENCRYPT_FREE_PUBKEY_ALG_FLAG = 0x00000004
CMSG_KEY_AGREE_ENCRYPT_FREE_PUBKEY_PARA_FLAG = 0x00000008
CMSG_KEY_AGREE_ENCRYPT_FREE_PUBKEY_BITS_FLAG = 0x00000010
CMSG_OID_EXPORT_KEY_AGREE_FUNC = "CryptMsgDllExportKeyAgree"
CMSG_MAIL_LIST_ENCRYPT_FREE_PARA_FLAG = 0x00000001
CMSG_OID_EXPORT_MAIL_LIST_FUNC = "CryptMsgDllExportMailList"
CMSG_OID_IMPORT_KEY_TRANS_FUNC = "CryptMsgDllImportKeyTrans"
CMSG_OID_IMPORT_KEY_AGREE_FUNC = "CryptMsgDllImportKeyAgree"
CMSG_OID_IMPORT_MAIL_LIST_FUNC = "CryptMsgDllImportMailList"

# Certificate property id's used with CertGetCertificateContextProperty
CERT_KEY_PROV_HANDLE_PROP_ID = 1
CERT_KEY_PROV_INFO_PROP_ID = 2
CERT_SHA1_HASH_PROP_ID = 3
CERT_MD5_HASH_PROP_ID = 4
CERT_HASH_PROP_ID = CERT_SHA1_HASH_PROP_ID
CERT_KEY_CONTEXT_PROP_ID = 5
CERT_KEY_SPEC_PROP_ID = 6
CERT_IE30_RESERVED_PROP_ID = 7
CERT_PUBKEY_HASH_RESERVED_PROP_ID = 8
CERT_ENHKEY_USAGE_PROP_ID = 9
CERT_CTL_USAGE_PROP_ID = CERT_ENHKEY_USAGE_PROP_ID
CERT_NEXT_UPDATE_LOCATION_PROP_ID = 10
CERT_FRIENDLY_NAME_PROP_ID = 11
CERT_PVK_FILE_PROP_ID = 12
CERT_DESCRIPTION_PROP_ID = 13
CERT_ACCESS_STATE_PROP_ID = 14
CERT_SIGNATURE_HASH_PROP_ID = 15
CERT_SMART_CARD_DATA_PROP_ID = 16
CERT_EFS_PROP_ID = 17
CERT_FORTEZZA_DATA_PROP_ID = 18
CERT_ARCHIVED_PROP_ID = 19
CERT_KEY_IDENTIFIER_PROP_ID = 20
CERT_AUTO_ENROLL_PROP_ID = 21
CERT_PUBKEY_ALG_PARA_PROP_ID = 22
CERT_CROSS_CERT_DIST_POINTS_PROP_ID = 23
CERT_ISSUER_PUBLIC_KEY_MD5_HASH_PROP_ID = 24
CERT_SUBJECT_PUBLIC_KEY_MD5_HASH_PROP_ID = 25
CERT_ENROLLMENT_PROP_ID = 26
CERT_DATE_STAMP_PROP_ID = 27
CERT_ISSUER_SERIAL_NUMBER_MD5_HASH_PROP_ID = 28
CERT_SUBJECT_NAME_MD5_HASH_PROP_ID = 29
CERT_EXTENDED_ERROR_INFO_PROP_ID = 30
CERT_RENEWAL_PROP_ID = 64
CERT_ARCHIVED_KEY_HASH_PROP_ID = 65
CERT_AUTO_ENROLL_RETRY_PROP_ID = 66
CERT_AIA_URL_RETRIEVED_PROP_ID = 67
CERT_AUTHORITY_INFO_ACCESS_PROP_ID = 68
CERT_BACKED_UP_PROP_ID = 69
CERT_OCSP_RESPONSE_PROP_ID = 70
CERT_REQUEST_ORIGINATOR_PROP_ID = 71
CERT_SOURCE_LOCATION_PROP_ID = 72
CERT_SOURCE_URL_PROP_ID = 73
CERT_NEW_KEY_PROP_ID = 74
CERT_OCSP_CACHE_PREFIX_PROP_ID = 75
CERT_SMART_CARD_ROOT_INFO_PROP_ID = 76
CERT_NO_AUTO_EXPIRE_CHECK_PROP_ID = 77
CERT_NCRYPT_KEY_HANDLE_PROP_ID = 78
CERT_HCRYPTPROV_OR_NCRYPT_KEY_HANDLE_PROP_ID = 79
CERT_SUBJECT_INFO_ACCESS_PROP_ID = 80
CERT_CA_OCSP_AUTHORITY_INFO_ACCESS_PROP_ID = 81
CERT_CA_DISABLE_CRL_PROP_ID = 82
CERT_ROOT_PROGRAM_CERT_POLICIES_PROP_ID = 83
CERT_ROOT_PROGRAM_NAME_CONSTRAINTS_PROP_ID = 84
CERT_SUBJECT_OCSP_AUTHORITY_INFO_ACCESS_PROP_ID = 85
CERT_SUBJECT_DISABLE_CRL_PROP_ID = 86
CERT_CEP_PROP_ID = 87
CERT_SIGN_HASH_CNG_ALG_PROP_ID = 89
CERT_SCARD_PIN_ID_PROP_ID = 90
CERT_SCARD_PIN_INFO_PROP_ID = 91
CERT_FIRST_RESERVED_PROP_ID = 92
CERT_LAST_RESERVED_PROP_ID = 0x00007FFF
CERT_FIRST_USER_PROP_ID = 0x00008000
CERT_LAST_USER_PROP_ID = 0x0000FFFF

szOID_CERT_PROP_ID_PREFIX = "*******.4.1.311.10.11."
szOID_CERT_KEY_IDENTIFIER_PROP_ID = "*******.4.1.311.10.11.20"
szOID_CERT_ISSUER_SERIAL_NUMBER_MD5_HASH_PROP_ID = "*******.4.1.311.10.11.28"
szOID_CERT_SUBJECT_NAME_MD5_HASH_PROP_ID = "*******.4.1.311.10.11.29"
CERT_ACCESS_STATE_WRITE_PERSIST_FLAG = 0x1
CERT_ACCESS_STATE_SYSTEM_STORE_FLAG = 0x2
CERT_ACCESS_STATE_LM_SYSTEM_STORE_FLAG = 0x4
CERT_SET_KEY_PROV_HANDLE_PROP_ID = 0x00000001
CERT_SET_KEY_CONTEXT_PROP_ID = 0x00000001
sz_CERT_STORE_PROV_MEMORY = "Memory"
sz_CERT_STORE_PROV_FILENAME_W = "File"
sz_CERT_STORE_PROV_FILENAME = sz_CERT_STORE_PROV_FILENAME_W
sz_CERT_STORE_PROV_SYSTEM_W = "System"
sz_CERT_STORE_PROV_SYSTEM = sz_CERT_STORE_PROV_SYSTEM_W
sz_CERT_STORE_PROV_PKCS7 = "PKCS7"
sz_CERT_STORE_PROV_SERIALIZED = "Serialized"
sz_CERT_STORE_PROV_COLLECTION = "Collection"
sz_CERT_STORE_PROV_SYSTEM_REGISTRY_W = "SystemRegistry"
sz_CERT_STORE_PROV_SYSTEM_REGISTRY = sz_CERT_STORE_PROV_SYSTEM_REGISTRY_W
sz_CERT_STORE_PROV_PHYSICAL_W = "Physical"
sz_CERT_STORE_PROV_PHYSICAL = sz_CERT_STORE_PROV_PHYSICAL_W
sz_CERT_STORE_PROV_SMART_CARD_W = "SmartCard"
sz_CERT_STORE_PROV_SMART_CARD = sz_CERT_STORE_PROV_SMART_CARD_W
sz_CERT_STORE_PROV_LDAP_W = "Ldap"
sz_CERT_STORE_PROV_LDAP = sz_CERT_STORE_PROV_LDAP_W
CERT_STORE_SIGNATURE_FLAG = 0x00000001
CERT_STORE_TIME_VALIDITY_FLAG = 0x00000002
CERT_STORE_REVOCATION_FLAG = 0x00000004
CERT_STORE_NO_CRL_FLAG = 0x00010000
CERT_STORE_NO_ISSUER_FLAG = 0x00020000
CERT_STORE_BASE_CRL_FLAG = 0x00000100
CERT_STORE_DELTA_CRL_FLAG = 0x00000200
CERT_STORE_NO_CRYPT_RELEASE_FLAG = 0x00000001
CERT_STORE_SET_LOCALIZED_NAME_FLAG = 0x00000002
CERT_STORE_DEFER_CLOSE_UNTIL_LAST_FREE_FLAG = 0x00000004
CERT_STORE_DELETE_FLAG = 0x00000010
CERT_STORE_UNSAFE_PHYSICAL_FLAG = 0x00000020
CERT_STORE_SHARE_STORE_FLAG = 0x00000040
CERT_STORE_SHARE_CONTEXT_FLAG = 0x00000080
CERT_STORE_MANIFOLD_FLAG = 0x00000100
CERT_STORE_ENUM_ARCHIVED_FLAG = 0x00000200
CERT_STORE_UPDATE_KEYID_FLAG = 0x00000400
CERT_STORE_BACKUP_RESTORE_FLAG = 0x00000800
CERT_STORE_READONLY_FLAG = 0x00008000
CERT_STORE_OPEN_EXISTING_FLAG = 0x00004000
CERT_STORE_CREATE_NEW_FLAG = 0x00002000
CERT_STORE_MAXIMUM_ALLOWED_FLAG = 0x00001000
CERT_SYSTEM_STORE_MASK = -65536
CERT_SYSTEM_STORE_RELOCATE_FLAG = -**********
CERT_SYSTEM_STORE_UNPROTECTED_FLAG = 0x40000000
CERT_SYSTEM_STORE_LOCATION_MASK = 0x00FF0000
CERT_SYSTEM_STORE_LOCATION_SHIFT = 16
CERT_SYSTEM_STORE_CURRENT_USER_ID = 1
CERT_SYSTEM_STORE_LOCAL_MACHINE_ID = 2
CERT_SYSTEM_STORE_CURRENT_SERVICE_ID = 4
CERT_SYSTEM_STORE_SERVICES_ID = 5
CERT_SYSTEM_STORE_USERS_ID = 6
CERT_SYSTEM_STORE_CURRENT_USER_GROUP_POLICY_ID = 7
CERT_SYSTEM_STORE_LOCAL_MACHINE_GROUP_POLICY_ID = 8
CERT_SYSTEM_STORE_LOCAL_MACHINE_ENTERPRISE_ID = 9
CERT_SYSTEM_STORE_CURRENT_USER = (
    CERT_SYSTEM_STORE_CURRENT_USER_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_SYSTEM_STORE_LOCAL_MACHINE = (
    CERT_SYSTEM_STORE_LOCAL_MACHINE_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_SYSTEM_STORE_CURRENT_SERVICE = (
    CERT_SYSTEM_STORE_CURRENT_SERVICE_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_SYSTEM_STORE_SERVICES = (
    CERT_SYSTEM_STORE_SERVICES_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_SYSTEM_STORE_USERS = CERT_SYSTEM_STORE_USERS_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
CERT_SYSTEM_STORE_CURRENT_USER_GROUP_POLICY = (
    CERT_SYSTEM_STORE_CURRENT_USER_GROUP_POLICY_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_SYSTEM_STORE_LOCAL_MACHINE_GROUP_POLICY = (
    CERT_SYSTEM_STORE_LOCAL_MACHINE_GROUP_POLICY_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_SYSTEM_STORE_LOCAL_MACHINE_ENTERPRISE = (
    CERT_SYSTEM_STORE_LOCAL_MACHINE_ENTERPRISE_ID << CERT_SYSTEM_STORE_LOCATION_SHIFT
)
CERT_PROT_ROOT_DISABLE_CURRENT_USER_FLAG = 0x1
CERT_PROT_ROOT_INHIBIT_ADD_AT_INIT_FLAG = 0x2
CERT_PROT_ROOT_INHIBIT_PURGE_LM_FLAG = 0x4
CERT_PROT_ROOT_DISABLE_LM_AUTH_FLAG = 0x8
CERT_PROT_ROOT_ONLY_LM_GPT_FLAG = 0x8
CERT_PROT_ROOT_DISABLE_NT_AUTH_REQUIRED_FLAG = 0x10
CERT_PROT_ROOT_DISABLE_NOT_DEFINED_NAME_CONSTRAINT_FLAG = 0x20
CERT_TRUST_PUB_ALLOW_TRUST_MASK = 0x00000003
CERT_TRUST_PUB_ALLOW_END_USER_TRUST = 0x00000000
CERT_TRUST_PUB_ALLOW_MACHINE_ADMIN_TRUST = 0x00000001
CERT_TRUST_PUB_ALLOW_ENTERPRISE_ADMIN_TRUST = 0x00000002
CERT_TRUST_PUB_CHECK_PUBLISHER_REV_FLAG = 0x00000100
CERT_TRUST_PUB_CHECK_TIMESTAMP_REV_FLAG = 0x00000200

CERT_AUTH_ROOT_AUTO_UPDATE_DISABLE_UNTRUSTED_ROOT_LOGGING_FLAG = 0x1
CERT_AUTH_ROOT_AUTO_UPDATE_DISABLE_PARTIAL_CHAIN_LOGGING_FLAG = 0x2
CERT_AUTH_ROOT_AUTO_UPDATE_ROOT_DIR_URL_VALUE_NAME = "RootDirUrl"
CERT_AUTH_ROOT_AUTO_UPDATE_SYNC_DELTA_TIME_VALUE_NAME = "SyncDeltaTime"
CERT_AUTH_ROOT_AUTO_UPDATE_FLAGS_VALUE_NAME = "Flags"
CERT_AUTH_ROOT_CTL_FILENAME = "authroot.stl"
CERT_AUTH_ROOT_CTL_FILENAME_A = "authroot.stl"
CERT_AUTH_ROOT_CAB_FILENAME = "authrootstl.cab"
CERT_AUTH_ROOT_SEQ_FILENAME = "authrootseq.txt"
CERT_AUTH_ROOT_CERT_EXT = ".crt"

CERT_GROUP_POLICY_SYSTEM_STORE_REGPATH = (
    r"Software\Policies\Microsoft\SystemCertificates"
)
CERT_EFSBLOB_REGPATH = CERT_GROUP_POLICY_SYSTEM_STORE_REGPATH + r"\EFS"
CERT_EFSBLOB_VALUE_NAME = "EFSBlob"
CERT_PROT_ROOT_FLAGS_REGPATH = (
    CERT_GROUP_POLICY_SYSTEM_STORE_REGPATH + r"\Root\ProtectedRoots"
)
CERT_PROT_ROOT_FLAGS_VALUE_NAME = "Flags"
CERT_TRUST_PUB_SAFER_GROUP_POLICY_REGPATH = (
    CERT_GROUP_POLICY_SYSTEM_STORE_REGPATH + r"\TrustedPublisher\Safer"
)
CERT_LOCAL_MACHINE_SYSTEM_STORE_REGPATH = r"Software\Microsoft\SystemCertificates"
CERT_TRUST_PUB_SAFER_LOCAL_MACHINE_REGPATH = (
    CERT_LOCAL_MACHINE_SYSTEM_STORE_REGPATH + r"\TrustedPublisher\Safer"
)
CERT_TRUST_PUB_AUTHENTICODE_FLAGS_VALUE_NAME = "AuthenticodeFlags"
CERT_OCM_SUBCOMPONENTS_LOCAL_MACHINE_REGPATH = (
    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Setup\OC Manager\Subcomponents"
)
CERT_OCM_SUBCOMPONENTS_ROOT_AUTO_UPDATE_VALUE_NAME = r"RootAutoUpdate"
CERT_DISABLE_ROOT_AUTO_UPDATE_REGPATH = (
    CERT_GROUP_POLICY_SYSTEM_STORE_REGPATH + r"\AuthRoot"
)
CERT_DISABLE_ROOT_AUTO_UPDATE_VALUE_NAME = "DisableRootAutoUpdate"
CERT_AUTH_ROOT_AUTO_UPDATE_LOCAL_MACHINE_REGPATH = (
    CERT_LOCAL_MACHINE_SYSTEM_STORE_REGPATH + r"\AuthRoot\AutoUpdate"
)

CERT_REGISTRY_STORE_REMOTE_FLAG = 0x10000
CERT_REGISTRY_STORE_SERIALIZED_FLAG = 0x20000
CERT_REGISTRY_STORE_CLIENT_GPT_FLAG = -**********
CERT_REGISTRY_STORE_LM_GPT_FLAG = 0x01000000
CERT_REGISTRY_STORE_ROAMING_FLAG = 0x40000
CERT_REGISTRY_STORE_MY_IE_DIRTY_FLAG = 0x80000
CERT_IE_DIRTY_FLAGS_REGPATH = r"Software\Microsoft\Cryptography\IEDirtyFlags"

CERT_FILE_STORE_COMMIT_ENABLE_FLAG = 0x10000
CERT_LDAP_STORE_SIGN_FLAG = 0x10000
CERT_LDAP_STORE_AREC_EXCLUSIVE_FLAG = 0x20000
CERT_LDAP_STORE_OPENED_FLAG = 0x40000
CERT_LDAP_STORE_UNBIND_FLAG = 0x80000
CRYPT_OID_OPEN_STORE_PROV_FUNC = "CertDllOpenStoreProv"

CERT_STORE_PROV_EXTERNAL_FLAG = 0x1
CERT_STORE_PROV_DELETED_FLAG = 0x2
CERT_STORE_PROV_NO_PERSIST_FLAG = 0x4
CERT_STORE_PROV_SYSTEM_STORE_FLAG = 0x8
CERT_STORE_PROV_LM_SYSTEM_STORE_FLAG = 0x10
CERT_STORE_PROV_CLOSE_FUNC = 0
CERT_STORE_PROV_READ_CERT_FUNC = 1
CERT_STORE_PROV_WRITE_CERT_FUNC = 2
CERT_STORE_PROV_DELETE_CERT_FUNC = 3
CERT_STORE_PROV_SET_CERT_PROPERTY_FUNC = 4
CERT_STORE_PROV_READ_CRL_FUNC = 5
CERT_STORE_PROV_WRITE_CRL_FUNC = 6
CERT_STORE_PROV_DELETE_CRL_FUNC = 7
CERT_STORE_PROV_SET_CRL_PROPERTY_FUNC = 8
CERT_STORE_PROV_READ_CTL_FUNC = 9
CERT_STORE_PROV_WRITE_CTL_FUNC = 10
CERT_STORE_PROV_DELETE_CTL_FUNC = 11
CERT_STORE_PROV_SET_CTL_PROPERTY_FUNC = 12
CERT_STORE_PROV_CONTROL_FUNC = 13
CERT_STORE_PROV_FIND_CERT_FUNC = 14
CERT_STORE_PROV_FREE_FIND_CERT_FUNC = 15
CERT_STORE_PROV_GET_CERT_PROPERTY_FUNC = 16
CERT_STORE_PROV_FIND_CRL_FUNC = 17
CERT_STORE_PROV_FREE_FIND_CRL_FUNC = 18
CERT_STORE_PROV_GET_CRL_PROPERTY_FUNC = 19
CERT_STORE_PROV_FIND_CTL_FUNC = 20
CERT_STORE_PROV_FREE_FIND_CTL_FUNC = 21
CERT_STORE_PROV_GET_CTL_PROPERTY_FUNC = 22
CERT_STORE_PROV_WRITE_ADD_FLAG = 0x1
CERT_STORE_SAVE_AS_STORE = 1
CERT_STORE_SAVE_AS_PKCS7 = 2
CERT_STORE_SAVE_TO_FILE = 1
CERT_STORE_SAVE_TO_MEMORY = 2
CERT_STORE_SAVE_TO_FILENAME_A = 3
CERT_STORE_SAVE_TO_FILENAME_W = 4
CERT_STORE_SAVE_TO_FILENAME = CERT_STORE_SAVE_TO_FILENAME_W
CERT_CLOSE_STORE_FORCE_FLAG = 0x00000001
CERT_CLOSE_STORE_CHECK_FLAG = 0x00000002
CERT_COMPARE_MASK = 0xFFFF
CERT_COMPARE_SHIFT = 16
CERT_COMPARE_ANY = 0
CERT_COMPARE_SHA1_HASH = 1
CERT_COMPARE_NAME = 2
CERT_COMPARE_ATTR = 3
CERT_COMPARE_MD5_HASH = 4
CERT_COMPARE_PROPERTY = 5
CERT_COMPARE_PUBLIC_KEY = 6
CERT_COMPARE_HASH = CERT_COMPARE_SHA1_HASH
CERT_COMPARE_NAME_STR_A = 7
CERT_COMPARE_NAME_STR_W = 8
CERT_COMPARE_KEY_SPEC = 9
CERT_COMPARE_ENHKEY_USAGE = 10
CERT_COMPARE_CTL_USAGE = CERT_COMPARE_ENHKEY_USAGE
CERT_COMPARE_SUBJECT_CERT = 11
CERT_COMPARE_ISSUER_OF = 12
CERT_COMPARE_EXISTING = 13
CERT_COMPARE_SIGNATURE_HASH = 14
CERT_COMPARE_KEY_IDENTIFIER = 15
CERT_COMPARE_CERT_ID = 16
CERT_COMPARE_CROSS_CERT_DIST_POINTS = 17
CERT_COMPARE_PUBKEY_MD5_HASH = 18
CERT_FIND_ANY = CERT_COMPARE_ANY << CERT_COMPARE_SHIFT
CERT_FIND_SHA1_HASH = CERT_COMPARE_SHA1_HASH << CERT_COMPARE_SHIFT
CERT_FIND_MD5_HASH = CERT_COMPARE_MD5_HASH << CERT_COMPARE_SHIFT
CERT_FIND_SIGNATURE_HASH = CERT_COMPARE_SIGNATURE_HASH << CERT_COMPARE_SHIFT
CERT_FIND_KEY_IDENTIFIER = CERT_COMPARE_KEY_IDENTIFIER << CERT_COMPARE_SHIFT
CERT_FIND_HASH = CERT_FIND_SHA1_HASH
CERT_FIND_PROPERTY = CERT_COMPARE_PROPERTY << CERT_COMPARE_SHIFT
CERT_FIND_PUBLIC_KEY = CERT_COMPARE_PUBLIC_KEY << CERT_COMPARE_SHIFT
CERT_FIND_SUBJECT_NAME = (
    CERT_COMPARE_NAME << CERT_COMPARE_SHIFT | CERT_INFO_SUBJECT_FLAG
)
CERT_FIND_SUBJECT_ATTR = (
    CERT_COMPARE_ATTR << CERT_COMPARE_SHIFT | CERT_INFO_SUBJECT_FLAG
)
CERT_FIND_ISSUER_NAME = CERT_COMPARE_NAME << CERT_COMPARE_SHIFT | CERT_INFO_ISSUER_FLAG
CERT_FIND_ISSUER_ATTR = CERT_COMPARE_ATTR << CERT_COMPARE_SHIFT | CERT_INFO_ISSUER_FLAG
CERT_FIND_SUBJECT_STR_A = (
    CERT_COMPARE_NAME_STR_A << CERT_COMPARE_SHIFT | CERT_INFO_SUBJECT_FLAG
)
CERT_FIND_SUBJECT_STR_W = (
    CERT_COMPARE_NAME_STR_W << CERT_COMPARE_SHIFT | CERT_INFO_SUBJECT_FLAG
)
CERT_FIND_SUBJECT_STR = CERT_FIND_SUBJECT_STR_W
CERT_FIND_ISSUER_STR_A = (
    CERT_COMPARE_NAME_STR_A << CERT_COMPARE_SHIFT | CERT_INFO_ISSUER_FLAG
)
CERT_FIND_ISSUER_STR_W = (
    CERT_COMPARE_NAME_STR_W << CERT_COMPARE_SHIFT | CERT_INFO_ISSUER_FLAG
)
CERT_FIND_ISSUER_STR = CERT_FIND_ISSUER_STR_W
CERT_FIND_KEY_SPEC = CERT_COMPARE_KEY_SPEC << CERT_COMPARE_SHIFT
CERT_FIND_ENHKEY_USAGE = CERT_COMPARE_ENHKEY_USAGE << CERT_COMPARE_SHIFT
CERT_FIND_CTL_USAGE = CERT_FIND_ENHKEY_USAGE
CERT_FIND_SUBJECT_CERT = CERT_COMPARE_SUBJECT_CERT << CERT_COMPARE_SHIFT
CERT_FIND_ISSUER_OF = CERT_COMPARE_ISSUER_OF << CERT_COMPARE_SHIFT
CERT_FIND_EXISTING = CERT_COMPARE_EXISTING << CERT_COMPARE_SHIFT
CERT_FIND_CERT_ID = CERT_COMPARE_CERT_ID << CERT_COMPARE_SHIFT
CERT_FIND_CROSS_CERT_DIST_POINTS = (
    CERT_COMPARE_CROSS_CERT_DIST_POINTS << CERT_COMPARE_SHIFT
)
CERT_FIND_PUBKEY_MD5_HASH = CERT_COMPARE_PUBKEY_MD5_HASH << CERT_COMPARE_SHIFT
CERT_FIND_OPTIONAL_ENHKEY_USAGE_FLAG = 0x1
CERT_FIND_EXT_ONLY_ENHKEY_USAGE_FLAG = 0x2
CERT_FIND_PROP_ONLY_ENHKEY_USAGE_FLAG = 0x4
CERT_FIND_NO_ENHKEY_USAGE_FLAG = 0x8
CERT_FIND_OR_ENHKEY_USAGE_FLAG = 0x10
CERT_FIND_VALID_ENHKEY_USAGE_FLAG = 0x20
CERT_FIND_OPTIONAL_CTL_USAGE_FLAG = CERT_FIND_OPTIONAL_ENHKEY_USAGE_FLAG
CERT_FIND_EXT_ONLY_CTL_USAGE_FLAG = CERT_FIND_EXT_ONLY_ENHKEY_USAGE_FLAG
CERT_FIND_PROP_ONLY_CTL_USAGE_FLAG = CERT_FIND_PROP_ONLY_ENHKEY_USAGE_FLAG
CERT_FIND_NO_CTL_USAGE_FLAG = CERT_FIND_NO_ENHKEY_USAGE_FLAG
CERT_FIND_OR_CTL_USAGE_FLAG = CERT_FIND_OR_ENHKEY_USAGE_FLAG
CERT_FIND_VALID_CTL_USAGE_FLAG = CERT_FIND_VALID_ENHKEY_USAGE_FLAG
CERT_SET_PROPERTY_IGNORE_PERSIST_ERROR_FLAG = -**********
CERT_SET_PROPERTY_INHIBIT_PERSIST_FLAG = 0x40000000
CTL_ENTRY_FROM_PROP_CHAIN_FLAG = 0x1
CRL_FIND_ANY = 0
CRL_FIND_ISSUED_BY = 1
CRL_FIND_EXISTING = 2
CRL_FIND_ISSUED_FOR = 3
CRL_FIND_ISSUED_BY_AKI_FLAG = 0x1
CRL_FIND_ISSUED_BY_SIGNATURE_FLAG = 0x2
CRL_FIND_ISSUED_BY_DELTA_FLAG = 0x4
CRL_FIND_ISSUED_BY_BASE_FLAG = 0x8
CERT_STORE_ADD_NEW = 1
CERT_STORE_ADD_USE_EXISTING = 2
CERT_STORE_ADD_REPLACE_EXISTING = 3
CERT_STORE_ADD_ALWAYS = 4
CERT_STORE_ADD_REPLACE_EXISTING_INHERIT_PROPERTIES = 5
CERT_STORE_ADD_NEWER = 6
CERT_STORE_ADD_NEWER_INHERIT_PROPERTIES = 7
CERT_STORE_CERTIFICATE_CONTEXT = 1
CERT_STORE_CRL_CONTEXT = 2
CERT_STORE_CTL_CONTEXT = 3

CERT_STORE_ALL_CONTEXT_FLAG = -1
CERT_STORE_CERTIFICATE_CONTEXT_FLAG = 1 << CERT_STORE_CERTIFICATE_CONTEXT
CERT_STORE_CRL_CONTEXT_FLAG = 1 << CERT_STORE_CRL_CONTEXT
CERT_STORE_CTL_CONTEXT_FLAG = 1 << CERT_STORE_CTL_CONTEXT
CTL_ANY_SUBJECT_TYPE = 1
CTL_CERT_SUBJECT_TYPE = 2
CTL_FIND_ANY = 0
CTL_FIND_SHA1_HASH = 1
CTL_FIND_MD5_HASH = 2
CTL_FIND_USAGE = 3
CTL_FIND_SUBJECT = 4
CTL_FIND_EXISTING = 5
CTL_FIND_NO_LIST_ID_CBDATA = -1
CTL_FIND_SAME_USAGE_FLAG = 0x1
CERT_STORE_CTRL_RESYNC = 1
CERT_STORE_CTRL_NOTIFY_CHANGE = 2
CERT_STORE_CTRL_COMMIT = 3
CERT_STORE_CTRL_AUTO_RESYNC = 4
CERT_STORE_CTRL_CANCEL_NOTIFY = 5
CERT_STORE_CTRL_INHIBIT_DUPLICATE_HANDLE_FLAG = 0x1
CERT_STORE_CTRL_COMMIT_FORCE_FLAG = 0x1
CERT_STORE_CTRL_COMMIT_CLEAR_FLAG = 0x2
CERT_STORE_LOCALIZED_NAME_PROP_ID = 0x1000
CERT_CREATE_CONTEXT_NOCOPY_FLAG = 0x1
CERT_CREATE_CONTEXT_SORTED_FLAG = 0x2
CERT_CREATE_CONTEXT_NO_HCRYPTMSG_FLAG = 0x4
CERT_CREATE_CONTEXT_NO_ENTRY_FLAG = 0x8

CERT_PHYSICAL_STORE_ADD_ENABLE_FLAG = 0x1
CERT_PHYSICAL_STORE_OPEN_DISABLE_FLAG = 0x2
CERT_PHYSICAL_STORE_REMOTE_OPEN_DISABLE_FLAG = 0x4
CERT_PHYSICAL_STORE_INSERT_COMPUTER_NAME_ENABLE_FLAG = 0x8
CERT_PHYSICAL_STORE_PREDEFINED_ENUM_FLAG = 0x1

# Names of physical cert stores
CERT_PHYSICAL_STORE_DEFAULT_NAME = ".Default"
CERT_PHYSICAL_STORE_GROUP_POLICY_NAME = ".GroupPolicy"
CERT_PHYSICAL_STORE_LOCAL_MACHINE_NAME = ".LocalMachine"
CERT_PHYSICAL_STORE_DS_USER_CERTIFICATE_NAME = ".UserCertificate"
CERT_PHYSICAL_STORE_LOCAL_MACHINE_GROUP_POLICY_NAME = ".LocalMachineGroupPolicy"
CERT_PHYSICAL_STORE_ENTERPRISE_NAME = ".Enterprise"
CERT_PHYSICAL_STORE_AUTH_ROOT_NAME = ".AuthRoot"
CERT_PHYSICAL_STORE_SMART_CARD_NAME = ".SmartCard"

CRYPT_OID_OPEN_SYSTEM_STORE_PROV_FUNC = "CertDllOpenSystemStoreProv"
CRYPT_OID_REGISTER_SYSTEM_STORE_FUNC = "CertDllRegisterSystemStore"
CRYPT_OID_UNREGISTER_SYSTEM_STORE_FUNC = "CertDllUnregisterSystemStore"
CRYPT_OID_ENUM_SYSTEM_STORE_FUNC = "CertDllEnumSystemStore"
CRYPT_OID_REGISTER_PHYSICAL_STORE_FUNC = "CertDllRegisterPhysicalStore"
CRYPT_OID_UNREGISTER_PHYSICAL_STORE_FUNC = "CertDllUnregisterPhysicalStore"
CRYPT_OID_ENUM_PHYSICAL_STORE_FUNC = "CertDllEnumPhysicalStore"
CRYPT_OID_SYSTEM_STORE_LOCATION_VALUE_NAME = "SystemStoreLocation"

CMSG_TRUSTED_SIGNER_FLAG = 0x1
CMSG_SIGNER_ONLY_FLAG = 0x2
CMSG_USE_SIGNER_INDEX_FLAG = 0x4
CMSG_CMS_ENCAPSULATED_CTL_FLAG = 0x00008000
CMSG_ENCODE_SORTED_CTL_FLAG = 0x1
CMSG_ENCODE_HASHED_SUBJECT_IDENTIFIER_FLAG = 0x2
CERT_VERIFY_INHIBIT_CTL_UPDATE_FLAG = 0x1
CERT_VERIFY_TRUSTED_SIGNERS_FLAG = 0x2
CERT_VERIFY_NO_TIME_CHECK_FLAG = 0x4
CERT_VERIFY_ALLOW_MORE_USAGE_FLAG = 0x8
CERT_VERIFY_UPDATED_CTL_FLAG = 0x1
CERT_CONTEXT_REVOCATION_TYPE = 1
CERT_VERIFY_REV_CHAIN_FLAG = 0x00000001
CERT_VERIFY_CACHE_ONLY_BASED_REVOCATION = 0x00000002
CERT_VERIFY_REV_ACCUMULATIVE_TIMEOUT_FLAG = 0x00000004
CERT_UNICODE_IS_RDN_ATTRS_FLAG = 0x1
CERT_CASE_INSENSITIVE_IS_RDN_ATTRS_FLAG = 0x2
CRYPT_VERIFY_CERT_SIGN_SUBJECT_BLOB = 1
CRYPT_VERIFY_CERT_SIGN_SUBJECT_CERT = 2
CRYPT_VERIFY_CERT_SIGN_SUBJECT_CRL = 3
CRYPT_VERIFY_CERT_SIGN_ISSUER_PUBKEY = 1
CRYPT_VERIFY_CERT_SIGN_ISSUER_CERT = 2
CRYPT_VERIFY_CERT_SIGN_ISSUER_CHAIN = 3
CRYPT_VERIFY_CERT_SIGN_ISSUER_NULL = 4
CRYPT_DEFAULT_CONTEXT_AUTO_RELEASE_FLAG = 0x00000001
CRYPT_DEFAULT_CONTEXT_PROCESS_FLAG = 0x00000002
CRYPT_DEFAULT_CONTEXT_CERT_SIGN_OID = 1
CRYPT_DEFAULT_CONTEXT_MULTI_CERT_SIGN_OID = 2
CRYPT_OID_EXPORT_PUBLIC_KEY_INFO_FUNC = "CryptDllExportPublicKeyInfoEx"
CRYPT_OID_IMPORT_PUBLIC_KEY_INFO_FUNC = "CryptDllImportPublicKeyInfoEx"
CRYPT_ACQUIRE_CACHE_FLAG = 0x00000001
CRYPT_ACQUIRE_USE_PROV_INFO_FLAG = 0x00000002
CRYPT_ACQUIRE_COMPARE_KEY_FLAG = 0x00000004
CRYPT_ACQUIRE_SILENT_FLAG = 0x00000040
CRYPT_FIND_USER_KEYSET_FLAG = 0x00000001
CRYPT_FIND_MACHINE_KEYSET_FLAG = 0x00000002
CRYPT_FIND_SILENT_KEYSET_FLAG = 0x00000040
CRYPT_OID_IMPORT_PRIVATE_KEY_INFO_FUNC = "CryptDllImportPrivateKeyInfoEx"
CRYPT_OID_EXPORT_PRIVATE_KEY_INFO_FUNC = "CryptDllExportPrivateKeyInfoEx"
CRYPT_DELETE_KEYSET = CRYPT_DELETEKEYSET
CERT_SIMPLE_NAME_STR = 1
CERT_OID_NAME_STR = 2
CERT_X500_NAME_STR = 3
CERT_NAME_STR_SEMICOLON_FLAG = 0x40000000
CERT_NAME_STR_NO_PLUS_FLAG = 0x20000000
CERT_NAME_STR_NO_QUOTING_FLAG = 0x10000000
CERT_NAME_STR_CRLF_FLAG = 0x08000000
CERT_NAME_STR_COMMA_FLAG = 0x04000000
CERT_NAME_STR_REVERSE_FLAG = 0x02000000
CERT_NAME_STR_DISABLE_IE4_UTF8_FLAG = 0x00010000
CERT_NAME_STR_ENABLE_T61_UNICODE_FLAG = 0x00020000
CERT_NAME_STR_ENABLE_UTF8_UNICODE_FLAG = 0x00040000
CERT_NAME_EMAIL_TYPE = 1
CERT_NAME_RDN_TYPE = 2
CERT_NAME_ATTR_TYPE = 3
CERT_NAME_SIMPLE_DISPLAY_TYPE = 4
CERT_NAME_FRIENDLY_DISPLAY_TYPE = 5
CERT_NAME_DNS_TYPE = 6
CERT_NAME_URL_TYPE = 7
CERT_NAME_UPN_TYPE = 8
CERT_NAME_ISSUER_FLAG = 0x1
CERT_NAME_DISABLE_IE4_UTF8_FLAG = 0x00010000
CRYPT_MESSAGE_BARE_CONTENT_OUT_FLAG = 0x00000001
CRYPT_MESSAGE_ENCAPSULATED_CONTENT_OUT_FLAG = 0x00000002
CRYPT_MESSAGE_KEYID_SIGNER_FLAG = 0x00000004
CRYPT_MESSAGE_SILENT_KEYSET_FLAG = 0x00000040
CRYPT_MESSAGE_KEYID_RECIPIENT_FLAG = 0x4
CERT_QUERY_OBJECT_FILE = 0x00000001
CERT_QUERY_OBJECT_BLOB = 0x00000002
CERT_QUERY_CONTENT_CERT = 1
CERT_QUERY_CONTENT_CTL = 2
CERT_QUERY_CONTENT_CRL = 3
CERT_QUERY_CONTENT_SERIALIZED_STORE = 4
CERT_QUERY_CONTENT_SERIALIZED_CERT = 5
CERT_QUERY_CONTENT_SERIALIZED_CTL = 6
CERT_QUERY_CONTENT_SERIALIZED_CRL = 7
CERT_QUERY_CONTENT_PKCS7_SIGNED = 8
CERT_QUERY_CONTENT_PKCS7_UNSIGNED = 9
CERT_QUERY_CONTENT_PKCS7_SIGNED_EMBED = 10
CERT_QUERY_CONTENT_PKCS10 = 11
CERT_QUERY_CONTENT_PFX = 12
CERT_QUERY_CONTENT_CERT_PAIR = 13
CERT_QUERY_CONTENT_FLAG_CERT = 1 << CERT_QUERY_CONTENT_CERT
CERT_QUERY_CONTENT_FLAG_CTL = 1 << CERT_QUERY_CONTENT_CTL
CERT_QUERY_CONTENT_FLAG_CRL = 1 << CERT_QUERY_CONTENT_CRL
CERT_QUERY_CONTENT_FLAG_SERIALIZED_STORE = 1 << CERT_QUERY_CONTENT_SERIALIZED_STORE
CERT_QUERY_CONTENT_FLAG_SERIALIZED_CERT = 1 << CERT_QUERY_CONTENT_SERIALIZED_CERT
CERT_QUERY_CONTENT_FLAG_SERIALIZED_CTL = 1 << CERT_QUERY_CONTENT_SERIALIZED_CTL
CERT_QUERY_CONTENT_FLAG_SERIALIZED_CRL = 1 << CERT_QUERY_CONTENT_SERIALIZED_CRL
CERT_QUERY_CONTENT_FLAG_PKCS7_SIGNED = 1 << CERT_QUERY_CONTENT_PKCS7_SIGNED
CERT_QUERY_CONTENT_FLAG_PKCS7_UNSIGNED = 1 << CERT_QUERY_CONTENT_PKCS7_UNSIGNED
CERT_QUERY_CONTENT_FLAG_PKCS7_SIGNED_EMBED = 1 << CERT_QUERY_CONTENT_PKCS7_SIGNED_EMBED
CERT_QUERY_CONTENT_FLAG_PKCS10 = 1 << CERT_QUERY_CONTENT_PKCS10
CERT_QUERY_CONTENT_FLAG_PFX = 1 << CERT_QUERY_CONTENT_PFX
CERT_QUERY_CONTENT_FLAG_CERT_PAIR = 1 << CERT_QUERY_CONTENT_CERT_PAIR
CERT_QUERY_CONTENT_FLAG_ALL = (
    CERT_QUERY_CONTENT_FLAG_CERT
    | CERT_QUERY_CONTENT_FLAG_CTL
    | CERT_QUERY_CONTENT_FLAG_CRL
    | CERT_QUERY_CONTENT_FLAG_SERIALIZED_STORE
    | CERT_QUERY_CONTENT_FLAG_SERIALIZED_CERT
    | CERT_QUERY_CONTENT_FLAG_SERIALIZED_CTL
    | CERT_QUERY_CONTENT_FLAG_SERIALIZED_CRL
    | CERT_QUERY_CONTENT_FLAG_PKCS7_SIGNED
    | CERT_QUERY_CONTENT_FLAG_PKCS7_UNSIGNED
    | CERT_QUERY_CONTENT_FLAG_PKCS7_SIGNED_EMBED
    | CERT_QUERY_CONTENT_FLAG_PKCS10
    | CERT_QUERY_CONTENT_FLAG_PFX
    | CERT_QUERY_CONTENT_FLAG_CERT_PAIR
)
CERT_QUERY_FORMAT_BINARY = 1
CERT_QUERY_FORMAT_BASE64_ENCODED = 2
CERT_QUERY_FORMAT_ASN_ASCII_HEX_ENCODED = 3
CERT_QUERY_FORMAT_FLAG_BINARY = 1 << CERT_QUERY_FORMAT_BINARY
CERT_QUERY_FORMAT_FLAG_BASE64_ENCODED = 1 << CERT_QUERY_FORMAT_BASE64_ENCODED
CERT_QUERY_FORMAT_FLAG_ASN_ASCII_HEX_ENCODED = (
    1 << CERT_QUERY_FORMAT_ASN_ASCII_HEX_ENCODED
)
CERT_QUERY_FORMAT_FLAG_ALL = (
    CERT_QUERY_FORMAT_FLAG_BINARY
    | CERT_QUERY_FORMAT_FLAG_BASE64_ENCODED
    | CERT_QUERY_FORMAT_FLAG_ASN_ASCII_HEX_ENCODED
)

CREDENTIAL_OID_PASSWORD_CREDENTIALS_A = 1
CREDENTIAL_OID_PASSWORD_CREDENTIALS_W = 2
CREDENTIAL_OID_PASSWORD_CREDENTIALS = CREDENTIAL_OID_PASSWORD_CREDENTIALS_W

SCHEME_OID_RETRIEVE_ENCODED_OBJECT_FUNC = "SchemeDllRetrieveEncodedObject"
SCHEME_OID_RETRIEVE_ENCODED_OBJECTW_FUNC = "SchemeDllRetrieveEncodedObjectW"
CONTEXT_OID_CREATE_OBJECT_CONTEXT_FUNC = "ContextDllCreateObjectContext"
CONTEXT_OID_CERTIFICATE = 1
CONTEXT_OID_CRL = 2
CONTEXT_OID_CTL = 3
CONTEXT_OID_PKCS7 = 4
CONTEXT_OID_CAPI2_ANY = 5
CONTEXT_OID_OCSP_RESP = 6

CRYPT_RETRIEVE_MULTIPLE_OBJECTS = 0x00000001
CRYPT_CACHE_ONLY_RETRIEVAL = 0x00000002
CRYPT_WIRE_ONLY_RETRIEVAL = 0x00000004
CRYPT_DONT_CACHE_RESULT = 0x00000008
CRYPT_ASYNC_RETRIEVAL = 0x00000010
CRYPT_STICKY_CACHE_RETRIEVAL = 0x00001000
CRYPT_LDAP_SCOPE_BASE_ONLY_RETRIEVAL = 0x00002000
CRYPT_OFFLINE_CHECK_RETRIEVAL = 0x00004000
CRYPT_LDAP_INSERT_ENTRY_ATTRIBUTE = 0x00008000
CRYPT_LDAP_SIGN_RETRIEVAL = 0x00010000
CRYPT_NO_AUTH_RETRIEVAL = 0x00020000
CRYPT_LDAP_AREC_EXCLUSIVE_RETRIEVAL = 0x00040000
CRYPT_AIA_RETRIEVAL = 0x00080000
CRYPT_VERIFY_CONTEXT_SIGNATURE = 0x00000020
CRYPT_VERIFY_DATA_HASH = 0x00000040
CRYPT_KEEP_TIME_VALID = 0x00000080
CRYPT_DONT_VERIFY_SIGNATURE = 0x00000100
CRYPT_DONT_CHECK_TIME_VALIDITY = 0x00000200
CRYPT_CHECK_FRESHNESS_TIME_VALIDITY = 0x00000400
CRYPT_ACCUMULATIVE_TIMEOUT = 0x00000800
CRYPT_PARAM_ASYNC_RETRIEVAL_COMPLETION = 1
CRYPT_PARAM_CANCEL_ASYNC_RETRIEVAL = 2
CRYPT_GET_URL_FROM_PROPERTY = 0x00000001
CRYPT_GET_URL_FROM_EXTENSION = 0x00000002
CRYPT_GET_URL_FROM_UNAUTH_ATTRIBUTE = 0x00000004
CRYPT_GET_URL_FROM_AUTH_ATTRIBUTE = 0x00000008
URL_OID_GET_OBJECT_URL_FUNC = "UrlDllGetObjectUrl"
TIME_VALID_OID_GET_OBJECT_FUNC = "TimeValidDllGetObject"
TIME_VALID_OID_FLUSH_OBJECT_FUNC = "TimeValidDllFlushObject"

TIME_VALID_OID_GET_CTL = 1
TIME_VALID_OID_GET_CRL = 2
TIME_VALID_OID_GET_CRL_FROM_CERT = 3
TIME_VALID_OID_GET_FRESHEST_CRL_FROM_CERT = 4
TIME_VALID_OID_GET_FRESHEST_CRL_FROM_CRL = 5

TIME_VALID_OID_FLUSH_CTL = 1
TIME_VALID_OID_FLUSH_CRL = 2
TIME_VALID_OID_FLUSH_CRL_FROM_CERT = 3
TIME_VALID_OID_FLUSH_FRESHEST_CRL_FROM_CERT = 4
TIME_VALID_OID_FLUSH_FRESHEST_CRL_FROM_CRL = 5

CRYPTPROTECT_PROMPT_ON_UNPROTECT = 0x1
CRYPTPROTECT_PROMPT_ON_PROTECT = 0x2
CRYPTPROTECT_PROMPT_RESERVED = 0x04
CRYPTPROTECT_PROMPT_STRONG = 0x08
CRYPTPROTECT_PROMPT_REQUIRE_STRONG = 0x10
CRYPTPROTECT_UI_FORBIDDEN = 0x1
CRYPTPROTECT_LOCAL_MACHINE = 0x4
CRYPTPROTECT_CRED_SYNC = 0x8
CRYPTPROTECT_AUDIT = 0x10
CRYPTPROTECT_NO_RECOVERY = 0x20
CRYPTPROTECT_VERIFY_PROTECTION = 0x40
CRYPTPROTECT_CRED_REGENERATE = 0x80
CRYPTPROTECT_FIRST_RESERVED_FLAGVAL = 0x0FFFFFFF
CRYPTPROTECT_LAST_RESERVED_FLAGVAL = -1
CRYPTPROTECTMEMORY_BLOCK_SIZE = 16
CRYPTPROTECTMEMORY_SAME_PROCESS = 0x00
CRYPTPROTECTMEMORY_CROSS_PROCESS = 0x01
CRYPTPROTECTMEMORY_SAME_LOGON = 0x02
CERT_CREATE_SELFSIGN_NO_SIGN = 1
CERT_CREATE_SELFSIGN_NO_KEY_INFO = 2
CRYPT_KEYID_MACHINE_FLAG = 0x00000020
CRYPT_KEYID_ALLOC_FLAG = 0x00008000
CRYPT_KEYID_DELETE_FLAG = 0x00000010
CRYPT_KEYID_SET_NEW_FLAG = 0x00002000
CERT_CHAIN_MAX_AIA_URL_COUNT_IN_CERT_DEFAULT = 5
CERT_CHAIN_MAX_AIA_URL_RETRIEVAL_COUNT_PER_CHAIN_DEFAULT = 10
CERT_CHAIN_MAX_AIA_URL_RETRIEVAL_BYTE_COUNT_DEFAULT = 100000
CERT_CHAIN_MAX_AIA_URL_RETRIEVAL_CERT_COUNT_DEFAULT = 10
CERT_CHAIN_CACHE_END_CERT = 0x00000001
CERT_CHAIN_THREAD_STORE_SYNC = 0x00000002
CERT_CHAIN_CACHE_ONLY_URL_RETRIEVAL = 0x00000004
CERT_CHAIN_USE_LOCAL_MACHINE_STORE = 0x00000008
CERT_CHAIN_ENABLE_CACHE_AUTO_UPDATE = 0x00000010
CERT_CHAIN_ENABLE_SHARE_STORE = 0x00000020
CERT_TRUST_NO_ERROR = 0x00000000
CERT_TRUST_IS_NOT_TIME_VALID = 0x00000001
CERT_TRUST_IS_NOT_TIME_NESTED = 0x00000002
CERT_TRUST_IS_REVOKED = 0x00000004
CERT_TRUST_IS_NOT_SIGNATURE_VALID = 0x00000008
CERT_TRUST_IS_NOT_VALID_FOR_USAGE = 0x00000010
CERT_TRUST_IS_UNTRUSTED_ROOT = 0x00000020
CERT_TRUST_REVOCATION_STATUS_UNKNOWN = 0x00000040
CERT_TRUST_IS_CYCLIC = 0x00000080
CERT_TRUST_INVALID_EXTENSION = 0x00000100
CERT_TRUST_INVALID_POLICY_CONSTRAINTS = 0x00000200
CERT_TRUST_INVALID_BASIC_CONSTRAINTS = 0x00000400
CERT_TRUST_INVALID_NAME_CONSTRAINTS = 0x00000800
CERT_TRUST_HAS_NOT_SUPPORTED_NAME_CONSTRAINT = 0x00001000
CERT_TRUST_HAS_NOT_DEFINED_NAME_CONSTRAINT = 0x00002000
CERT_TRUST_HAS_NOT_PERMITTED_NAME_CONSTRAINT = 0x00004000
CERT_TRUST_HAS_EXCLUDED_NAME_CONSTRAINT = 0x00008000
CERT_TRUST_IS_OFFLINE_REVOCATION = 0x01000000
CERT_TRUST_NO_ISSUANCE_CHAIN_POLICY = 0x02000000
CERT_TRUST_IS_PARTIAL_CHAIN = 0x00010000
CERT_TRUST_CTL_IS_NOT_TIME_VALID = 0x00020000
CERT_TRUST_CTL_IS_NOT_SIGNATURE_VALID = 0x00040000
CERT_TRUST_CTL_IS_NOT_VALID_FOR_USAGE = 0x00080000
CERT_TRUST_HAS_EXACT_MATCH_ISSUER = 0x00000001
CERT_TRUST_HAS_KEY_MATCH_ISSUER = 0x00000002
CERT_TRUST_HAS_NAME_MATCH_ISSUER = 0x00000004
CERT_TRUST_IS_SELF_SIGNED = 0x00000008
CERT_TRUST_HAS_PREFERRED_ISSUER = 0x00000100
CERT_TRUST_HAS_ISSUANCE_CHAIN_POLICY = 0x00000200
CERT_TRUST_HAS_VALID_NAME_CONSTRAINTS = 0x00000400
CERT_TRUST_IS_COMPLEX_CHAIN = 0x00010000
USAGE_MATCH_TYPE_AND = 0x00000000
USAGE_MATCH_TYPE_OR = 0x00000001
CERT_CHAIN_REVOCATION_CHECK_END_CERT = 0x10000000
CERT_CHAIN_REVOCATION_CHECK_CHAIN = 0x20000000
CERT_CHAIN_REVOCATION_CHECK_CHAIN_EXCLUDE_ROOT = 0x40000000
CERT_CHAIN_REVOCATION_CHECK_CACHE_ONLY = -**********
CERT_CHAIN_REVOCATION_ACCUMULATIVE_TIMEOUT = 0x08000000
CERT_CHAIN_DISABLE_PASS1_QUALITY_FILTERING = 0x00000040
CERT_CHAIN_RETURN_LOWER_QUALITY_CONTEXTS = 0x00000080
CERT_CHAIN_DISABLE_AUTH_ROOT_AUTO_UPDATE = 0x00000100
CERT_CHAIN_TIMESTAMP_TIME = 0x00000200
REVOCATION_OID_CRL_REVOCATION = 1
CERT_CHAIN_FIND_BY_ISSUER = 1
CERT_CHAIN_FIND_BY_ISSUER_COMPARE_KEY_FLAG = 0x0001
CERT_CHAIN_FIND_BY_ISSUER_COMPLEX_CHAIN_FLAG = 0x0002
CERT_CHAIN_FIND_BY_ISSUER_CACHE_ONLY_URL_FLAG = 0x0004
CERT_CHAIN_FIND_BY_ISSUER_LOCAL_MACHINE_FLAG = 0x0008
CERT_CHAIN_FIND_BY_ISSUER_NO_KEY_FLAG = 0x4000
CERT_CHAIN_FIND_BY_ISSUER_CACHE_ONLY_FLAG = 0x8000
CERT_CHAIN_POLICY_IGNORE_NOT_TIME_VALID_FLAG = 0x00000001
CERT_CHAIN_POLICY_IGNORE_CTL_NOT_TIME_VALID_FLAG = 0x00000002
CERT_CHAIN_POLICY_IGNORE_NOT_TIME_NESTED_FLAG = 0x00000004
CERT_CHAIN_POLICY_IGNORE_INVALID_BASIC_CONSTRAINTS_FLAG = 0x00000008
CERT_CHAIN_POLICY_IGNORE_ALL_NOT_TIME_VALID_FLAGS = (
    CERT_CHAIN_POLICY_IGNORE_NOT_TIME_VALID_FLAG
    | CERT_CHAIN_POLICY_IGNORE_CTL_NOT_TIME_VALID_FLAG
    | CERT_CHAIN_POLICY_IGNORE_NOT_TIME_NESTED_FLAG
)
CERT_CHAIN_POLICY_ALLOW_UNKNOWN_CA_FLAG = 0x00000010
CERT_CHAIN_POLICY_IGNORE_WRONG_USAGE_FLAG = 0x00000020
CERT_CHAIN_POLICY_IGNORE_INVALID_NAME_FLAG = 0x00000040
CERT_CHAIN_POLICY_IGNORE_INVALID_POLICY_FLAG = 0x00000080
CERT_CHAIN_POLICY_IGNORE_END_REV_UNKNOWN_FLAG = 0x00000100
CERT_CHAIN_POLICY_IGNORE_CTL_SIGNER_REV_UNKNOWN_FLAG = 0x00000200
CERT_CHAIN_POLICY_IGNORE_CA_REV_UNKNOWN_FLAG = 0x00000400
CERT_CHAIN_POLICY_IGNORE_ROOT_REV_UNKNOWN_FLAG = 0x00000800
CERT_CHAIN_POLICY_IGNORE_ALL_REV_UNKNOWN_FLAGS = (
    CERT_CHAIN_POLICY_IGNORE_END_REV_UNKNOWN_FLAG
    | CERT_CHAIN_POLICY_IGNORE_CTL_SIGNER_REV_UNKNOWN_FLAG
    | CERT_CHAIN_POLICY_IGNORE_CA_REV_UNKNOWN_FLAG
    | CERT_CHAIN_POLICY_IGNORE_ROOT_REV_UNKNOWN_FLAG
)
CERT_CHAIN_POLICY_ALLOW_TESTROOT_FLAG = 0x00008000
CERT_CHAIN_POLICY_TRUST_TESTROOT_FLAG = 0x00004000
CRYPT_OID_VERIFY_CERTIFICATE_CHAIN_POLICY_FUNC = "CertDllVerifyCertificateChainPolicy"
AUTHTYPE_CLIENT = 1
AUTHTYPE_SERVER = 2
BASIC_CONSTRAINTS_CERT_CHAIN_POLICY_CA_FLAG = -**********
BASIC_CONSTRAINTS_CERT_CHAIN_POLICY_END_ENTITY_FLAG = 0x40000000
MICROSOFT_ROOT_CERT_CHAIN_POLICY_ENABLE_TEST_ROOT_FLAG = 0x00010000
CRYPT_STRING_BASE64HEADER = 0x00000000
CRYPT_STRING_BASE64 = 0x00000001
CRYPT_STRING_BINARY = 0x00000002
CRYPT_STRING_BASE64REQUESTHEADER = 0x00000003
CRYPT_STRING_HEX = 0x00000004
CRYPT_STRING_HEXASCII = 0x00000005
CRYPT_STRING_BASE64_ANY = 0x00000006
CRYPT_STRING_ANY = 0x00000007
CRYPT_STRING_HEX_ANY = 0x00000008
CRYPT_STRING_BASE64X509CRLHEADER = 0x00000009
CRYPT_STRING_HEXADDR = 0x0000000A
CRYPT_STRING_HEXASCIIADDR = 0x0000000B
CRYPT_STRING_NOCR = -**********
CRYPT_USER_KEYSET = 0x00001000
PKCS12_IMPORT_RESERVED_MASK = -65536
REPORT_NO_PRIVATE_KEY = 0x0001
REPORT_NOT_ABLE_TO_EXPORT_PRIVATE_KEY = 0x0002
EXPORT_PRIVATE_KEYS = 0x0004
PKCS12_EXPORT_RESERVED_MASK = -65536

# Certificate store provider types used with CertOpenStore
CERT_STORE_PROV_MSG = 1
CERT_STORE_PROV_MEMORY = 2
CERT_STORE_PROV_FILE = 3
CERT_STORE_PROV_REG = 4
CERT_STORE_PROV_PKCS7 = 5
CERT_STORE_PROV_SERIALIZED = 6
CERT_STORE_PROV_FILENAME = 8
CERT_STORE_PROV_SYSTEM = 10
CERT_STORE_PROV_COLLECTION = 11
CERT_STORE_PROV_SYSTEM_REGISTRY = 13
CERT_STORE_PROV_PHYSICAL = 14
CERT_STORE_PROV_SMART_CARD = 15
CERT_STORE_PROV_LDAP = 16

URL_OID_CERTIFICATE_ISSUER = 1
URL_OID_CERTIFICATE_CRL_DIST_POINT = 2
URL_OID_CTL_ISSUER = 3
URL_OID_CTL_NEXT_UPDATE = 4
URL_OID_CRL_ISSUER = 5
URL_OID_CERTIFICATE_FRESHEST_CRL = 6
URL_OID_CRL_FRESHEST_CRL = 7
URL_OID_CROSS_CERT_DIST_POINT = 8
URL_OID_CERTIFICATE_OCSP = 9
URL_OID_CERTIFICATE_OCSP_AND_CRL_DIST_POINT = 10
URL_OID_CERTIFICATE_CRL_DIST_POINT_AND_OCSP = 11
URL_OID_CROSS_CERT_SUBJECT_INFO_ACCESS = 12
URL_OID_CERTIFICATE_ONLY_OCSP = 13
