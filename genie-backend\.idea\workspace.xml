<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="31406ef4-73d0-4ee0-a4a8-f293e05513e0" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../genie-client/uv.lock" beforeDir="false" afterPath="$PROJECT_DIR$/../genie-client/uv.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../genie-tool/genie_tool/util/llm_util.py" beforeDir="false" afterPath="$PROJECT_DIR$/../genie-tool/genie_tool/util/llm_util.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../genie-tool/genie_tool/util/prompt_util.py" beforeDir="false" afterPath="$PROJECT_DIR$/../genie-tool/genie_tool/util/prompt_util.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\code\environment\maven_repo" />
        <option name="userSettingsFile" value="D:\code\environment\apache-maven-3.8.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="312xtLe2qAA3oOaoDkzXiNGbO34" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Unnamed.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "F:/tmp/joyagent-jdgenie/genie-backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Unnamed" type="Application" factoryName="Application" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.jd.genie.GenieApplication" />
      <module name="genie-backend" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="31406ef4-73d0-4ee0-a4a8-f293e05513e0" name="Changes" comment="" />
      <created>1727355445408</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727355445408</updated>
      <workItem from="1754736183267" duration="11521000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>